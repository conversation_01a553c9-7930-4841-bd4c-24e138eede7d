import {
  MessageSquare,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Euro,
  User,
  Home,
} from "lucide-react";
import { format } from "date-fns";
import { nl } from "date-fns/locale";
import { useEffect, useState } from "react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Job, JobOwner } from "./types";
import { supabase } from "@/integrations/supabase/client";

interface ResponseDetailsDialogProps {
  showDetails: boolean;
  setShowDetails: (show: boolean) => void;
  job: Job | null;
  jobOwner: JobOwner | null;
  handleViewConversation: () => void;
  getJobStatusBadge: (status: string) => JSX.Element;
}

export const ResponseDetailsDialog = ({
  showDetails,
  setShowDetails,
  job,
  jobOwner,
  handleViewConversation,
  getJobStatusBadge,
}: ResponseDetailsDialogProps) => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const getCurrentUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user) {
        setCurrentUserId(user.id);
      }
    };
    getCurrentUser();
  }, []);

  if (!job) return null;

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "d MMMM yyyy 'om' HH:mm", {
      locale: nl,
    });
  };

  // Check if the current user is either the job owner or the accepted vakman
  const isAccepted =
    job.status === "accepted" || job.status === "in_behandeling";
  const isJobOwner = currentUserId === job.user_id;
  const isAcceptedVakman = currentUserId === job.accepted_vakman_id;
  const showConversationButton = isAccepted && (isJobOwner || isAcceptedVakman);

  return (
    <Dialog open={showDetails} onOpenChange={setShowDetails}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {job.title}
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[70vh]">
          <div className="space-y-6 pr-4">
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {job.category && (
                  <Badge variant="secondary">{job.category}</Badge>
                )}
                {getJobStatusBadge(job.status)}
              </div>
            </div>

            {isAccepted && jobOwner && (
              <div className="bg-muted p-4 rounded-lg space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Opdrachtgever
                </h3>
                <div className="space-y-2">
                  <p className="text-lg">
                    {jobOwner.first_name} {jobOwner.last_name}
                  </p>
                  {jobOwner.email && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Mail className="h-4 w-4" />
                      <a
                        href={`mailto:${jobOwner.email}`}
                        className="hover:underline"
                      >
                        {jobOwner.email}
                      </a>
                    </div>
                  )}
                  {jobOwner.phone_number && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Phone className="h-4 w-4" />
                      <a
                        href={`tel:${jobOwner.phone_number}`}
                        className="hover:underline"
                      >
                        {jobOwner.phone_number}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="bg-muted p-4 rounded-lg space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <Home className="h-4 w-4" />
                Locatie
              </h3>
              <div className="grid gap-2">
                {isAccepted ? (
                  <>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>Postcode: {job.postal_code}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4" />
                      <span>
                        Huisnummer: {job.house_number}
                        {job.house_number_addition &&
                          ` ${job.house_number_addition}`}
                      </span>
                    </div>
                    {job.owner?.street_address && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>Straat: {job.owner.street_address}</span>
                      </div>
                    )}
                  </>
                ) : (
                  <p className="text-muted-foreground">
                    Adresgegevens worden zichtbaar na acceptatie
                  </p>
                )}
              </div>
            </div>

            <div className="bg-muted p-4 rounded-lg space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Details
              </h3>
              <div className="grid gap-2">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <span>Geplaatst op {formatDate(job.created_at)}</span>
                </div>
                {job.budget && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Euro className="h-4 w-4" />
                    <span>Budget: €{job.budget.toFixed(2)}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold">Beschrijving</h3>
              <p className="text-muted-foreground whitespace-pre-wrap">
                {job.description}
              </p>
            </div>

            {showConversationButton && (
              <Button
                onClick={handleViewConversation}
                className="w-full font-medium shadow-sm hover:shadow-md transition-shadow bg-primary hover:bg-primary/90"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Naar conversatie
              </Button>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
