/**
 * @description This component renders a comprehensive and SEO-optimized detail page for dormer window installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout, perfectly mirroring the structure of other top-tier service pages for consistency. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for dormer installations.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Building2,
  CheckSquare,
  ArrowRight,
  Sun,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
} from "lucide-react";

const Service_DakkapelPlaatsingPage = () => {
  usePageTitle("Dakkapel Plaatsen | Klusgebied - Meer Ruimte & Licht");
  const navigate = useNavigate();

  const dakkapelServices = [
    {
      icon: CheckSquare,
      title: "Kunststof Dakkapellen",
      description:
        "Onderhoudsarme en duurzame dakkapellen met uitstekende isolatie.",
      points: [
        "Onderhoudsarm en zeer duurzaam.",
        "Uitstekende thermische en akoestische isolatie.",
        "Verkrijgbaar in vele kleuren en structuren (zoals houtlook).",
        "Een investering waar u jarenlang plezier van heeft.",
      ],
    },
    {
      icon: Building2,
      title: "Houten Dakkapellen",
      description:
        "Authentieke en op maat gemaakte houten dakkapellen voor een klassieke uitstraling.",
      points: [
        "Geeft uw woning een warme en authentieke uitstraling.",
        "Volledig op maat gemaakt, passend bij de stijl van uw huis.",
        "Makkelijk te bewerken en te schilderen in elke gewenste kleur.",
        "Natuurlijk en ademend materiaal.",
      ],
    },
    {
      icon: Sun,
      title: "Prefab Dakkapellen",
      description:
        "Snelle plaatsing binnen één dag dankzij prefabricage in de fabriek.",
      points: [
        "Razendsnel genieten van meer ruimte en licht.",
        "In de werkplaats volledig voorbereid.",
        "Met een kraan op uw dak geplaatst, vaak binnen 1 dag.",
        "Minimale overlast tijdens de bouw.",
      ],
    },
    {
      icon: CheckCircle,
      title: "Volledige Afwerking",
      description:
        "Inclusief binnenafwerking zoals isolatie, wanden en vensterbanken.",
      points: [
        "Aanbrengen van isolatie en plaatsen van binnenwanden.",
        "Installeren van vensterbanken en aftimmeren.",
        "Schilderklaar of volledig afgewerkt opgeleverd.",
        "Uw nieuwe kamer is direct klaar voor gebruik.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Sun className="w-8 h-8 text-white" />,
      title: "Meer Ruimte & Licht",
      description:
        "Creëer een volwaardige extra kamer en geniet van veel meer daglicht op zolder.",
    },
    {
      icon: <Building2 className="w-8 h-8 text-white" />,
      title: "Waardevermeerdering",
      description:
        "Een dakkapel is een slimme investering die de waarde van uw huis aanzienlijk verhoogt.",
    },
    {
      icon: <CheckSquare className="w-8 h-8 text-white" />,
      title: "Plaatsing in 1 Dag",
      description:
        "Dankzij efficiënte prefabricage is de dakkapel vaak al binnen één dag geplaatst.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een dakkapel?",
      answer:
        "De kosten voor een standaard kunststof dakkapel van 2,5 meter breed beginnen rond de €4.500. De prijs is afhankelijk van de afmetingen, materialen en extra opties.",
    },
    {
      question: "Heb ik een vergunning nodig voor een dakkapel?",
      answer:
        "Voor een dakkapel aan de achterkant van de woning is vaak geen vergunning nodig. Aan de voorkant meestal wel. Wij kunnen u helpen met de vergunningsaanvraag.",
    },
    {
      question: "Hoe lang duurt het plaatsen van een dakkapel?",
      answer:
        "Een prefab dakkapel wordt meestal binnen één dag geplaatst. De binnenafwerking kan nog enkele dagen extra in beslag nemen.",
    },
    {
      question: "Welk materiaal is het beste voor een dakkapel?",
      answer:
        "Kunststof is het populairst vanwege de duurzaamheid en het weinige onderhoud. Hout biedt een authentieke uitstraling maar vereist meer onderhoud. Wij adviseren u graag.",
    },
  ];

  const reviews = [
    {
      name: "Jeroen en Anja",
      location: "Amersfoort",
      rating: 5,
      quote:
        "De dakkapel werd in één dag geplaatst, precies zoals beloofd. Wat een ruimte!",
      highlighted: false,
    },
    {
      name: "Simone L.",
      location: "Leiden",
      rating: 5,
      quote:
        "Vanaf het eerste contact tot de oplevering perfect geregeld. Onze zolder is nu de mooiste kamer van het huis.",
      highlighted: true,
    },
    {
      name: "Familie El Amrani",
      location: "Ede",
      rating: 4,
      quote:
        "Professioneel team, duidelijke communicatie en een prachtig eindresultaat. Zeer tevreden over Klusgebied.",
      highlighted: false,
    },
    {
      name: "Mark de Groot",
      location: "Zwolle",
      rating: 5,
      quote:
        "De extra ruimte en het licht zijn fantastisch. De waarde van ons huis is direct gestegen!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1635106768840-ae433112fc5a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxkb3JtZXIlMjB3aW5kb3clMkMlMjBtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBob21lJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDIzNjF8MA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1600585153490-76fb20a32601?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Beschrijf uw wensen",
      description:
        "Geef de afmetingen, het gewenste materiaal (kunststof, hout) en eventuele extra's zoals rolluiken op.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes op maat",
      description:
        "Specialisten in dakkapellen sturen u vrijblijvende offertes, vaak inclusief een schets of plan.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & plan de plaatsing",
      description:
        "Vergelijk de specialisten, kies de beste deal en plan de plaatsing. Geniet binnen no-time van uw nieuwe ruimte!",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Dakkapel Specialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "dakkapel",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Dakkapel Plaatsen | Klusgebied - Meer Ruimte & Licht</title>
        <meta
          name="description"
          content="Transformeer uw zolder in een volwaardige leefruimte. Een dakkapel wordt vaak binnen één dag geplaatst en verhoogt de waarde van uw woning aanzienlijk."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Dakkapel Plaatsing",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.9",
              reviewCount: "187",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "4500",
                maxPrice: "10000",
                valueAddedTaxIncluded: true,
                description:
                  "Prijs is afhankelijk van afmetingen, materiaal en opties.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <Building2 className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      Dakkapel Plaatsing
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Dakkapel Plaatsen?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-cyan-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Meer Ruimte & Licht
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Transformeer uw zolder in een volwaardige leefruimte. Een
                    dakkapel wordt vaak binnen één dag geplaatst en verhoogt de
                    waarde van uw woning.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/dakkapel-plaatsen-vervangen")
                      }
                      className="group inline-flex items-center justify-center bg-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw specialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 187 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1635106768840-ae433112fc5a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxkb3JtZXIlMjB3aW5kb3clMkMlMjBtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBob21lJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDIzNjF8MA&ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Moderne zolderkamer met een grote, lichte dakkapel"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Uw Dakkapel in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte specialist voor uw
                dakkapel.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Dakkapel Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet aanbod voor een duurzame en stijlvolle uitbreiding
                van uw woning.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {dakkapelServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een dakkapel via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor erkende
                vakmannen. De kosten zijn afhankelijk van afmetingen, materiaal
                en extra opties. Vraag een exacte offerte aan voor uw situatie.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Kunststof Dakkapel:{" "}
                    <strong className="text-slate-900">vanaf €4.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Houten Dakkapel:{" "}
                    <strong className="text-slate-900">vanaf €5.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Dakkapel met Nokverhoging:{" "}
                    <strong className="text-slate-900">vanaf €8.000</strong>
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/dakkapel-plaatsen-vervangen")
                }
                className="bg-blue-600 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een dakkapel lieten plaatsen
                via Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-blue-600 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-blue-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-blue-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-blue-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-blue-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Voordelen van een Dakkapel
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Een slimme investering in uw wooncomfort en de waarde van uw
                huis.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-blue-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-blue-600 to-cyan-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw zolder te transformeren?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvende offerte aan en ontdek hoe een dakkapel uw
              huis kan verrijken.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/dakkapel-plaatsen-vervangen")
              }
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Ontvang een gratis offerte
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() =>
            navigate("/plaats-een-klus/dakkapel-plaatsen-vervangen")
          }
          className="w-full group inline-flex items-center justify-center bg-blue-600 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_DakkapelPlaatsingPage;
