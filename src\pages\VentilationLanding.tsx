import { useState } from "react";
import {
  Phone,
  FileText,
  MapPin,
  Calendar,
  CheckCircle,
  Info,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";

import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const VentilationLanding = () => {
  const [contactModalOpen, setContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      rating: 5,
      shortText:
        "Helemaal top deze service! Die monteur, echt een vakman en nog aardig ook! Alles spic en span achtergelaten. Ze waren...",
      fullText:
        "Helemaal top deze service! Die monteur, echt een vakman en nog aardig ook! Alles spic en span achtergelaten. Ze waren precies op tijd en hebben alles perfect uitgevoerd. De ventilatie werkt nu veel beter dan voorheen. Echt een aanrader!",
      verified: true,
    },
    {
      id: 2,
      name: "Joost",
      rating: 5,
      shortText:
        "Top geregeld allemaal! Die nieuwe ventilatiebox zat er zo in. De monteur was precies op tijd, gaf goed advies en 't ziet er allemaal...",
      fullText:
        "Top geregeld allemaal! Die nieuwe ventilatiebox zat er zo in. De monteur was precies op tijd, gaf goed advies en 't ziet er allemaal netjes uit. Heel professioneel werk geleverd en de prijs was ook heel redelijk.",
      verified: true,
    },
    {
      id: 3,
      name: "Lotte",
      rating: 5,
      shortText:
        "De monteur had alle tijd voor mijn vragen en de installatie was zo gepiept. Heel fijn dat ze alles netjes achterlieten en ik kreeg...",
      fullText:
        "De monteur had alle tijd voor mijn vragen en de installatie was zo gepiept. Heel fijn dat ze alles netjes achterlieten en ik kreeg uitgebreide uitleg over het onderhoud. Zeer tevreden met de service!",
      verified: true,
    },
    {
      id: 4,
      name: "Bram",
      rating: 5,
      shortText:
        "Prima vent, die monteur. Stond stipt op tijd voor de deur en was erg vriendelijk. Hij heeft alles rustig uitgelegd en al mijn vragen...",
      fullText:
        "Prima vent, die monteur. Stond stipt op tijd voor de deur en was erg vriendelijk. Hij heeft alles rustig uitgelegd en al mijn vragen beantwoord. Het werk was snel klaar en de ventilatie doet het perfect.",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={contactModalOpen}
        setIsOpen={setContactModalOpen}
        jobType="ventilatie-vervangen-of-reinigen"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-2xl font-bold tracking-wide">Klusgebied</h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md text-lg tracking-wide"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md text-lg tracking-wide"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-xl mb-2 tracking-wide leading-relaxed">
            Mechanische ventilatie vuil, defect of verouderd?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Wij inspecteren, reinigen en installeren ventilatiesystemen voor een
            gezond en fris binnenklimaat
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-xl tracking-wide leading-loose">
            We zijn actief in heel Nederland en kunnen vaak al dezelfde dag
            langskomen. Je krijgt 100% garantie en de zekerheid van
            gecertificeerde vakmensen.
          </p>
          <button
            onClick={() => setContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Actief in Amsterdam, Almere en Utrecht
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Vaak dezelfde dag beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Afspraak op dezelfde dag mogelijk
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="/images/ventilation_landing/main.webp"
            className="w-full h-full object-cover"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-xl tracking-wide leading-relaxed">
              Onze monteurs hebben ervaring met alle bekende ventilatiemerken.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="/images/ventilation_landing/before-a.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="/images/ventilation_landing/after-a.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Vuile, defecte of verouderde mechanische ventilatie?
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Een slecht functionerend ventilatiesysteem kan de luchtkwaliteit
                in huis verslechteren. Ophoping van stof, bacteriën en andere
                verontreinigingen kan je gezondheid negatief beïnvloeden.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Met een grondige onderhoudsbeurt werkt je systeem weer zoals het
                hoort. We reinigen de luchtkanalen, ventilatiebox en ventielen,
                en testen en stellen de afzuigcapaciteit af.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Een goed onderhouden ventilatiesysteem zorgt voor een gezonde
                luchtvochtigheid en helpt gezondheidsklachten zoals hoofdpijn en
                allergieën te voorkomen.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Mechanische Ventilatieservice
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-xl tracking-wide leading-loose">
            Wij lossen alle ventilatieproblemen op, van noodgevallen tot
            regulier onderhoud. Onze diensten zijn beschikbaar in de Randstad en
            daarbuiten.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <img
                  src="/images/ventilation_landing/cleaning.webp"
                  className="w-[140px] h-[140px]"
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Mechanische ventilatie reinigen
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-loose">
                Verwijder stof, vuil en bacteriën uit je ventilatiesysteem voor
                een gezondere luchtkwaliteit. Wij reinigen luchtkanalen,
                ventilatieboxen en ventielen grondig en vakkundig.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <img
                  src="/images/ventilation_landing/replacing.webp"
                  className="w-[140px] h-[140px]"
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Mechanische ventilatie vervangen
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-loose">
                Is je ventilatiesysteem verouderd of defect? Wij vervangen het
                met een energiezuinige en efficiënte oplossing die zorgt voor
                frisse lucht en optimaal wooncomfort.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <img
                  src="/images/ventilation_landing/installing.webp"
                  className="w-[140px] h-[140px]"
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Mechanische ventilatie installeren
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-loose">
                Geen ventilatiesysteem in huis of bedrijfspand? Wij installeren
                mechanische ventilatie op maat, afgestemd op jouw ruimte en
                ventilatiebehoeften.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <img
                  src="/images/ventilation_landing/inspect.webp"
                  className="w-[140px] h-[140px]"
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Mechanische ventilatie inspecteren
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-loose">
                Twijfel je of je ventilatiesysteem nog goed werkt? Wij voeren
                een uitgebreide inspectie uit, meten de afzuigcapaciteit en
                controleren op verstoppingen of defecten.
              </p>
            </div>

            {/* Service Card 5 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <img
                  src="/images/ventilation_landing/maintaining.webp"
                  className="w-[140px] h-[140px]"
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Mechanische ventilatie onderhouden
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-loose">
                Voorkom storingen en zorg ervoor dat je ventilatiesysteem
                efficiënt blijft werken. Wij bieden periodiek onderhoud om
                slijtage, stofophoping en prestatieverlies tegen te gaan.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="/images/ventilation_landing/result-1.webp"
                  className="w-[360px] h-[300px]"
                />
              </div>
              <p className="text-lg tracking-wide leading-loose">
                De ventilatieopening was bedekt met een dikke laag stof en vuil,
                maar is na reiniging weer volledig schoon voor een vrije
                luchtstroom.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="/images/ventilation_landing/result-2.webp"
                  className="w-[360px] h-[300px]"
                />
              </div>
              <p className="text-lg tracking-wide leading-loose">
                Een verstopt ventilatiekanaal vol stof en aanslag is grondig
                gereinigd, waardoor frisse lucht weer probleemloos kan
                circuleren.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="/images/ventilation_landing/result-3.webp"
                  className="w-[360px] h-[300px]"
                />
              </div>
              <p className="text-lg tracking-wide leading-loose">
                Sterk vervuilde en dichtgeslibde ventilatiekanalen zijn ontdaan
                van ophopingen en werken weer optimaal voor een gezond
                binnenklimaat.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                24/7 service – Direct hulp bij jouw mechanische ventilatie
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Wij staan dag en nacht voor je klaar.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Of het nu gaat om een storing, defect of dringend onderhoud, we
                lossen het snel op.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Neem contact met ons op en in veel gevallen kunnen we dezelfde
                dag nog langskomen. Zo heb je snel weer een gezond binnenklimaat
                zonder gedoe.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Onze eigen gecertificeerde experts werken met een persoonlijke
                en doeltreffende aanpak. Geen lange wachttijden, gewoon een
                snelle en efficiënte oplossing.
              </p>
              <p className="mb-6 text-lg tracking-wide leading-loose">
                Heb je direct hulp nodig? Neem contact met ons op.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="/images/ventilation_landing/24-7-service.webp"
                  className="w-[530px] h-[353px] object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="/images/ventilation_landing/about.webp"
                  className="w-[530px] h-[339px]"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Bij Klusgebied zorgen we ervoor dat je ventilatiesysteem altijd
                in topconditie is. Of het nu gaat om reinigen, vervangen of
                onderhoud, wij verbinden je met een lokaal gecertificeerde
                vakmensen die het vakkundig en snel oplossen.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Heb je last van een slecht werkend ventilatiesysteem of wil je
                een afspraak inplannen? Wij maken het eenvoudig en zorgen dat je
                snel wordt geholpen door onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="/images/ventilation_landing/before-b.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="/images/ventilation_landing/after-b.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-loose">
                    Toegewijde en deskundige specialisten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-loose">
                    Afspraak gegarandeerd binnen 3 dagen
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-loose">
                    Snelle en vakkundige klantenservice; elke vraag is welkom
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-loose">
                    Uitstekend beoordeeld door klanten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-loose">
                    Mogelijkheid om 24/7 een afspraak in te plannen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Klein onderhoud
                </h3>
                <p className="text-lg tracking-wide">Mechanische ventilatie</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Inspectie ventilatiebox
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen ventilatiebox
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen en afstellen ventielen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Voor & na foto's
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Uitleg & ventilatieadvies
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-lg text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €90,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-lg tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Groot onderhoud
                </h3>
                <p className="text-lg tracking-wide">Mechanische ventilatie</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Inspectie ventilatiebox
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen ventilatiebox
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen en afstellen ventielen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Voor & na foto's
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Uitleg & ventilatieadvies
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Afstellen systeem
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen ventilatiekanalen
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-lg text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €125,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-lg tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Installatie
                </h3>
                <p className="text-lg tracking-wide">Mechanische ventilatie</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Inspectie ventilatiebox
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen ventilatiebox
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen en afstellen ventielen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Voor & na foto's
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Uitleg & ventilatieadvies
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Afstellen systeem
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Reinigen ventilatiekanalen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Nieuwe ventilatiebox installeren
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Nieuwe ventielen plaatsen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-lg tracking-wide leading-relaxed">
                      Volledige systeemafstelling
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center text-lg tracking-wide leading-relaxed">
                      Vervangen luchtkanalen
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-lg text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €275,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-lg tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Is mechanische ventilatie echt noodzakelijk voor mijn gezondheid?"
              answer="Ja, mechanische ventilatie is essentieel voor een gezond binnenklimaat. Het zorgt voor de afvoer van vocht, CO2 en andere schadelijke stoffen, en de aanvoer van verse lucht. Een goed werkend ventilatiesysteem helpt bij het voorkomen van schimmel, vocht en gezondheidsproblemen zoals allergieën en luchtwegklachten."
            />
            <FAQItem
              question="Wat als mijn systeem te oud is om effectief te zijn?"
              answer="Oudere ventilatiesystemen kunnen inderdaad minder efficiënt werken. Bij Klusgebied kunnen we uw systeem beoordelen en adviseren over mogelijke verbeteringen of vervanging. Moderne systemen zijn energiezuiniger, stiller en effectiever in het verbeteren van de luchtkwaliteit."
            />
            <FAQItem
              question="Zijn er verborgen kosten verbonden aan de service?"
              answer="Nee, bij Klusgebied werken we met transparante prijzen. Voordat we beginnen, krijgt u een duidelijke offerte zonder verborgen kosten. We bespreken altijd vooraf wat er precies gedaan moet worden en wat de kosten zijn, zodat u niet voor verrassingen komt te staan."
            />
            <FAQItem
              question="Kan ik zelf onderhoud uitvoeren aan mijn ventilatiesysteem?"
              answer="Eenvoudig onderhoud zoals het schoonmaken van ventielen kunt u zelf doen. Voor grondige reiniging van kanalen, het afstellen van het systeem of het vervangen van onderdelen is professionele hulp nodig. Verkeerd onderhoud kan leiden tot verminderde prestaties of zelfs schade aan het systeem."
            />
            <FAQItem
              question="Hoe vaak moet mijn ventilatiesysteem onderhouden worden?"
              answer="Voor optimale prestaties adviseren wij om uw ventilatiesysteem minimaal eens per 2 jaar te laten onderhouden. Bij intensief gebruik of in omgevingen met veel stof of vervuiling kan jaarlijks onderhoud nodig zijn. Regelmatig onderhoud verlengt de levensduur van uw systeem en zorgt voor een gezond binnenklimaat."
            />
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor een optimaal binnenklimaat
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 text-xl tracking-wide leading-loose">
                Onze mechanische ventilatie experts zijn 24/7 bereikbaar en
                kunnen vaak dezelfde dag nog op de stoep staan. Klik op de
                onderstaande knop om contact op te nemen.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img src="/images/ventilation_landing/robert.webp" />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-lg italic tracking-wide leading-relaxed">
            Robert van Huizen – ventilatie-expert in Almere, Amsterdam en
            Utrecht, geselecteerd door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl font-bold tracking-wide">Klusgebied</h3>
              </div>
              <p className="mb-4 text-lg tracking-wide leading-loose">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4 tracking-wide">Contact</h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium text-lg tracking-wide">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="text-lg tracking-wide">Slotermeerlaan 58</p>
                </li>
                <li>
                  <p className="text-lg tracking-wide">1064 HC Amsterdam</p>
                </li>
                <li>
                  <p className="text-lg tracking-wide">KVK: 93475101</p>
                </li>
                <li>
                  <p className="text-lg tracking-wide"><EMAIL></p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-lg text-gray-400 tracking-wide">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1]">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default VentilationLanding;
