/**
 * @description This component renders a comprehensive and SEO-optimized detail page for tile specialist services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for tile specialists.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  ArrowRight,
  Puzzle,
  Droplets,
  Award,
  MapPin,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  Euro,
} from "lucide-react";

const Service_TegelvloerSpecialistPage = () => {
  usePageTitle("Tegelzetter Nodig? | Klusgebied - Vloer & Wand Tegelwerk");
  const navigate = useNavigate();

  const tilerServices = [
    {
      icon: Puzzle,
      title: "Keramische Tegels Leggen",
      description:
        "Duurzaam, slijtvast en onderhoudsvriendelijk. Ideaal voor elke ruimte.",
      points: [
        "Enorm aanbod in kleuren, formaten en designs.",
        "Perfect voor vloerverwarming door goede warmtegeleiding.",
        "Slijtvast en makkelijk schoon te houden.",
        "Wij zorgen voor een perfect vlakke en strakke vloer.",
      ],
    },
    {
      icon: Award,
      title: "Natuursteen Vloeren",
      description:
        "Luxe en unieke uitstraling met materialen als marmer of graniet.",
      points: [
        "Geeft een exclusieve en tijdloze uitstraling.",
        "Elke tegel is uniek, wat zorgt voor een levendige vloer.",
        "Duurzaam en verhoogt de waarde van uw woning.",
        "Specialistische kennis vereist voor leggen en onderhoud.",
      ],
    },
    {
      icon: Droplets,
      title: "Badkamer & Toilet Betegelen",
      description: "Waterdicht en hygiënisch tegelwerk voor wanden en vloeren.",
      points: [
        "Gegarandeerd waterdicht door vakkundige afwerking.",
        "Compleet tegelwerk: wanden, vloeren en nissen.",
        "Advies over de juiste tegels voor natte ruimtes.",
        "Strak voeg- en kitwerk voor een perfecte finish.",
      ],
    },
    {
      icon: Puzzle,
      title: "Tegelreparatie",
      description: "Reparatie van gebarsten, losse of beschadigde tegels.",
      points: [
        "Vervangen van losse of kapotte tegels.",
        "Herstellen van beschadigd voegwerk.",
        "Voorkomt verdere schade en lekkages.",
        "We proberen de originele uitstraling te behouden.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Puzzle className="w-8 h-8 text-white" />,
      title: "Vakmanschap & Precisie",
      description:
        "Millimeter-precies tegelwerk voor een naadloos en strak resultaat.",
    },
    {
      icon: <Droplets className="w-8 h-8 text-white" />,
      title: "100% Waterdicht",
      description:
        "Gegarandeerde waterdichte afwerking voor natte ruimtes zoals badkamers.",
    },
    {
      icon: <Award className="w-8 h-8 text-white" />,
      title: "Beste Materialen",
      description:
        "Wij werken met de beste lijmen en voegmiddelen voor een duurzaam resultaat.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een tegelzetter?",
      answer:
        "De kosten voor een tegelzetter variëren van €30 tot €50 per uur of €25 tot €40 per m², afhankelijk van de tegel en de complexiteit. Vraag een offerte aan voor een exacte prijs.",
    },
    {
      question: "Hoe lang duurt het betegelen van een badkamer?",
      answer:
        "Een gemiddelde badkamer duurt 3 tot 5 dagen om volledig te betegelen, inclusief voorbereiding en voegwerk.",
    },
    {
      question: "Welk type tegel is het beste voor vloerverwarming?",
      answer:
        "Keramische tegels zijn ideaal voor vloerverwarming omdat ze warmte uitstekend geleiden. Onze specialisten kunnen u hierover adviseren.",
    },
    {
      question: "Kunnen jullie ook oude tegels verwijderen?",
      answer:
        "Ja, wij bieden een complete service, inclusief het verwijderen en afvoeren van oude tegels en het voorbereiden van de ondergrond.",
    },
  ];

  const reviews = [
    {
      name: "Familie Verhoeven",
      location: "Den Bosch",
      rating: 5,
      quote:
        "Onze badkamer is een droom geworden. Het tegelwerk is zo ontzettend strak gedaan. Echte vakmannen!",
      highlighted: true,
    },
    {
      name: "Mark de Jong",
      location: "Tilburg",
      rating: 5,
      quote:
        "De keukenvloer met houtlook tegels is prachtig. Niet van echt hout te onderscheiden en super praktisch.",
      highlighted: false,
    },
    {
      name: "Linda Gerritsen",
      location: "Breda",
      rating: 5,
      quote:
        "Snel, vakkundig en netjes gewerkt. De tegelzetter dacht goed mee over de indeling van de tegels. Heel blij mee.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1695191388218-f6259600223f?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxiYXRocm9vbSUyMHRpbGVzJTJDJTIwbW9kZXJuJTIwZGVzaWduJTJDJTIwdGlsZSUyMHdvcmt8ZW58MHx8fHwxNzUxNzQwNzQ0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1563219125-1db796e20ff2?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxmbG9vciUyMHRpbGVzJTJDJTIwaW50ZXJpb3IlMjBkZXNpZ24lMkMlMjB0aWxlJTIwZmxvb3Jpbmd8ZW58MHx8fHwxNzUxNzQwNzQ0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1732552933763-38484166f4ad?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx0aWxlJTIwaW5zdGFsbGF0aW9uJTJDJTIwcHJvZmVzc2lvbmFsJTIwdGlsZXIlMkMlMjBob21lJTIwcmVub3ZhdGlvbnxlbnwwfHx8fDE3NTE3NDA3NDR8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf uw tegelklus. Welke ruimte? Welk type tegel? Voeg foto's toe.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde tegelzetters.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste vakman voor uw tegelwerk. Plan en start!",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Tegelzetters in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "tegelzetter",
    color: "blue",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Tegelzetter Nodig? | Klusgebied - Vloer & Wand Tegelwerk</title>
        <meta
          name="description"
          content="Vind een betrouwbare tegelzetter voor uw badkamer, keuken, toilet of vloer. Vakkundig en strak tegelwerk. Plaats uw klus gratis en ontvang offertes."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Tegelzetter",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.9",
              reviewCount: "158",
            },
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6">
                    <Puzzle className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      Tegelzetter
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Tegelzetter Nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-indigo-500 mt-2">
                      Strak & Waterdicht
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor vakkundig tegelwerk in uw badkamer, keuken of vloer.
                    Vind een ervaren tegelzetter voor een perfect en duurzaam
                    resultaat.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw tegelzetter
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele tegelzetter aan het werk in een badkamer"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Uw Tegelwerk in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte tegelzetter voor uw klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Tegel Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vakmanschap en precisie voor een perfecte tegelvloer of -wand.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {tilerServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een tegelzetter via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van het type tegel en de complexiteit
                van het legpatroon. U ontvangt altijd een duidelijke offerte
                vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Wandtegels:{" "}
                    <strong className="text-slate-900">€25–€40</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vloertegels:{" "}
                    <strong className="text-slate-900">€30–€50</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Uurtarief:{" "}
                    <strong className="text-slate-900">€35–€55</strong> per uur
                  </span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/plaats-een-klus")}
                className="bg-blue-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een tegelzetter vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-blue-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-blue-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-blue-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-blue-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een tegelzetter via Klusgebied en wees verzekerd van
                kwaliteit.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-blue-600 to-indigo-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor een nieuwe vloer of wand?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en ontvang snel offertes van de beste tegelzetters
              bij u in de buurt. Strak, waterdicht en duurzaam.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je tegel klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_TegelvloerSpecialistPage;
