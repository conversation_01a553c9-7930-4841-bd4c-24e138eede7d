/**
 * @description This component renders a comprehensive and SEO-optimized detail page for roofing services, now aligned with the new standardized service page structure. It features a dynamic hero section, a detailed breakdown of services, a transparent pricing block, a 'how it works' guide, customer testimonials, a project gallery, an FAQ section, and strong calls-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for roofing and structured for optimal user experience and SEO.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Home,
  ShieldCheck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  User<PERSON><PERSON>ck,
  CheckCircle,
  MapPin,
  Euro,
  Umbrella,
} from "lucide-react";

const Service_DakbedekkingPage = () => {
  usePageTitle("Dakbedekking Nodig? | Klusgebied - Waterdicht & Veilig");
  const navigate = useNavigate();

  const roofingServices = [
    {
      icon: Umbrella,
      title: "Bitumen Dakbedekking",
      description:
        "Duurzame en waterdichte dakbedekking voor platte en licht hellende daken.",
      points: [
        "Hoogwaardige, gemodificeerde bitumen.",
        "Naadloze, waterdichte afwerking met brander.",
        "Kosteneffectieve en betrouwbare keuze.",
        "Lange levensduur gegarandeerd.",
      ],
    },
    {
      icon: Home,
      title: "EPDM Dakbedekking",
      description:
        "Elastische, onderhoudsvriendelijke en zeer duurzame rubberen dakfolie.",
      points: [
        "Synthetisch rubber met levensduur tot 50 jaar.",
        "UV-bestendig en zeer elastisch.",
        "Naadloos gelegd uit één stuk, minimale kans op lekkages.",
        "Ideaal voor daken met zonnepanelen of groendak.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Dakpannen Leggen & Vervangen",
      description:
        "Voor hellende daken, van het vervangen van enkele pannen tot een compleet nieuw pannendak.",
      points: [
        "Vervangen van kapotte dakpannen.",
        "Compleet nieuwe pannendaken (keramisch en beton).",
        "Perfecte plaatsing inclusief panlatten, folie en nokvorsten.",
        "Professioneel advies over de beste dakpannen.",
      ],
    },
    {
      icon: Clock,
      title: "Dakinspectie & Onderhoud",
      description:
        "Periodieke controle en onderhoud om de levensduur van uw dak te verlengen.",
      points: [
        "Jaarlijkse inspectie om problemen te voorkomen.",
        "Controle op zwakke plekken en potentiële lekkages.",
        "Reinigen van dakgoten en klein onderhoud.",
        "Verleng de levensduur van uw dak aanzienlijk.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Umbrella className="w-8 h-8 text-white" />,
      title: "Gegarandeerd Waterdicht",
      description:
        "Professionele installatie voor een dak dat u beschermt tegen alle weersomstandigheden.",
    },
    {
      icon: <Home className="w-8 h-8 text-white" />,
      title: "Alle Soorten Daken",
      description:
        "Onze dakdekkers hebben ervaring met zowel platte als hellende daken.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Lange Levensduur",
      description:
        "Wij werken met hoogwaardige materialen die een lange levensduur garanderen.",
    },
  ];

  const faqs = [
    {
      question: "Hoe lang gaat een dak mee?",
      answer:
        "De levensduur hangt af van het materiaal. Bitumen gaat gemiddeld 20-25 jaar mee, EPDM 40-50 jaar, en dakpannen kunnen wel 50 tot 100 jaar meegaan, afhankelijk van het type.",
    },
    {
      question: "Wat kost nieuwe dakbedekking?",
      answer:
        "Voor een plat dak met bitumen of EPDM liggen de kosten tussen de €60 en €100 per m². Een nieuw pannendak is duurder, reken op €100 tot €200 per m².",
    },
    {
      question: "Mijn dak lekt, wat nu?",
      answer:
        "Neem direct contact op om verdere waterschade te voorkomen. Een dakdekker kan een noodreparatie uitvoeren en daarna de definitieve oplossing adviseren.",
    },
    {
      question: "Is een groen dak (sedumdak) een goede optie?",
      answer:
        "Ja, een groen dak heeft veel voordelen. Het isoleert, verlengt de levensduur van uw dakbedekking, en is goed voor de biodiversiteit. Wij kunnen u hierover adviseren.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Wit",
      location: "Amsterdam",
      rating: 5,
      quote: "Onze daklekkage was binnen een dag verholpen. Super service!",
      highlighted: false,
    },
    {
      name: "Mark V.",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Het nieuwe EPDM dak op onze aanbouw is perfect gelegd. Zeer tevreden.",
      highlighted: true,
    },
    {
      name: "Linda de Boer",
      location: "Utrecht",
      rating: 4,
      quote:
        "Duidelijke offerte en de dakdekkers waren zeer professioneel. Een aanrader.",
      highlighted: false,
    },
    {
      name: "Peter Janssen",
      location: "Eindhoven",
      rating: 5,
      quote:
        "De dakpannen op ons nieuwe huis zijn prachtig gelegd. Ziet er top uit!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1726589004565-bedfba94d3a2?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyb29maW5nJTIwd29yayUyQyUyMGNvbnN0cnVjdGlvbiUyMHNpdGUlMkMlMjByb29maW5nJTIwbWF0ZXJpYWxzfGVufDB8fHx8MTc1MTc0MjUxMHww&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1741446458387-74132b7d49cc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyb29mJTIwaW5zdGFsbGF0aW9uJTJDJTIwcHJvZmVzc2lvbmFsJTIwcm9vZmVyJTJDJTIwcm9vZmluZyUyMHNlcnZpY2VzfGVufDB8fHx8MTc1MTc0MjUxMXww&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    { type: "image", url: "https://heyboss.heeyo.ai/1751742512-1a5f7a9e.webp" },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats uw klus",
      description:
        "Beschrijf uw dakklus. Is het een reparatie, vervanging of een nieuw dak? Voeg foto's toe voor een duidelijke offerte.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Geverifieerde dakdekkers uit uw regio sturen u binnen 24 uur vrijblijvende offertes en advies.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk de offertes, bekijk profielen en kies de beste vakman voor uw dak. Plan de klus en geniet van een veilig dak.",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const pricingProps = {
    title: "Indicatieve Prijzen voor Dakbedekking",
    tiers: [
      {
        name: "Bitumen Dakbedekking",
        price: "€60 - €100",
        unit: "per m²",
        features: [
          "Platte daken",
          "Levensduur 20-25 jaar",
          "Brandmethode",
          "Goedkoopste optie",
        ],
        cta: "Offerte aanvragen",
      },
      {
        name: "EPDM Dakbedekking",
        price: "€70 - €120",
        unit: "per m²",
        features: [
          "Platte daken",
          "Levensduur 40-50 jaar",
          "Naadloos gelegd",
          "UV-bestendig",
        ],
        cta: "Offerte aanvragen",
        popular: true,
      },
      {
        name: "Pannendak Vervangen",
        price: "€100 - €200",
        unit: "per m²",
        features: [
          "Hellende daken",
          "Levensduur 50+ jaar",
          "Keramisch of beton",
          "Inclusief panlatten & folie",
        ],
        cta: "Offerte aanvragen",
      },
    ],
  };

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Dakdekkers in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "dakdekker",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Dakbedekking Nodig? | Klusgebied - Waterdicht & Veilig</title>
        <meta
          name="description"
          content="Vind snel een betrouwbare dakdekker voor reparatie, vervanging of een nieuw dak. Plaats je klus gratis en ontvang offertes van geverifieerde vakmannen in jouw regio."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Dakdekker",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "289",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "60",
                maxPrice: "200",
                valueAddedTaxIncluded: true,
                unitText: "SQM",
                description:
                  "Prijs per vierkante meter, afhankelijk van materiaal.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-red-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-red-100 border border-red-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <Home className="w-5 h-5 text-red-600" />
                    <span className="text-red-800 font-semibold text-sm">
                      Dakdekker Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Dakbedekking Nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Waterdicht & Veilig
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Van bitumen en EPDM tot pannendaken. Onze ervaren dakdekkers
                    zorgen voor een duurzaam en waterdicht dak boven uw hoofd.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
                      }
                      className="group inline-flex items-center justify-center bg-red-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-red-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw dakdekker
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.8/5</span>
                    <span>gebaseerd op 289 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele dakdekker aan het werk op een dak"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Uw Dak Weer als Nieuw in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte dakdekker voor jouw klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-red-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Dakdekker Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet aanbod voor een duurzame en stijlvolle afwerking
                van uw woning.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {roofingServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-red-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-red-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {pricingProps.title}
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Transparante prijzen voor de meest voorkomende dakklussen. De
                uiteindelijke prijs hangt af van de specifieke situatie en
                materialen.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8 items-stretch">
              {pricingProps.tiers.map((tier, index) => (
                <div
                  key={index}
                  className={`bg-white relative border rounded-2xl p-8 flex flex-col ${
                    tier.popular
                      ? "border-red-500 border-2"
                      : "border-slate-200"
                  }`}
                >
                  {tier.popular && (
                    <div className="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-red-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Populair
                    </div>
                  )}
                  <h3 className="text-xl font-bold text-slate-900">
                    {tier.name}
                  </h3>
                  <div className="mt-4 mb-8">
                    <span className="text-4xl font-bold">{tier.price}</span>
                    <span className="text-slate-500"> {tier.unit}</span>
                  </div>
                  <ul className="space-y-4 text-slate-600 flex-grow">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() =>
                      navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
                    }
                    className={`w-full mt-8 py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${
                      tier.popular
                        ? "bg-red-600 text-white hover:bg-red-700"
                        : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                    }`}
                  >
                    {tier.cta}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een dakdekker vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-red-600 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-red-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted ? "text-red-100" : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted ? "text-red-200" : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-red-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Betrouwbare dakdekkers die staan voor kwaliteit en snelle
                service.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-red-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-red-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-red-600 to-orange-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Is uw dak aan vervanging toe?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Wacht niet op lekkages. Vraag een vrijblijvende dakinspectie en
              offerte aan.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
              }
              className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() =>
            navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
          }
          className="w-full group inline-flex items-center justify-center bg-red-600 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-red-700 transition-all duration-300 shadow-lg hover:shadow-red-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_DakbedekkingPage;
