/**
 * @description This component renders a dedicated page for all maintenance contracts offered by Klusgebied. It provides a clear overview of the available contracts, such as for boilers, gutters, and general handyman services, encouraging users to explore the details of each. The page features a stunning hero section, uses the reusable MaintenanceContracts component for consistency, and includes a final call-to-action to drive conversions. Key variables include the page title and the structured layout components.
 */
import React from "react";
import Footer from "../../components/landing/Footer";
import MaintenanceContracts from "../../components/landing/MaintenanceContracts";
import FinalCTA from "../../components/landing/FinalCTA";
import usePageTitle from "../../hooks/usePageTitle";
import { ShieldCheck, Wrench, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

const OnderhoudscontractenPage = () => {
  usePageTitle("Onderhoudscontracten | Zorgeloos Wonen met Klusgebied");
  const navigate = useNavigate();

  const finalCTAProps = {
    title: "Heeft u een specifieke klus?",
    description:
      "Plaats uw klus gratis en vrijblijvend op Klusgebied. Ontvang reacties van de beste vakmensen in uw regio en kies de perfecte match voor uw project.",
    primaryCta: { text: "Plaats nu een klus", path: "/" },
    secondaryCta: { text: "Bekijk alle diensten", path: "/diensten" },
    trustElements: [
      {
        icon: ShieldCheck,
        title: "Garantie op werk",
        description: "Zekerheid voorop",
        color: "text-teal-300",
      },
      {
        icon: Wrench,
        title: "Geverifieerde Vakmensen",
        description: "Alleen de besten",
        color: "text-blue-300",
      },
    ],
    stats: [],
    backgroundImageUrl:
      "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1200",
  };

  return (
    <div className="bg-white">
      <main>
        {/* Hero Section */}
        <section className="relative bg-slate-900 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1602629726749-61bfe80aadca?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxtYWludGVuYW5jZSUyQyUyMGNvbnRyYWN0fGVufDB8fHx8MTc1MTczNzU4MHww&ixlib=rb-4.1.0&w=1920&h=1080&fit=crop"
              alt="Professioneel onderhoud"
              className="w-full h-full object-cover opacity-30"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-900/70 to-transparent"></div>
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20 md:pt-40 md:pb-28 text-center">
            <div className="inline-block bg-teal-500/10 text-teal-400 p-4 rounded-full mb-6 motion-preset-fade-down">
              <ShieldCheck className="h-12 w-12" />
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white motion-preset-fade-down motion-delay-100">
              Zorgeloos Wonen met Onze Onderhoudscontracten
            </h1>
            <p className="mt-6 max-w-3xl mx-auto text-lg md:text-xl text-slate-300 motion-preset-fade-down motion-delay-200">
              Voorkom onverwachte kosten en problemen met periodiek,
              professioneel onderhoud. Kies het contract dat bij u past en
              geniet van zekerheid en gemak, het hele jaar door.
            </p>
            <div className="mt-10 flex justify-center motion-preset-fade-down motion-delay-300">
              <button
                onClick={() => {
                  const section = document.getElementById(
                    "maintenance-contracts-section"
                  );
                  if (section) {
                    section.scrollIntoView({ behavior: "smooth" });
                  }
                }}
                className="bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 hover:-translate-y-1 inline-flex items-center"
              >
                <span>Bekijk de contracten</span>
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Maintenance Contracts Section */}
        <section
          id="maintenance-contracts-section"
          className="py-20 lg:py-28 bg-slate-50"
        >
          <div className="motion-preset-fade-down motion-delay-300">
            <MaintenanceContracts />
          </div>
        </section>

        {/* Final CTA */}
        <FinalCTA {...finalCTAProps} />
      </main>
      <Footer />
    </div>
  );
};

export default OnderhoudscontractenPage;
