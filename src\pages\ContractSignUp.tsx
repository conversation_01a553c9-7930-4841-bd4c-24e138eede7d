// src/pages/ContractSignupPage.js

import React from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { ContractSignupWizard } from '@/components/ui/ContractSignupWizard';
import { packages } from '@/components/ui/contract-template'; // Import from the new data file

const ContractSignupPage = () => {
    // 1. Read the dynamic 'packageId' from the URL (e.g., "klusgebied-cv-zeker")
    const { packageId } = useParams();
    

    console.log("packageid",packageId)
    // 2. Find the correct package data using the ID from the URL
    const selectedPackage = packages.find(p => p.id === packageId);

    console.log("packages from other",packages)
    console.log("slected package",selectedPackage)
    // 3. If the URL is invalid (no package found), redirect the user back to the main page
    if (!selectedPackage) {
        return <Navigate to="/maintenance" replace />;
    }

    // 4. Render the page with the signup wizard
    return (
        <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
             {/* 
                The wizard is now the main content of this page. 
                It no longer needs `isOpen` or `onClose` props because it's not a modal.
                You might need to adjust the `ContractSignupWizard` component itself
                to remove the modal-specific layout (like a close button or dark overlay).
             */}
            <ContractSignupWizard
            isOpen={true}
                selectedPackage={selectedPackage}
                packages={packages}
            />
        </div>
    );
};

export default ContractSignupPage;