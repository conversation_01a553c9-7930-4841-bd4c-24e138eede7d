/**
 * @description This component renders a comprehensive grid of all available services on the Klusgebied platform with dynamic categorization and interactive elements. It displays services in organized categories with hover effects, animations, and clickable navigation to individual service detail pages. The component features responsive design, world-class animations, and semantic Link elements for proper SEO and accessibility. Key variables include servicesByCategory for organizing services, hoveredService for interactive states, and Link components for navigation routing.
 */

import React, { useState, useRef, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import {
	Wrench,
	Zap,
	Paintbrush,
	Droplets,
	Hammer,
	Home,
	Drill,
	Scissors,
	Thermometer,
	Shield,
	Settings,
	Puzzle,
	TreePine,
	Car,
	Building,
	Lightbulb,
	Wind,
	Camera,
	Phone,
	Laptop,
	Truck,
	HardHat,
	CheckCircle,
	ArrowRight,
	PackageCheck,
	Building2,
	Bath,
	Pipette,
	ThermometerSun,
	ReceiptSwissFrancIcon,
	Heater,
	Grip,
	Construction,
	Waves,
} from "lucide-react";

const ServicesGrid = ({ showTitle = true, showCTA = true }) => {
	const [hoveredService, setHoveredService] = useState(null);
	const [isVisible, setIsVisible] = useState(false);
	const sectionRef = useRef();
	const navigate = useNavigate();

	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				if (entry.isIntersecting) {
					setIsVisible(true);
				}
			},
			{ threshold: 0.1 }
		);

		if (sectionRef.current) {
			observer.observe(sectionRef.current);
		}

		return () => {
			if (sectionRef.current) {
				observer.unobserve(sectionRef.current);
			}
		};
	}, []);

	const servicesByCategory = {
		"Bouw & Renovatie": [
			{ name: "Aannemer", slug: "aannemer-service", icon: HardHat },
			{ name: "Timmerman", slug: "timmerman", icon: Hammer },
			{ name: "Schilder", slug: "schilder", icon: Paintbrush },
			{ name: "Dakdekker", slug: "dakdekker", icon: Home },
			{ name: "Badkamer Renovatie", slug: "badkamer-renovatie", icon: Bath },
			{
				name: "Dakkapel Plaatsing",
				slug: "dakkapel-plaatsing",
				icon: Building2,
			},
			{
				name: "Traprenovatie",
				slug: "traprenovatie",
				icon: ReceiptSwissFrancIcon,
			},
			{ name: "Kozijn Specialist", slug: "kozijn-specialist", icon: Building },
		],
		"Installatie & Onderhoud": [
			{ name: "Loodgieter", slug: "loodgieter", icon: Droplets, popular: true },
			{ name: "Elektricien", slug: "elektricien", icon: Zap, popular: true },
			{ name: "CV Installateur", slug: "cv-installateur", icon: Thermometer },
			{ name: "Klusjesman", slug: "klusjesman", icon: Wrench, popular: true },
			{
				name: "Isolatie Service",
				slug: "isolatie-service",
				icon: ThermometerSun,
			},
			{ name: "Airco Installateur", slug: "airco-installateur", icon: Wind },
			{
				name: "Beveiligingsmonteur",
				slug: "beveiligingsmonteur",
				icon: Shield,
			},
			{
				name: "IKEA Montage",
				slug: "ikea-montage-service",
				icon: PackageCheck,
			},
		],
		"Tuin & Buitenwerk": [
			{ name: "Tuinman", slug: "tuinman", icon: TreePine },
			{ name: "Tuinbestrating", slug: "tuinbestrating", icon: Grip },
			{ name: "Dakbedekking", slug: "dakbedekking", icon: Home },
			{ name: "Garagedeur Monteur", slug: "garagedeur-monteur", icon: Car },
		],
	};

	return (
		<section ref={sectionRef} className="py-16 lg:py-20 bg-white">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{showTitle && (
					<div className="text-center mb-12 md:mb-16">
						<h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4 motion-preset-slide-up">
							Voor iedere klus een geschikte vakman
						</h2>
						<p className="text-base md:text-lg text-slate-600 max-w-3xl mx-auto motion-preset-slide-up motion-delay-200">
							Van kleine reparaties tot grote renovaties - onze geverifieerde
							professionals staan klaar om jouw klus vakkundig uit te voeren.
							Meer dan 2600 vakmannen actief – ook in jouw buurt.
						</p>
					</div>
				)}

				<div className="space-y-20">
					{Object.entries(servicesByCategory).map(
						([category, services], categoryIndex) => (
							<div
								key={category}
								className={`motion-preset-slide-up motion-delay-${
									categoryIndex * 100
								}`}
							>
								<div className="mb-10">
									<h3 className="text-2xl md:text-3xl font-bold text-slate-900">
										{category}
									</h3>
									<div className="mt-2 h-1 w-20 bg-teal-500 rounded-full"></div>
								</div>

								<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
									{services.map((service) => (
										<Link
											to={`/diensten/${service.slug}`}
											key={service.name}
											onMouseEnter={() => setHoveredService(service.name)}
											onMouseLeave={() => setHoveredService(null)}
											className="relative group block bg-white border border-slate-100 rounded-2xl p-6 text-center cursor-pointer transition-all duration-300 hover:border-teal-200 hover:shadow-2xl hover:shadow-teal-500/10 hover:scale-105 hover:-translate-y-2 h-full flex flex-col items-center justify-center"
										>
											<div className="flex justify-center mb-4">
												<div
													className={`p-4 bg-slate-100 rounded-2xl shadow-sm transition-all duration-300 group-hover:shadow-lg group-hover:bg-teal-50 ${
														hoveredService === service.name
															? "scale-110 rotate-3"
															: ""
													}`}
												>
													<service.icon className="w-8 h-8 text-teal-600 transition-colors duration-300" />
												</div>
											</div>
											<h3 className="font-bold text-slate-800 group-hover:text-teal-700 transition-colors duration-300 text-base leading-tight flex-grow flex items-center">
												{service.name}
											</h3>
											<div className="mt-4 text-sm text-slate-500 group-hover:text-teal-600 transition-colors duration-300 flex items-center justify-center font-medium">
												<span>Plaats je klus</span>
												<ArrowRight className="w-4 h-4 ml-1.5 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
											</div>
											{service.popular && (
												<div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2.5 py-1 rounded-full font-bold shadow-lg z-10">
													Populair
												</div>
											)}
										</Link>
									))}
								</div>
							</div>
						)
					)}
				</div>

				{showCTA && (
					<div className="text-center mt-16">
						<div className="bg-slate-50 rounded-3xl p-8 lg:p-12">
							<h3 className="text-2xl lg:text-3xl font-bold text-slate-800 mb-4">
								Niet gevonden wat je zoekt?
							</h3>
							<p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
								Geen probleem! Plaats je klus gratis en ontvang reacties van
								vakmannen binnen 24 uur.
							</p>
							<button
								onClick={() => navigate("/")}
								className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-600 text-white font-bold rounded-2xl hover:from-teal-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1"
							>
								Plaats direct je klus
								<ArrowRight className="ml-2 w-5 h-5" />
							</button>
						</div>
					</div>
				)}
			</div>
		</section>
	);
};

export default ServicesGrid;
