import { useState } from "react";
import { Mail } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ReferralFormProps {
  onSubmit: (email: string) => Promise<void>;
  isSubmitting: boolean;
}

export const ReferralForm = ({ onSubmit, isSubmitting }: ReferralFormProps) => {
  const [referralEmail, setReferralEmail] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit(referralEmail);
    setReferralEmail("");
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex gap-2">
        <Input
          type="email"
          placeholder="E-mailad<PERSON> van vakman"
          value={referralEmail}
          onChange={(e) => setReferralEmail(e.target.value)}
          required
        />
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <span className="text-white">Versturen...</span>
          ) : (
            <>
              <Mail className="w-4 h-4 mr-2 text-white" />
              <span className="text-white">Uitnodigen</span>
            </>
          )}
        </Button>
      </div>
    </form>
  );
};
