/**
 * @description This component renders a visually stunning, responsive gallery to showcase project images and videos. It features a masonry-style grid layout, smooth hover animations, and a modal for viewing media in full screen. The component is designed to be reusable and can be populated with different sets of media for various services. Key variables include the 'media' prop which is an array of image and video URLs, and state management for the modal viewer.
 */
import { useState } from "react";
import { X, Maximize } from "lucide-react";
import { createPortal } from "react-dom";

const ServiceGallery = ({ media }) => {
  const [selectedMedia, setSelectedMedia] = useState(null);

  const openModal = (mediaItem) => {
    setSelectedMedia(mediaItem);
  };

  const closeModal = () => {
    setSelectedMedia(null);
  };

  const modalContent = selectedMedia && (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 motion-preset-fade-in"
      onClick={closeModal}
    >
      <div
        className="relative max-w-4xl max-h-full"
        onClick={(e) => e.stopPropagation()}
      >
        {selectedMedia.type === "video" ? (
          <video
            src={selectedMedia.url}
            className="w-full h-auto max-h-[90vh] rounded-lg"
            controls
            autoPlay
          />
        ) : (
          <img
            src={selectedMedia.url}
            alt="Geselecteerd project"
            className="w-full h-auto max-h-[90vh] rounded-lg"
          />
        )}
        <button
          onClick={closeModal}
          className="absolute -top-4 -right-4 bg-white text-slate-800 rounded-full p-2 shadow-lg hover:bg-slate-200 transition"
        >
          <X className="w-6 h-6" />
        </button>
      </div>
    </div>
  );

  return (
    <div className="py-16 lg:py-24 bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
            Ons Werk in Beeld
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
            Bekijk een selectie van onze recent voltooide projecten en zie de
            kwaliteit die we leveren.
          </p>
        </div>
        <div className="columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
          {media.map((item, index) => (
            <div
              key={index}
              className="overflow-hidden rounded-2xl shadow-lg group relative cursor-pointer break-inside-avoid"
              onClick={() => openModal(item)}
            >
              {item.type === "video" ? (
                <video
                  src={item.url}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  autoPlay
                  loop
                  muted
                  playsInline
                />
              ) : (
                <img
                  src={item.url}
                  alt={`Projectvoorbeeld ${index + 1}`}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
              )}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                <Maximize className="w-12 h-12 text-white transform scale-0 group-hover:scale-100 transition-transform duration-300" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {modalContent && createPortal(modalContent, document.body)}
    </div>
  );
};

export default ServiceGallery;
