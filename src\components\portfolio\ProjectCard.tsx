import { useState } from "react";

import { Card, CardContent } from "@/components/ui/card";
import { Project } from "./types";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ProjectHeader } from "./ProjectHeader";
import { ProjectEditForm, ProjectFormData } from "./ProjectEditForm";
import { ProjectPhotos } from "./ProjectPhotos";
import ConfirmModal from "../modal/ConfirmModal";

interface ProjectCardProps {
  project: Project;
  onUpdate: (projectId: string, updatedData: Partial<Project>) => Promise<void>;
  onDelete?: (projectId: string) => void;
}

export const ProjectCard = ({
  project,
  onUpdate,
  onDelete,
}: ProjectCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const { toast } = useToast();

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const { error } = await supabase
        .from("portfolio_projects")
        .delete()
        .eq("id", project.id);

      if (error) throw error;

      toast({
        title: "Project verwijderd",
        description: "Het project is succesvol verwijderd uit je portfolio.",
      });

      if (onDelete) {
        onDelete(project.id);
      }
    } catch (error) {
      console.error("Error deleting project:", error);
      toast({
        variant: "destructive",
        title: "Fout bij verwijderen",
        description:
          "Er is iets misgegaan bij het verwijderen van het project.",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      // Upload new photos first
      const uploadPromises = data.newPhotos.map((file) => {
        const fileExt = file.name.split(".").pop();
        const fileName = `${Math.random()}.${fileExt}`;
        return supabase.storage.from("job-photos").upload(fileName, file);
      });

      const uploadResults = await Promise.all(uploadPromises);
      const newPhotoUrls = await Promise.all(
        uploadResults
          .filter((result) => !result.error)
          .map((result) => {
            const { data: urlData } = supabase.storage
              .from("job-photos")
              .getPublicUrl(result.data?.path);
            return {
              id: crypto.randomUUID(),
              photo_url: urlData.publicUrl,
            };
          })
      );

      // Update project with all data including photos
      await onUpdate(project.id, {
        title: data.title,
        description: data.description,
        budget: data.budget || null,
        photos: [...data.existingPhotos, ...newPhotoUrls],
      });

      setIsEditing(false);
      toast({
        title: "Project bijgewerkt",
        description:
          "Je project is succesvol bijgewerkt inclusief nieuwe foto's.",
      });
    } catch (error) {
      console.error("Error updating project:", error);
      toast({
        variant: "destructive",
        title: "Fout bij bijwerken",
        description:
          "Er is een fout opgetreden bij het bijwerken van het project.",
      });
    }
  };

  if (isEditing) {
    return (
      <ProjectEditForm
        project={project}
        onSubmit={handleSubmit}
        onCancel={() => setIsEditing(false)}
      />
    );
  }

  return (
    <>
      <Card className="shadow">
        <ProjectHeader
          title={project.title}
          isDeleting={isDeleting}
          onEdit={() => setIsEditing(true)}
          onDelete={() => setShowDeleteConfirm(true)}
        />
        {project.budget && (
          <CardContent className="pt-2 pb-0">
            <p className="text-sm font-medium text-green-600">
              Budget: €{project.budget.toFixed(2)}
            </p>
          </CardContent>
        )}
        <CardContent>
          <p className="text-muted-foreground text-sm">{project.description}</p>
        </CardContent>
        <ProjectPhotos photos={project.photos} />
      </Card>

      <ConfirmModal
        isOpen={showDeleteConfirm}
        setIsOpen={setShowDeleteConfirm}
        confirmHandler={handleDelete}
        loading={isDeleting}
        title="Project verwijderen"
        content="Weet je zeker dat je dit project wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt."
        variant="danger"
        yesText="Verwijderen"
      />
    </>
  );
};
