import { MessageCircle } from "lucide-react";

import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChatListItem } from "./ChatListItem";
import { ChatListProps } from "./types";

export const ChatList = ({
  chats,
  isLoading,
  onSelectChat,
  activeId,
}: ChatListProps) => {
  if (isLoading) {
    return (
      <div className="space-y-3 p-2">
        <Skeleton className="h-24 w-full rounded-lg" />
        <Skeleton className="h-24 w-full rounded-lg" />
        <Skeleton className="h-24 w-full rounded-lg" />
      </div>
    );
  }

  return (
    <ScrollArea className="h-[500px]">
      <div className="space-y-2 pr-4">
        {!chats || chats.length === 0 ? (
          <div className="text-center p-8 bg-muted/30 rounded-lg">
            <MessageCircle className="mx-auto h-8 w-8 mb-3 text-muted-foreground/50" />
            <p className="font-medium text-muted-foreground">
              Geen actieve chats
            </p>
            <p className="text-sm text-muted-foreground/70 mt-1">
              Start een gesprek door te reageren op een klus
            </p>
          </div>
        ) : (
          chats.map((chat: any) => (
            <ChatListItem
              key={chat.id}
              chat={chat}
              isActive={chat?.id === activeId}
              onClick={() => onSelectChat(chat)}
            />
          ))
        )}
      </div>
    </ScrollArea>
  );
};
