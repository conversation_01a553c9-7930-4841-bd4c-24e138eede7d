-- Migration: Create blog content generator submissions table
-- Description: Creates the content generator submissions table migrated from the separate blog project

-- Create the content generator submissions table
CREATE TABLE IF NOT EXISTS "blog_content_submissions" (
  id SERIAL PRIMARY KEY,
  user_id TEXT REFERENCES auth.users(id) ON DELETE SET NULL,
  interest TEXT NOT NULL,
  output_type TEXT NOT NULL,
  output_length TEXT NOT NULL,
  extra_requirements TEXT,
  result_text TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  email TEXT,
  email_sent BOOLEAN DEFAULT false,
  email_sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on status for filtering
CREATE INDEX IF NOT EXISTS "idx_blog_content_submissions_status" ON "blog_content_submissions" (status);

-- Create index on email for filtering
CREATE INDEX IF NOT EXISTS "idx_blog_content_submissions_email" ON "blog_content_submissions" (email);

-- Enable row-level security
ALTER TABLE "blog_content_submissions" ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Only authenticated users with admin role can view all submissions
CREATE POLICY "Admins can view all submissions" ON "blog_content_submissions"
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
);

-- Users can view their own submissions
CREATE POLICY "Users can view own submissions" ON "blog_content_submissions"
FOR SELECT TO authenticated
USING (user_id = auth.uid());

-- Anyone can create submissions
CREATE POLICY "Anyone can create submissions" ON "blog_content_submissions"
FOR INSERT TO anon, authenticated
WITH CHECK (true);

-- Only admins can update submissions
CREATE POLICY "Admins can update submissions" ON "blog_content_submissions"
FOR UPDATE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
);
