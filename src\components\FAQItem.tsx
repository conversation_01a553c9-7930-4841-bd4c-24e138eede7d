import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem = ({ question, answer }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200">
      <button
        className="flex justify-between items-center w-full py-5 px-4 text-left"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <span className="font-medium text-lg leading-relaxed tracking-wide">
          {question}
        </span>
        {isOpen ? (
          <ChevronUp className="flex-shrink-0" />
        ) : (
          <ChevronDown className="flex-shrink-0" />
        )}
      </button>

      {isOpen && (
        <div className="pb-5 px-4">
          <p className="text-gray-600 text-base leading-relaxed tracking-wide">
            {answer}
          </p>
        </div>
      )}
    </div>
  );
};

export default FAQItem;
