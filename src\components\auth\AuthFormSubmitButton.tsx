import { Loader2 } from "lucide-react";
import { useMemo } from "react";

import { Button } from "@/components/ui/button";
import { ViewType } from "@/types/auth";

interface AuthFormSubmitButtonProps {
  isLoading: boolean;
  step: "email" | "verification";
  view: ViewType;
}

export const AuthFormSubmitButton = ({
  isLoading,
  step,
  view,
}: AuthFormSubmitButtonProps) => {
  const buttonText = useMemo(() => {
    if (isLoading) {
      return "Even geduld...";
    }

    if (step === "email") {
      if (view === "sign_in") {
        return "Inloggen";
      }
      return "Maak een account aan";
    }

    if (step === "verification") {
      if (view === "sign_in") {
        return "Inloggen";
      }
      return "Account aanmaken";
    }

    return "Volgende";
  }, [isLoading, step, view]);

  return (
    <Button type="submit" className="w-full" disabled={isLoading}>
      {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
      {buttonText}
    </Button>
  );
};
