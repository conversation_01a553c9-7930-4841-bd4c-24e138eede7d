import { useEffect, useState } from "react";
import { Setting<PERSON>, Clock, Power } from "lucide-react";

import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";

interface BotConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  isRunning: boolean;
  onToggle: (state: boolean) => Promise<void>;
  selectedTimes: string[];
  onTimesChange: (times: string[]) => void;
  isLoading?: boolean;
}

export function BotConfigModal({
  isOpen,
  onClose,
  isRunning,
  onToggle,
  selectedTimes,
  onTimesChange,
  isLoading = false,
}: BotConfigModalProps) {
  const [localTimes, setLocalTimes] = useState<string[]>([]);
  const [saveLoading, setSaveLoading] = useState(false);

  const availableTimes = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, "0");
    return `${hour}:00`;
  });

  useEffect(() => {
    setLocalTimes(selectedTimes);
  }, [selectedTimes]);

  const handleTimeToggle = (time: string) => {
    setLocalTimes((prev) =>
      prev.includes(time)
        ? prev.filter((t) => t !== time)
        : [...prev, time].sort()
    );
  };

  const handleSave = async () => {
    setSaveLoading(true);
    try {
      // First, unschedule existing job
      await supabase.functions.invoke("unschedule-cron-job", {
        body: {
          job_name: "run-scheduled-job",
        },
      });

      if (localTimes.length > 0) {
        // Convert times to cron expression
        const hours = localTimes
          .map((time) => parseInt(time.split(":")[0]).toString())
          .join(",");

        const cronExpression = `0 ${hours} * * *`;

        // Schedule new job with updated times
        const { error } = await supabase.functions.invoke("schedule-cron-job", {
          body: {
            job_name: "run-scheduled-job",
            cron_expression: cronExpression,
            command: "SELECT create_random_scheduled_job();",
          },
        });

        if (error) throw error;
      }

      onTimesChange(localTimes);
      onClose();
    } catch (error) {
      console.error("Failed to update cron job:", error);
    } finally {
      setSaveLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] w-[calc(100vw-32px)]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Bot Configuratie
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
            <div className="flex items-center gap-3">
              <Power
                className={`h-5 w-5 ${
                  isRunning ? "text-green-500" : "text-muted-foreground"
                }`}
              />
              <div className="space-y-1">
                <h4 className="font-medium">Bot Status</h4>
                <p className="text-sm text-muted-foreground">
                  {isRunning ? "Bot is actief" : "Bot is inactief"}
                </p>
              </div>
            </div>
            <Switch
              checked={isRunning}
              onCheckedChange={onToggle}
              disabled={isLoading}
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <h4 className="font-medium">Uitvoeringstijden</h4>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {availableTimes.map((time) => (
                <Button
                  key={time}
                  variant={localTimes.includes(time) ? "default" : "outline"}
                  onClick={() => handleTimeToggle(time)}
                  className="w-full"
                  size="sm"
                >
                  {time}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={onClose}>
            Annuleren
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !isRunning || saveLoading}
          >
            Instellingen opslaan
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
