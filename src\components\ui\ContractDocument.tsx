import React from 'react';
import { Page, Text, View, Document, StyleSheet, Image } from '@react-pdf/renderer';

// --- THE FIX IS HERE ---
// We must assign the result of StyleSheet.create to a constant named 'styles'.
const styles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 9, 
    paddingTop: 40,
    paddingHorizontal: 50,
    paddingBottom: 60,
    lineHeight: 1.4,
    color: '#333'
  },
  text: {
    marginBottom: 8,
    textAlign: 'justify',
  },
  articleTitle: {
    fontFamily: 'Helvetica-Bold',
    fontSize: 11,
    marginBottom: 8,
    marginTop: 12,
  },
  signatureSection: {
    marginTop: 30,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  signatureImage: {
    width: 150, // This style is crucial for visibility
    height: 75,  // This style is crucial for visibility
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 4,
    marginTop: 5,
  },
  signatureLabel: {
    fontFamily: 'Helvetica-Bold',
  }
});

/**
 * @description This component serves as the template for the final PDF contract.
 * It receives the pre-processed full legal text and the user's form data to render a complete document.
 * @param {object} props - The component props.
 * @param {object} props.formData - The user's submitted form data, used for the signature block.
 * @param {string} props.contractText - The full, personalized legal text for the contract.
 */
const ContractDocument = ({ formData, contractText }) => {
  const lines = contractText.split('\n');

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {lines.map((line, index) => {
          if (line.trim().startsWith('Artikel')) {
            return <Text key={index} style={styles.articleTitle}>{line.trim()}</Text>;
          }
          if (line.trim() === '') {
            return null;
          }
          return <Text key={index} style={styles.text}>{line.trim()}</Text>;
        })}
        
        {/* The 'break' prop suggests to the renderer that this is a good place for a page break if needed */}
        <View style={styles.signatureSection} break>
            <Text style={styles.articleTitle}>Ondertekening</Text>
            <Text style={styles.text}>
                <Text style={styles.signatureLabel}>Datum:</Text> {new Date().toLocaleDateString('nl-NL')}
            </Text>
             <Text style={styles.text}>
                <Text style={styles.signatureLabel}>Klant:</Text> {formData.voornaam} {formData.achternaam}
            </Text>
            <Text style={styles.signatureLabel}>Handtekening Klant:</Text>
            {formData.digitale_handtekening ? (
                // This Image tag will now correctly receive its width and height from styles.signatureImage
                <Image style={styles.signatureImage} src={formData.digitale_handtekening} />
            ) : (
                <Text style={styles.text}>(Geen handtekening opgegeven)</Text>
            )}
        </View>
      </Page>
    </Document>
  );
};

export default ContractDocument;