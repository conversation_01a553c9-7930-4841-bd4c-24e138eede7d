import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, Info, Loader2 } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ConfirmModalProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  loading?: boolean;
  confirmHandler: () => void;
  cancelHandler?: () => void;
  title: string;
  content: string | React.ReactNode;
  yesText?: string;
  noText?: string;
  variant?: "danger" | "warning" | "info";
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  setIsOpen,
  loading = false,
  confirmHandler,
  cancelHandler,
  title,
  content,
  yesText = "Bevestigen",
  noText = "Annuleren",
  variant = "warning",
}) => {
  const icons = {
    danger: <XCircle className="h-5 w-5 text-destructive" />,
    warning: <AlertTriangle className="h-5 w-5 text-warning" />,
    info: <Info className="h-5 w-5 text-primary" />,
  };

  const buttonVariants = {
    danger: "error",
    warning: "default",
    info: "default",
  } as const;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent>
        <DialogHeader>
          <div className="flex items-center gap-2">
            {icons[variant]}
            <DialogTitle>{title}</DialogTitle>
          </div>
          <DialogDescription>{content}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => {
              setIsOpen(false);
              cancelHandler?.();
            }}
          >
            {noText}
          </Button>
          <Button
            variant={buttonVariants[variant]}
            onClick={confirmHandler}
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Verwerken...
              </span>
            ) : (
              yesText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmModal;
