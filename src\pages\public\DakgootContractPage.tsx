/**
 * @description This component renders a dedicated landing page for the Dakgoot (Gutter) Cleaning Contract. It provides detailed information about three selectable contract tiers, allowing users to compare benefits, features, and pricing dynamically. The page is designed with a world-class, conversion-focused layout, is fully responsive, and displays all contract options side-by-side on desktop for easy comparison. Key variables include contractsData for storing contract details and navigation handlers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import { ArrowLeft, Home, Check, X, Star } from "lucide-react";

const contractsData = [
  {
    name: "Dak<PERSON>ot Ba<PERSON>",
    description:
      "Essentiële jaarlijkse reiniging om verstoppingen in het najaar te voorkomen en de basisfunctionaliteit van uw dakgoten te garanderen.",
    price: "8,95",
    included: [
      "1x per jaar reiniging (najaar)",
      "Verwijderen van blad en vuil",
      "Controle op doorstroming regenpijpen",
      "Geen voorrijkosten bij onderhoud",
    ],
    excluded: [
      "Visuele inspectie op schade",
      "Kleine reparaties",
      "24/7 storingshulp",
    ],
    isMostChosen: false,
    themeColor: "bg-blue-500 hover:bg-blue-600",
  },
  {
    name: "Dakgoot Zeker",
    description:
      "Complete zorg met twee reinigingsbeurten per jaar en inspectie, ideaal om schade door alle seizoenen heen te voorkomen.",
    price: "14,95",
    included: [
      "Alle voordelen van Basis",
      "2x per jaar reiniging (voor- & najaar)",
      "Visuele inspectie op schade/slijtage",
      "Prioriteitsplanning",
      "Telefonische helpdesk voor advies",
    ],
    excluded: ["Reparatie van defecten", "Vervanging van onderdelen"],
    isMostChosen: true,
    themeColor: "bg-teal-500 hover:bg-teal-600",
  },
  {
    name: "Dakgoot All-in",
    description:
      "Volledige ontzorging inclusief reparaties. Maximale zekerheid tegen lekkages en andere onverwachte problemen het hele jaar door.",
    price: "22,95",
    included: [
      "Alle voordelen van Zeker",
      "Kleine reparaties inbegrepen",
      "Spoedservice binnen 24 uur",
      "Vogelwering/bladscheiders plaatsen",
      "Volledige dekking arbeidskosten",
    ],
    excluded: ["Vervanging van complete dakgoot"],
    isMostChosen: false,
    themeColor: "bg-blue-500 hover:bg-blue-600",
  },
];

const DakgootContractPage = () => {
  usePageTitle("Onderhoudscontract Dakgoot Reiniging | Klusgebied");
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-slate-50">
      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative py-20 lg:py-24 bg-slate-800 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1920&h=1080&fit=crop"
              alt="Dakgoot reiniging"
              className="w-full h-full object-cover opacity-20"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900 to-slate-800/50"></div>
          </div>
          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <button
              onClick={() => navigate("/")}
              className="flex items-center space-x-2 text-white/80 hover:text-white mb-8 transition-all duration-300 hover:scale-105 mx-auto"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-medium">Terug naar home</span>
            </button>
            <Home className="w-16 h-16 text-blue-400 mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              Contract Dakgoot Reiniging
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-slate-300 leading-relaxed">
              Bescherm uw woning tegen waterschade met ons zorgeloze
              reinigingscontract.
            </p>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 lg:py-24">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
              {contractsData.map((contract, index) => (
                <div
                  key={contract.name}
                  className={`relative bg-white rounded-3xl shadow-lg transition-all duration-500 h-full flex flex-col group motion-preset-slide-up motion-delay-${
                    index * 100
                  } ${
                    contract.isMostChosen
                      ? "border-4 border-teal-500 scale-100 lg:scale-105 shadow-2xl shadow-teal-500/30 z-10"
                      : "border-2 border-slate-100 hover:shadow-2xl hover:scale-102"
                  }`}
                >
                  {contract.isMostChosen && (
                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-bold inline-flex items-center space-x-2 shadow-lg">
                      <Star className="w-4 h-4" />
                      <span>Meest Gekozen</span>
                    </div>
                  )}
                  <div className="p-8 flex flex-col flex-grow">
                    <div className="text-center mb-6">
                      <h2 className="text-2xl font-bold text-slate-800 mb-2">
                        {contract.name.replace("Dakgoot ", "")}
                      </h2>
                      <p className="text-slate-600 text-sm min-h-[6rem]">
                        {contract.description}
                      </p>
                    </div>

                    <div className="text-center my-4">
                      <div className="inline-block bg-blue-100/50 rounded-full p-4">
                        <Home className="w-12 h-12 text-blue-500" />
                      </div>
                    </div>

                    <div className="text-center text-4xl font-bold text-slate-900 mb-2">
                      €{contract.price}
                      <span className="text-lg font-normal text-slate-500">
                        {" "}
                        /maand
                      </span>
                    </div>
                    <p className="text-center text-xs text-slate-400 mb-8">
                      Exclusief BTW, jaarlijks gefactureerd.
                    </p>

                    <div className="mt-auto mb-8 pt-6 border-t border-slate-200 flex-grow">
                      <h3 className="text-base font-semibold text-center mb-4 text-slate-700">
                        Wat is inbegrepen?
                      </h3>
                      <ul className="space-y-3">
                        {contract.included.map((item) => (
                          <li key={item} className="flex items-start text-sm">
                            <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-slate-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                      {contract.excluded.length > 0 && (
                        <>
                          <h3 className="text-base font-semibold text-center mb-4 mt-6 text-slate-700">
                            Wat is niet inbegrepen?
                          </h3>
                          <ul className="space-y-3">
                            {contract.excluded.map((item) => (
                              <li
                                key={item}
                                className="flex items-start text-sm"
                              >
                                <X className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                                <span className="text-slate-500 opacity-90">
                                  {item}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </div>

                    <button
                      className={`w-full text-white font-bold py-3 rounded-xl text-lg transition-all duration-300 shadow-lg hover:scale-105 ${contract.themeColor}`}
                    >
                      Kies {contract.name.replace("Dakgoot ", "")}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default DakgootContractPage;
