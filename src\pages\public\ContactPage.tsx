/**
 * @description This component renders the Contact page for the Klusgebied platform. It includes a fully functional contact form that submits data to a Supabase database and triggers an email notification. The page features a modern design with clear contact information, an interactive map placeholder, and provides users with clear feedback on form submission status (loading, success, error). Key variables include form state management, submission handlers, and API integration logic for a seamless user experience.
 */
import React, { useState } from "react";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  Mail,
  Phone,
  MapPin,
  Send,
  Loader,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
// import { supabase } from "../lib/supabase";
import config from "../../lib/data/project_config.json";
import { supabase } from "@/integrations/supabase/client";

const ContactPage = () => {
  usePageTitle("Contact | Klusgebied");
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [status, setStatus] = useState("idle"); // idle, loading, success, error
  const [error, setError] = useState("");

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.email || !formData.message) {
      setError("Email en bericht zijn verplicht.");
      setStatus("error");
      return;
    }
    setStatus("loading");
    setError("");

    try {
      // 1. Check for duplicates
      const { data: existingSubmission, error: checkError } = await supabase
        .from("contact_submissions")
        .select("id")
        .eq("email", formData.email)
        .eq("message", formData.message)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        // Ignore 'No rows found' error
        throw new Error(`Database check error: ${checkError.message}`);
      }

      if (existingSubmission) {
        setStatus("error");
        setError("U heeft dit bericht al eerder verzonden.");
        return;
      }

      // 2. Send email notification
      const emailHtml = `...`; // Simplified for brevity
      const { error: emailError } = await supabase.functions.invoke(
        "send-email",
        {
          body: {
            to: [config.user_email],
            title: `[Klusgebied Contact] Nieuw bericht: ${formData.subject}`,
            html: `<p>Nieuw contactformulier ontvangen:</p><ul><li>Naam: ${formData.name}</li><li>Email: ${formData.email}</li><li>Onderwerp: ${formData.subject}</li><li>Bericht: ${formData.message}</li></ul>`,
          },
        }
      );

      let emailSent = true;
      if (emailError) {
        console.error("Error sending notification email:", emailError);
        emailSent = false;
      }

      // 3. Save to database
      const { error: insertError } = await supabase
        .from("contact_submissions")
        .insert({
          ...formData,
          email_sent: emailSent,
          email_sent_at: new Date().toISOString(),
        });

      if (insertError) {
        throw new Error(`Database insert error: ${insertError.message}`);
      }

      setStatus("success");
      setFormData({ name: "", email: "", subject: "", message: "" });
    } catch (err) {
      setStatus("error");
      setError(
        err.message || "Er is iets misgegaan. Probeer het later opnieuw."
      );
      console.error(err);
    }
  };

  return (
    <div className="bg-white min-h-screen flex flex-col">
      <main className="flex-grow">
        <section className="bg-slate-100 py-20 md:py-32">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-slate-800 mb-4">
              Neem Contact Op
            </h1>
            <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto">
              Heeft u een vraag, opmerking of wilt u met ons samenwerken? We
              horen graag van u.
            </p>
          </div>
        </section>

        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
              {/* Contact Form */}
              <div className="bg-slate-50 p-8 rounded-lg shadow-md">
                <h2 className="text-xl md:text-2xl font-bold text-slate-800 mb-6">
                  Stuur ons een bericht
                </h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        htmlFor="name"
                        className="block text-sm font-medium text-slate-700 mb-1"
                      >
                        Naam
                      </label>
                      <input
                        type="text"
                        name="name"
                        id="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium text-slate-700 mb-1"
                      >
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-medium text-slate-700 mb-1"
                    >
                      Onderwerp
                    </label>
                    <input
                      type="text"
                      name="subject"
                      id="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-medium text-slate-700 mb-1"
                    >
                      Bericht *
                    </label>
                    <textarea
                      name="message"
                      id="message"
                      rows={5}
                      value={formData.message}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                    ></textarea>
                  </div>
                  <div>
                    <button
                      type="submit"
                      disabled={status === "loading"}
                      className="w-full flex justify-center items-center bg-teal-500 text-white font-bold py-3 px-6 rounded-lg hover:bg-teal-600 transition-colors duration-300 disabled:bg-slate-400"
                    >
                      {status === "loading" ? (
                        <Loader className="animate-spin h-5 w-5" />
                      ) : (
                        <>
                          <Send className="h-5 w-5 mr-2" /> Bericht Verzenden
                        </>
                      )}
                    </button>
                  </div>
                  {status === "success" && (
                    <div className="text-green-600 flex items-center">
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Bedankt voor uw bericht! We nemen zo snel mogelijk contact
                      op.
                    </div>
                  )}
                  {status === "error" && (
                    <div className="text-red-600 flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2" />
                      {error}
                    </div>
                  )}
                </form>
              </div>

              {/* Contact Info */}
              <div className="space-y-8">
                <div className="bg-slate-50 p-8 rounded-lg shadow-md">
                  <h3 className="text-lg md:text-xl font-bold text-slate-800 mb-4">
                    Contactgegevens
                  </h3>
                  <div className="space-y-4 text-slate-600">
                    <div className="flex items-start">
                      <Mail className="h-6 w-6 mr-4 text-teal-500 mt-1" />
                      <div>
                        <span className="font-semibold">Email:</span>
                        <br /> <EMAIL>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Phone className="h-6 w-6 mr-4 text-teal-500 mt-1" />
                      <div>
                        <span className="font-semibold">Telefoon:</span>
                        <br /> 085 - 130 5000
                      </div>
                    </div>
                    <div className="flex items-start">
                      <MapPin className="h-6 w-6 mr-4 text-teal-500 mt-1" />
                      <div>
                        <span className="font-semibold">Adres:</span>
                        <br /> Keizersgracht 123, 1015 CJ Amsterdam
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-slate-50 p-8 rounded-lg shadow-md">
                  <h3 className="text-lg md:text-xl font-bold text-slate-800 mb-4">
                    Locatie
                  </h3>
                  <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1736117705462-34145ac33bdf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxBbXN0ZXJkYW0lMjBtYXAlMkMlMjBjaXR5JTIwbWFwJTJDJTIwbG9jYXRpb258ZW58MHx8fHwxNzUxNzU0NDkwfDA&ixlib=rb-4.1.0?w=1024&h=1024"
                      alt="Kaart van Amsterdam"
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default ContactPage;
