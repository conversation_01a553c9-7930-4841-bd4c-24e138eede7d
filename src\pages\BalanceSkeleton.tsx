import { Skeleton } from "@/components/ui/skeleton";

export const BalanceSkeleton = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Back button skeleton */}
      <Skeleton className="h-10 w-[150px]" />

      {/* Title skeleton */}
      <Skeleton className="h-9 w-[200px]" />

      {/* Balance card skeleton */}
      <div className="rounded-lg border p-6 space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-5 w-[100px]" />
          <Skeleton className="h-8 w-[180px]" />
        </div>
        <div className="space-y-3">
          <Skeleton className="h-10 w-full max-w-[300px]" />
          <Skeleton className="h-10 w-[120px]" />
        </div>
      </div>

      {/* Transaction history skeleton */}
      <div className="space-y-4">
        <Skeleton className="h-6 w-[180px]" />
        <div className="space-y-2">
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 rounded-lg border"
            >
              <div className="space-y-1">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-3 w-[120px]" />
              </div>
              <Skeleton className="h-6 w-[80px]" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
