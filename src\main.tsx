// src/main.jsx (or index.js) - AFTER
import React, { Suspense } from "react"; // <-- 1. Import Suspense
import ReactDOM from "react-dom/client";
import { BrowserRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import App from "./App";
import "./index.css";
import "./i18n"; // <-- 2. Import your i18n configuration to run it
import { HelmetProvider } from "react-helmet-async";

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			staleTime: 1000 * 60,
			gcTime: 1000 * 60 * 5,
			retry: 1,
		},
	},
});

ReactDOM.createRoot(document.getElementById("root")!).render(
	<React.StrictMode>
		<HelmetProvider>
			{/* 3. Wrap your app in Suspense */}
			<Suspense fallback={<div>Loading translations...</div>}>
				<QueryClientProvider client={queryClient}>
					<BrowserRouter>
						<App />
					</BrowserRouter>
				</QueryClientProvider>
			</Suspense>
		</HelmetProvider>
	</React.StrictMode>
);
