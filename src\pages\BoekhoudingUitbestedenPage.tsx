import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  FolderOpen,
  CheckCircle,
  Building,
  FileText,
  Calculator,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/hooks/useAuth";

interface FormData {
  bedrijfsnaam: string;
  kvkNummer: string;
  jaaromzet: string;
  boekhoudstatus: string;
  aantalFacturen: string;
  opmerkingen: string;
}

export const BoekhoudingUitbestedenPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    bedrijfsnaam: "",
    kvkNummer: "",
    jaaromzet: "",
    boekhoudstatus: "",
    aantalFacturen: "",
    opmerkingen: "",
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Save to database
      const { error } = await supabase.from("accounting_requests").insert({
        user_id: userProfile.id,
        company_name: formData.bedrijfsnaam,
        chamber_of_commerce_number: formData.kvkNummer,
        annual_revenue: formData.jaaromzet,
        accounting_status: formData.boekhoudstatus,
        invoice_count: formData.aantalFacturen,
        comments: formData.opmerkingen,
        status: "pending",
      });

      if (error) throw error;

      // Send email notification to accounting partner
      const emailContent = {
        to: ["<EMAIL>"], // Replace with actual accounting partner email
        subject: "Nieuwe boekhouding aanvraag van vakman",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #0d9488;">Nieuwe Boekhouding Aanvraag</h2>
            <div style="background-color: #f0fdfa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #0d9488; margin-top: 0;">Bedrijfsgegevens</h3>
              <p><strong>Bedrijfsnaam:</strong> ${formData.bedrijfsnaam}</p>
              <p><strong>KvK-nummer:</strong> ${formData.kvkNummer}</p>
              <p><strong>Jaaromzet:</strong> ${formData.jaaromzet}</p>
              <p><strong>Huidige boekhoudstatus:</strong> ${
                formData.boekhoudstatus
              }</p>
              <p><strong>Aantal facturen per maand:</strong> ${
                formData.aantalFacturen
              }</p>
              ${
                formData.opmerkingen
                  ? `<p><strong>Opmerkingen:</strong> ${formData.opmerkingen}</p>`
                  : ""
              }
            </div>
            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #475569; margin-top: 0;">Contactgegevens Vakman</h3>
              <p><strong>Naam:</strong> ${userProfile.first_name} ${
          userProfile.last_name
        }</p>
              <p><strong>Email:</strong> ${userProfile.email}</p>
              <p><strong>Telefoon:</strong> ${
                userProfile.phone_number || "Niet opgegeven"
              }</p>
            </div>
            <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
              Deze aanvraag is automatisch gegenereerd via het Klusgebied platform.
            </p>
          </div>
        `,
      };

      const { error: emailError } = await supabase.functions.invoke(
        "send-email",
        {
          body: emailContent,
        }
      );

      if (emailError) {
        console.error("Error sending notification email:", emailError);
        // Don't fail the form submission if email fails
      }

      setIsSubmitted(true);
      toast({
        title: "Aanvraag verzonden!",
        description:
          "Onze boekhoudpartner neemt binnen 2 werkdagen contact met je op.",
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Fout bij verzenden",
        description: "Er is iets misgegaan. Probeer het opnieuw.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-[calc(100vh-85px)] py-12 bg-gradient-to-br from-teal-50 to-blue-50">
        <div className="container mx-auto p-6 max-w-4xl">
          <Button
            variant="ghost"
            onClick={() => navigate("/dashboard")}
            className="mb-6 hover:bg-white/50"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Terug naar dashboard
          </Button>

          <Card className="p-8 text-center bg-white/80 backdrop-blur-sm border-0 shadow-xl">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Bedankt voor je aanvraag!
              </h1>
              <p className="text-lg text-gray-600 mb-6">
                Onze boekhoudpartner neemt binnen 2 werkdagen contact met je op.
              </p>
              <div className="bg-teal-50 border border-teal-200 rounded-lg p-4 mb-6">
                <p className="text-teal-800 font-medium">
                  Je aanvraag is succesvol verzonden en wordt nu verwerkt.
                </p>
              </div>
              <Button
                onClick={() => navigate("/dashboard")}
                className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3"
              >
                Terug naar dashboard
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-85px)] py-12 bg-gradient-to-br from-teal-50 to-blue-50">
      <div className="container mx-auto p-6 max-w-4xl">
        <Button
          variant="ghost"
          onClick={() => navigate("/dashboard")}
          className="mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Terug naar dashboard
        </Button>

        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-teal-100 rounded-full">
              <FolderOpen className="h-8 w-8 text-teal-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Boekhouding Uitbesteden
              </h1>
              <p className="text-gray-600 text-lg">
                Besteed je administratie uit aan een erkende boekhoudpartner
              </p>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-teal-100 rounded-lg">
                <Building className="h-5 w-5 text-teal-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Professioneel</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Erkende boekhoudpartners met jarenlange ervaring
            </p>
          </Card>

          <Card className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Compleet</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Van facturatie tot BTW-aangifte, alles onder één dak
            </p>
          </Card>

          <Card className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calculator className="h-5 w-5 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Tijdbesparend</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Meer tijd voor je klussen, minder tijd aan administratie
            </p>
          </Card>
        </div>

        {/* Form Section */}
        <Card className="p-8 bg-white/80 backdrop-blur-sm border-0 shadow-xl">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Vraag boekhouder aan
            </h2>
            <p className="text-gray-600">
              Vul onderstaand formulier in en onze partner neemt contact met je
              op
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label
                  htmlFor="bedrijfsnaam"
                  className="text-sm font-medium text-gray-700"
                >
                  Bedrijfsnaam *
                </Label>
                <Input
                  id="bedrijfsnaam"
                  type="text"
                  required
                  value={formData.bedrijfsnaam}
                  onChange={(e) =>
                    handleInputChange("bedrijfsnaam", e.target.value)
                  }
                  className="mt-1"
                  placeholder="Jouw bedrijfsnaam"
                />
              </div>

              <div>
                <Label
                  htmlFor="kvkNummer"
                  className="text-sm font-medium text-gray-700"
                >
                  KvK-nummer *
                </Label>
                <Input
                  id="kvkNummer"
                  type="text"
                  required
                  value={formData.kvkNummer}
                  onChange={(e) =>
                    handleInputChange("kvkNummer", e.target.value)
                  }
                  className="mt-1"
                  placeholder="12345678"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label
                  htmlFor="jaaromzet"
                  className="text-sm font-medium text-gray-700"
                >
                  Jaaromzet *
                </Label>
                <Select
                  onValueChange={(value) =>
                    handleInputChange("jaaromzet", value)
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Selecteer jaaromzet" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-25000">€0 - €25.000</SelectItem>
                    <SelectItem value="25000-50000">
                      €25.000 - €50.000
                    </SelectItem>
                    <SelectItem value="50000-100000">
                      €50.000 - €100.000
                    </SelectItem>
                    <SelectItem value="100000-250000">
                      €100.000 - €250.000
                    </SelectItem>
                    <SelectItem value="250000+">€250.000+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label
                  htmlFor="boekhoudstatus"
                  className="text-sm font-medium text-gray-700"
                >
                  Huidige boekhoudstatus *
                </Label>
                <Select
                  onValueChange={(value) =>
                    handleInputChange("boekhoudstatus", value)
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Selecteer status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="zelf-doen">Zelf doen</SelectItem>
                    <SelectItem value="boekhouder">
                      Heb al een boekhouder
                    </SelectItem>
                    <SelectItem value="geen-idee">Geen idee</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label
                htmlFor="aantalFacturen"
                className="text-sm font-medium text-gray-700"
              >
                Aantal facturen per maand *
              </Label>
              <Select
                onValueChange={(value) =>
                  handleInputChange("aantalFacturen", value)
                }
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecteer aantal facturen" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-10">1-10 facturen</SelectItem>
                  <SelectItem value="11-25">11-25 facturen</SelectItem>
                  <SelectItem value="26-50">26-50 facturen</SelectItem>
                  <SelectItem value="51-100">51-100 facturen</SelectItem>
                  <SelectItem value="100+">100+ facturen</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label
                htmlFor="opmerkingen"
                className="text-sm font-medium text-gray-700"
              >
                Eventuele opmerkingen
              </Label>
              <Textarea
                id="opmerkingen"
                value={formData.opmerkingen}
                onChange={(e) =>
                  handleInputChange("opmerkingen", e.target.value)
                }
                className="mt-1"
                placeholder="Vertel ons meer over je specifieke wensen of vragen..."
                rows={4}
              />
            </div>

            <div className="flex justify-end pt-6">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 text-lg font-medium"
              >
                {isSubmitting ? "Verzenden..." : "Aanvraag verzenden"}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default BoekhoudingUitbestedenPage;
