import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, CheckCircle, XCircle, UserCheck } from "lucide-react";

import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

interface JobDetailHeaderProps {
  isOwner: boolean;
  status: string;
  onBack: () => void;
  onEdit?: () => void;
  onComplete?: () => void;
  onCancel?: () => void;
  showAcceptButton?: boolean;
  onAcceptVakman?: () => void;
  jobId: string;
  onStatusChange: (newStatus: string) => void;
}

export const JobDetailHeader = ({
  isOwner,
  status,
  onBack,
  onCancel,
  showAcceptButton,
  onAcceptVakman,
  jobId,
  onStatusChange,
}: JobDetailHeaderProps) => {
  const showStatusButtons = status === "in_behandeling" && isOwner;
  const navigate = useNavigate();
  const [isVakman, setIsVakman] = useState(false);

  useEffect(() => {
    const checkUserType = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      const { data: profile } = await supabase
        .from("profiles")
        .select("user_type")
        .eq("id", user.id)
        .single();

      setIsVakman(profile?.user_type === "vakman");
    };

    checkUserType();
  }, []);

  const handleBack = () => {
    if (isVakman) {
      console.log("Navigating vakman to my-responses");
      navigate("/mijn_antwoorden");
    } else {
      onBack();
    }
  };

  const handleComplete = async () => {
    try {
      const { error } = await supabase
        .from("jobs")
        .update({ status: "completed" })
        .eq("id", jobId);

      if (error) throw error;

      onStatusChange("completed");
      toast({
        title: "Klus afgerond",
        description: "De klus is succesvol afgerond.",
      });
    } catch (error) {
      console.error("Error completing job:", error);
      toast({
        variant: "destructive",
        title: "Fout",
        description: "Er is een fout opgetreden bij het afronden van de klus.",
      });
    }
  };

  return (
    <div className="flex items-center justify-between">
      <Button
        variant="ghost"
        className="flex items-center gap-2 border"
        onClick={handleBack}
      >
        <ArrowLeft className="w-4 h-4" />
        Terug naar overzicht
      </Button>
      <div className="flex items-center gap-2">
        {showAcceptButton && onAcceptVakman && (
          <Button
            variant="default"
            className="flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white"
            onClick={onAcceptVakman}
          >
            <UserCheck className="w-4 h-4" />
            Accepteer Vakman
          </Button>
        )}
        {showStatusButtons && (
          <Button
            variant="default"
            className="flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white"
            onClick={handleComplete}
          >
            <CheckCircle className="w-4 h-4" />
            Markeer als voltooid
          </Button>
        )}
        {showStatusButtons && onCancel && (
          <Button
            variant="destructive"
            className="flex items-center gap-2"
            onClick={onCancel}
          >
            <XCircle className="w-4 h-4" />
            Annuleer klus
          </Button>
        )}
      </div>
    </div>
  );
};
