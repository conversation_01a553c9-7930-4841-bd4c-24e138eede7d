/**
 * @description This component renders a comprehensive and SEO-optimized detail page for air conditioning installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for airco installers.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Wind,
  ShieldCheck,
  Thermometer,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
} from "lucide-react";

const Service_AircoInstallateurPage = () => {
  usePageTitle("Airco Installateur Nodig? | Klusgebied - Koelen & Verwarmen");
  const navigate = useNavigate();

  const aircoServices = [
    {
      icon: Wind,
      title: "Single-Split Airco Installatie",
      description:
        "Perfect voor het koelen of verwarmen van één specifieke ruimte.",
      points: [
        "Ideaal voor slaapkamer, woonkamer of kantoor.",
        "Energiezuinige modellen met A++ label.",
        "Stille werking voor optimaal comfort.",
        "Vakkundige installatie van binnen- en buitenunit.",
      ],
    },
    {
      icon: Wind,
      title: "Multi-Split Airco Installatie",
      description:
        "Meerdere binnenunits aangesloten op één buitenunit voor meerdere kamers.",
      points: [
        "Koel of verwarm meerdere ruimtes onafhankelijk.",
        "Tot 5 binnenunits op één krachtige buitenunit.",
        "Ruimtebesparend en esthetisch fraai.",
        "Flexibele oplossing voor het hele huis.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Airco Onderhoud & Service",
      description:
        "Periodiek onderhoud voor een optimale werking en lange levensduur.",
      points: [
        "Jaarlijkse controle en reiniging van filters.",
        "Controle van koudemiddel en druk.",
        "Voorkomt storingen en zorgt voor schone lucht.",
        "Verplicht voor behoud van garantie.",
      ],
    },
    {
      icon: Thermometer,
      title: "Advies & Capaciteitsberekening",
      description:
        "Wij helpen u de juiste airco te kiezen die past bij uw ruimte en wensen.",
      points: [
        "Nauwkeurige berekening van het benodigde koelvermogen.",
        "Advies over de beste locatie voor de units.",
        "Vergelijking van verschillende merken en modellen.",
        "Een perfect afgestemd systeem voor uw situatie.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "STEK-gecertificeerd",
      description:
        "Onze installateurs zijn gecertificeerd voor een veilige en correcte installatie.",
    },
    {
      icon: <Thermometer className="w-8 h-8 text-white" />,
      title: "Energiezuinige Systemen",
      description:
        "Wij installeren moderne, energiezuinige airco's met een hoog rendement.",
    },
    {
      icon: <Wind className="w-8 h-8 text-white" />,
      title: "Stil & Comfortabel",
      description:
        "Geniet van een aangename temperatuur zonder storend geluid.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een airco inclusief installatie?",
      answer:
        "Een split-unit airco voor één kamer kost gemiddeld tussen de €1.800 en €2.800, inclusief installatie. De prijs is afhankelijk van het vermogen en merk.",
    },
    {
      question: "Hoeveel koelvermogen heb ik nodig?",
      answer:
        "Dit hangt af van de grootte van de ruimte, de hoeveelheid zonlicht en de isolatie. Onze specialist berekent het benodigde vermogen voor u.",
    },
    {
      question: "Kan een airco ook verwarmen?",
      answer:
        "Ja, de meeste moderne airco's zijn lucht-lucht warmtepompen en kunnen zeer efficiënt verwarmen in de winter. Dit kan u besparen op uw gasrekening.",
    },
    {
      question: "Hoe vaak moet een airco onderhouden worden?",
      answer:
        "Wij adviseren om uw airco jaarlijks te laten onderhouden. Dit zorgt voor een schone lucht, optimale werking en een langere levensduur van het systeem.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Vries",
      location: "Almere",
      rating: 5,
      quote:
        "De airco is perfect geïnstalleerd. Wat een verademing tijdens de hete dagen. De monteur was zeer professioneel en vriendelijk.",
      highlighted: true,
    },
    {
      name: "Joris B.",
      location: "Lelystad",
      rating: 5,
      quote:
        "Duidelijk advies gekregen en de installatie was snel en netjes. De airco is superstil en koelt de slaapkamer perfect.",
      highlighted: false,
    },
    {
      name: "Linda Hermans",
      location: "Dronten",
      rating: 5,
      quote:
        "Heel blij met onze multi-split airco. Elke kamer de perfecte temperatuur. Klusgebied heeft ons gekoppeld aan een echte vakman.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1678616143792-77d4091e9bbc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBhaXIlMjBjb25kaXRpb25pbmclMjB1bml0JTJDJTIwaW5kb29yJTIwaW5zdGFsbGF0aW9uJTJDJTIwaG9tZSUyMGNvbWZvcnR8ZW58MHx8fHwxNzUxNzQxNTQ0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/flagged/photo-1585772311853-3c823ba89097?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxhaXIlMjBjb25kaXRpb25pbmclMjBzeXN0ZW0lMkMlMjBvdXRkb29yJTIwdW5pdCUyQyUyMGVuZXJneSUyMGVmZmljaWVuY3l8ZW58MHx8fHwxNzUxNzQxNTQ0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1622787066476-2794c60844d3?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxhaXIlMjBjb25kaXRpb25pbmclMjBpbnN0YWxsYXRpb24lMkMlMjB0ZWNobmljaWFuJTIwYXQlMjB3b3JrJTJDJTIwSFZBQyUyMHNlcnZpY2V8ZW58MHx8fHwxNzUxNzQxNTQ0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzcGxpdCUyMGFpciUyMGNvbmRpdGlvbmluZyUyMHN5c3RlbSUyQyUyMHJlc2lkZW50aWFsJTIwY29vbGluZyUyQyUyMGhvbWUlMjBjbGltYXRlJTIwY29udHJvbHxlbnwwfHx8fDE3NTE3NDE1NDR8MA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Vraag advies aan",
      description:
        "Beschrijf uw wensen en de ruimte(s) die u wilt koelen of verwarmen. Wij helpen u op weg.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes op maat",
      description:
        "STEK-gecertificeerde installateurs sturen u vrijblijvende offertes voor het beste systeem.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & plan de installatie",
      description:
        "Vergelijk de specialisten, kies de beste deal en plan de installatie. Geniet van een perfect klimaat!",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Airco Installateurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "airco installateur",
    color: "blue",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Airco Installateur Nodig? | Klusgebied - Koelen & Verwarmen
        </title>
        <meta
          name="description"
          content="Vind een STEK-gecertificeerde airco installateur voor het plaatsen en onderhouden van uw aircosysteem. Vraag gratis offertes aan voor een comfortabel huis, het hele jaar door."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6">
                    <Wind className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      Airco Installateur
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Airco nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-cyan-500 mt-2">
                      Koel & Comfortabel
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor een comfortabel klimaat in huis, het hele jaar door.
                    Professionele installatie van energiezuinige aircosystemen.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/airco-installateur")
                      }
                      className="group inline-flex items-center justify-center bg-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw installateur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1678616143792-77d4091e9bbc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBhaXIlMjBjb25kaXRpb25pbmclMjB1bml0JTJDJTIwaW5kb29yJTIwaW5zdGFsbGF0aW9uJTJDJTIwaG9tZSUyMGNvbWZvcnR8ZW58MHx8fHwxNzUxNzQxNTQ0fDA&ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Moderne airco unit aan een strakke muur"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Uw Airco in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte installateur voor uw
                airco.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Airco Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Complete oplossingen voor koeling en verwarming van uw woning of
                kantoor.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {aircoServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een airco inclusief installatie?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn inclusief een standaard installatie. U ontvangt
                altijd een duidelijke offerte vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Single-Split Airco:{" "}
                    <strong className="text-slate-900">€1.800–€2.800</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Multi-Split Airco (2 units):{" "}
                    <strong className="text-slate-900">vanaf €3.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Jaarlijks onderhoud:{" "}
                    <strong className="text-slate-900">€100–€150</strong>
                  </span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/plaats-een-klus/airco-installateur")}
                className="bg-blue-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een airco lieten installeren
                via Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-blue-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-blue-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-blue-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-blue-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een STEK-gecertificeerde monteur en geniet van een
                perfect werkend systeem.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-blue-500 to-cyan-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor een comfortabel huis?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend adviesgesprek aan en ontdek de beste koel-
              en verwarmingsoplossing voor uw situatie.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/airco-installateur")}
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu advies aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/airco-installateur")}
          className="w-full group inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_AircoInstallateurPage;
