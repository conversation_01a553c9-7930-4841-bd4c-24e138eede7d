import { Plus } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

interface EmptyStateProps {
  onCreateClick: () => void;
}

export function EmptyState({ onCreateClick }: EmptyStateProps) {
  return (
    <div className="rounded-lg border-2 border-dashed p-8 text-center">
      <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center">
        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-muted">
          <Plus className="h-6 w-6" />
        </div>
        <h3 className="mt-4 text-lg font-semibold">Gee<PERSON> sjablonen</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          Je hebt nog geen werksjablonen. Maak er een aan om te beginnen.
        </p>
        <Button onClick={onCreateClick} className="mt-4">
          <Plus className="h-4 w-4 mr-2" />
          S<PERSON><PERSON><PERSON>on maken
        </Button>
      </div>
    </div>
  );
}
