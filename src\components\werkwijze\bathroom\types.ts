export interface BathroomFormData {
	surfaceArea: string;
	currentSituation: string;
	wallTiles: string;
	floorTiles: string;
	toilet: string;
	shower: string;
	bath: string;
	sink: string;
	ventilation: string;
	heating: string;
	additionalInfo: string;
	email: string;
	password: string;
	postalCode: string;
	houseNumber: string;
	description: string;
}

export enum QuestionStep {
	"surface" = "surfaceArea",
	"current" = "currentSituation",
	"walls" = "wallTiles",
	"floor" = "floorTiles",
	"toilet" = "toilet",
	"shower" = "shower",
	"bath" = "bath",
	"sink" = "sink",
	"ventilation" = "ventilation",
	"heating" = "heating",
	"additional" = "additionalInfo",
	"account" = "account",
}
