/**
 * @description This component renders a comprehensive and SEO-optimized detail page for IKEA assembly services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout, mirroring the structure of other top-tier service pages for consistency. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for IKEA furniture assembly.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  PackageCheck,
  Clock,
  ThumbsUp,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  <PERSON>ch,
  ChevronLeft,
  ChevronRight,
  <PERSON>uote,
  MapPinned,
} from "lucide-react";

const Service_IKEAMontageServicePage = () => {
  usePageTitle("IKEA Montage Service | Klusgebied - Snel & Vakkundig");
  const navigate = useNavigate();

  const ikeaServices = [
    {
      icon: PackageCheck,
      title: "PAX Kasten Monteren",
      description:
        "Vakkundige montage van uw complete PAX garderobekast, inclusief deuren en interieur.",
      points: [
        "Snelle, stevige en waterpas montage.",
        "Installatie van deuren en alle interieuraccessoires.",
        "Ervaring met honderden PAX-systemen.",
        "Bespaar uzelf de tijd en frustratie.",
      ],
    },
    {
      icon: Wrench,
      title: "BESTÅ Kasten Monteren",
      description:
        "Montage van BESTÅ tv-meubels, opbergkasten en complete wandcombinaties.",
      points: [
        "Montage van alle BESTÅ-combinaties.",
        "Veilige en stevige wandmontage.",
        "Precies zoals u het heeft ontworpen.",
        "Professionele afwerking.",
      ],
    },
    {
      icon: PackageCheck,
      title: "METOD Keukens Installeren",
      description:
        "Volledige installatie van uw IKEA keuken, van kasten tot werkblad en apparatuur.",
      points: [
        "Complete keukeninstallatie van A tot Z.",
        "Montage kasten, zagen en plaatsen werkblad.",
        "Aansluiten van alle apparatuur.",
        "Perfect afgewerkt eindresultaat door ervaren monteurs.",
      ],
    },
    {
      icon: ThumbsUp,
      title: "Algemene Meubelmontage",
      description:
        "Montage van bedden, bureaus, tafels, stoelen en andere IKEA meubels.",
      points: [
        "Montage van alle soorten IKEA meubels.",
        "Van MALM bedden tot KALLAX kasten.",
        "Snel, vakkundig en zonder stress.",
        "Laat het over aan een professional.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Snel & Efficiënt",
      description:
        "Onze monteurs kennen de producten en monteren uw meubels snel en correct.",
    },
    {
      icon: <ThumbsUp className="w-8 h-8 text-white" />,
      title: "Geen Stress",
      description:
        "Bespaar uzelf de tijd en frustratie van ingewikkelde handleidingen.",
    },
    {
      icon: <PackageCheck className="w-8 h-8 text-white" />,
      title: "Perfect Resultaat",
      description:
        "Wij zorgen voor een stevig en perfect afgesteld meubel, klaar voor gebruik.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost het om IKEA meubels te laten monteren?",
      answer:
        "De kosten zijn meestal gebaseerd op een uurtarief (gemiddeld €45-€65) of een vaste prijs per meubelstuk. Voor een grote PAX kast kunt u rekenen op 2-4 uur werk.",
    },
    {
      question: "Moet ik zelf aanwezig zijn tijdens de montage?",
      answer:
        "Het is handig als u aanwezig bent om de monteur binnen te laten en eventuele vragen over de plaatsing te beantwoorden. Daarna kunt u uw gang gaan.",
    },
    {
      question: "Wat moet ik voorbereiden voor de monteur komt?",
      answer:
        "Zorg ervoor dat de dozen in de juiste kamer staan en er voldoende werkruimte is. Controleer of alle pakketten compleet zijn.",
    },
    {
      question: "Monteren jullie ook meubels van andere merken?",
      answer:
        "Jazeker! Hoewel we gespecialiseerd zijn in IKEA, monteren onze klusjesmannen ook meubels van andere merken.",
    },
  ];

  const reviews = [
    {
      name: "Jeroen & Annelies",
      location: "Utrecht",
      rating: 5,
      quote:
        "De monteur heeft onze complete PAX kast in een halve dag perfect in elkaar gezet. Wat een verademing! Super service.",
      highlighted: true,
    },
    {
      name: "Sophie de Groot",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Mijn BESTÅ wandmeubel hangt prachtig en superstrak aan de muur. De monteur was vriendelijk, snel en heel vakkundig.",
      highlighted: false,
    },
    {
      name: "Familie Yilmaz",
      location: "Rotterdam",
      rating: 4,
      quote:
        "De keuken is netjes geïnstalleerd. Het duurde iets langer dan verwacht, maar het resultaat is top. Blij mee!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1653971858474-4f2dfa7f4dc1?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxJS0VBJTIwYXNzZW1ibHklMkMlMjBmdXJuaXR1cmUlMjBhc3NlbWJseSUyQyUyMGhvbWUlMjBpbXByb3ZlbWVudHxlbnwwfHx8fDE3NTE3NDI0Mzl8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1602123116122-9b6e1695a670?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxJS0VBJTIwZnVybml0dXJlJTJDJTIwcHJvZmVzc2lvbmFsJTIwYXNzZW1ibHklMkMlMjBob21lJTIwZGVjb3J8ZW58MHx8fHwxNzUxNzQyNDM5fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1556228453-efd6c1ff04f6?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Geef aan welke meubels gemonteerd moeten worden. Voeg eventueel foto's toe.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Monteurs reageren",
      description:
        "Ontvang binnen 24 uur reacties en prijsvoorstellen van ervaren monteurs.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste monteur. Leun achterover en geniet!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Montage Monteurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "montage monteur",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>IKEA Montage Service | Klusgebied - Snel & Vakkundig</title>
        <meta
          name="description"
          content="Geen zin in gedoe met handleidingen? Laat uw IKEA meubels snel en vakkundig monteren door een ervaren monteur via Klusgebied. Plaats uw klus gratis."
        />
        {/* ... other meta tags ... */}
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <PackageCheck className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      IKEA Montage Service
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    IKEA Montage Hulp?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-cyan-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Snel & Vakkundig
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Geen zin in gedoe met handleidingen en schroefjes? Onze
                    ervaren monteurs zetten uw IKEA meubels snel en vakkundig in
                    elkaar.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/ikea-meubels")}
                      className="group inline-flex items-center justify-center bg-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Boek een monteur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 215 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Een persoon die een IKEA meubel in elkaar zet"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte monteur voor jouw meubels.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze IKEA Montage Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Voor elk IKEA meubel een snelle en vakkundige montage.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {ikeaServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een montage monteur?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De prijs is vaak gebaseerd op een uurtarief of een vaste prijs
                per meubel. U ontvangt altijd een duidelijke offerte vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Uurtarief:{" "}
                    <strong className="text-slate-900">€45–€65</strong> (incl.
                    BTW)
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vaste prijs: Op basis van offerte voor grotere projecten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Voorrijkosten: Vaak inbegrepen</span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/ikea-meubels")}
                className="bg-blue-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een monteur vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-blue-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-blue-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-blue-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-blue-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Voordelen van Montagehulp
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor gemak, snelheid en een perfect gemonteerd meubel.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-blue-500 to-cyan-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar met de montage-stress?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en laat uw meubels monteren door een professional.
              Geniet direct van uw aankoop.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/ikea-meubels")}
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu uw montageklus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/ikea-meubels")}
          className="w-full group inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_IKEAMontageServicePage;
