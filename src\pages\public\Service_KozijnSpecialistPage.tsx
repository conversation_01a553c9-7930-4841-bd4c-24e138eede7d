/**
 * @description This component renders a comprehensive and SEO-optimized detail page for window and door frame services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for frame specialists. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data, mirroring the structure of the Loodgieter page for consistency.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Thermometer,
  ShieldCheck,
  Square,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  User<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
  Layers,
} from "lucide-react";

const Service_KozijnSpecialistPage = () => {
  usePageTitle(
    "Kozijn Specialist Nodig? | Klusgebied - Hout, Kunststof & Aluminium"
  );
  const navigate = useNavigate();

  const kozijnServices = [
    {
      icon: Square,
      title: "Kunststof Kozijnen Plaatsen",
      description:
        "Onderhoudsarme en uitstekend isolerende kunststof kozijnen.",
      points: [
        "Onderhoudsarm en zeer duurzaam.",
        "Uitstekende thermische en akoestische isolatie.",
        "Verkrijgbaar in vele kleuren en structuren (zoals houtlook).",
        "Een investering waar u jarenlang plezier van heeft.",
      ],
    },
    {
      icon: Layers,
      title: "Houten Kozijnen Repareren & Plaatsen",
      description:
        "Reparatie van houtrot en plaatsing van nieuwe, authentieke houten kozijnen.",
      points: [
        "Geeft uw woning een warme en authentieke uitstraling.",
        "Volledig op maat gemaakt, passend bij de stijl van uw huis.",
        "Makkelijk te bewerken en te schilderen in elke gewenste kleur.",
        "Natuurlijk en ademend materiaal.",
      ],
    },
    {
      icon: Square,
      title: "Aluminium Kozijnen",
      description:
        "Strakke en moderne aluminium kozijnen voor een minimalistische uitstraling.",
      points: [
        "Sterk, licht en hebben een slank profiel.",
        "Moderne, minimalistische uitstraling met maximale lichtinval.",
        "Zeer onderhoudsarm en ideaal voor grote raampartijen.",
        "Duurzaam en volledig recyclebaar.",
      ],
    },
    {
      icon: Thermometer,
      title: "HR++ Glas Installatie",
      description:
        "Vervangen van enkel of dubbel glas door hoogrendementsglas voor optimale isolatie.",
      points: [
        "Bespaar aanzienlijk op uw energierekening.",
        "Speciale coating en gasvulling voor betere isolatie.",
        "Meer comfort en minder stookkosten.",
        "Draagt bij aan een beter energielabel.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Thermometer className="w-8 h-8 text-white" />,
      title: "Betere Isolatie",
      description:
        "Bespaar op uw energierekening met kozijnen die warmte binnen en kou buiten houden.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Inbraakwerend",
      description:
        "Onze kozijnen zijn voorzien van modern hang- en sluitwerk met politiekeurmerk.",
    },
    {
      icon: <Square className="w-8 h-8 text-white" />,
      title: "Onderhoudsarm",
      description:
        "Kies voor kunststof of aluminium kozijnen en u heeft er jarenlang geen omkijken naar.",
    },
  ];

  const faqs = [
    {
      question: "Wat is het verschil tussen houten en kunststof kozijnen?",
      answer:
        "Houten kozijnen hebben een authentieke uitstraling en zijn makkelijk te repareren, maar vragen meer onderhoud. Kunststof kozijnen zijn onderhoudsarm, isoleren uitstekend en zijn verkrijgbaar in vele kleuren.",
    },
    {
      question: "Hoe lang gaan nieuwe kozijnen mee?",
      answer:
        "Kunststof en aluminium kozijnen gaan gemiddeld 50 tot 75 jaar mee. Goed onderhouden houten kozijnen kunnen ook tientallen jaren meegaan.",
    },
    {
      question: "Krijg ik subsidie op nieuwe kozijnen met HR++ glas?",
      answer:
        "Ja, de overheid stelt vaak subsidies beschikbaar voor het verbeteren van de isolatie van uw woning. Onze specialisten kunnen u informeren over de actuele regelingen.",
    },
    {
      question: "Wat is de levertijd en installatietijd voor nieuwe kozijnen?",
      answer:
        "De levertijd is afhankelijk van het materiaal en de maatwerkopties, en varieert van 4 tot 12 weken. De installatie zelf duurt meestal 1 tot 2 dagen per verdieping.",
    },
  ];

  const reviews = [
    {
      name: "Familie Janssen",
      location: "Eindhoven",
      rating: 5,
      quote:
        "Onze nieuwe kunststof kozijnen zijn prachtig en het scheelt enorm in de stookkosten. Top werk!",
      highlighted: false,
    },
    {
      name: "Linda S.",
      location: "Breda",
      rating: 5,
      quote:
        "De houtlook kunststof kozijnen zijn niet van echt te onderscheiden. Zeer tevreden met het advies en de plaatsing.",
      highlighted: true,
    },
    {
      name: "Peter de Groot",
      location: "Tilburg",
      rating: 5,
      quote:
        "Snelle en vakkundige plaatsing van onze nieuwe kozijnen met HR++ glas. Het is nu veel stiller in huis.",
      highlighted: false,
    },
    {
      name: "Mark de Vries",
      location: "Den Bosch",
      rating: 5,
      quote:
        "De aluminium kozijnen geven ons huis een super strakke en moderne look. Heel blij mee!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    { type: "image", url: "https://heyboss.heeyo.ai/1751741652-ac260188.webp" },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Vraag een offerte aan",
      description:
        "Beschrijf uw wensen: materiaal, afmetingen en type glas. Onze specialisten adviseren u graag.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang advies & offertes",
      description:
        "Vergelijk vrijblijvende offertes van geverifieerde kozijnspecialisten uit uw regio.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & laat plaatsen",
      description:
        "Kies de beste vakman voor uw project. De specialist meet alles in en zorgt voor een vakkundige montage.",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Kozijn Specialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "kozijn specialist",
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    serviceType: "Kozijn Plaatsing en Vervanging",
    provider: {
      "@type": "Organization",
      name: "Klusgebied",
      url: "https://www.klusgebied.nl/",
    },
    areaServed: {
      "@type": "Country",
      name: "NL",
    },
    description:
      "Vind een specialist voor het plaatsen, vervangen of repareren van houten, kunststof of aluminium kozijnen. Vraag gratis offertes aan.",
    name: "Kozijn Specialist Service",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Kozijn Specialist Nodig? | Klusgebied - Hout, Kunststof & Aluminium
        </title>
        <meta
          name="description"
          content="Voor het plaatsen, vervangen en repareren van houten, kunststof en aluminium kozijnen. Verbeter de isolatie, veiligheid en uitstraling van uw woning."
        />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-teal-100 border border-teal-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <Square className="w-5 h-5 text-teal-600" />
                    <span className="text-teal-800 font-semibold text-sm">
                      Kozijn Specialist
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Kozijn Specialist?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-green-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Isolatie & Stijl
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor het plaatsen, vervangen en repareren van houten,
                    kunststof en aluminium kozijnen. Verbeter de isolatie,
                    veiligheid en uitstraling van uw woning.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/kozijnen")}
                      className="group inline-flex items-center justify-center bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw specialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 215 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Moderne woning met grote ramen en strakke, donkere kozijnen"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Nieuwe Kozijnen in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte specialist voor uw
                kozijnen.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-teal-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-teal-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Kozijn Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet aanbod voor een duurzame en stijlvolle afwerking
                van uw woning.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {kozijnServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-teal-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-teal-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-teal-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een kozijn via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor erkende
                vakmannen. Prijzen zijn per m² en inclusief montage en HR++
                glas. De uiteindelijke prijs is afhankelijk van uw keuzes.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Kunststof Kozijn:{" "}
                    <strong className="text-slate-900">€700 - €900</strong> per
                    m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Houten Kozijn:{" "}
                    <strong className="text-slate-900">€800 - €1.100</strong>{" "}
                    per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Aluminium Kozijn:{" "}
                    <strong className="text-slate-900">€900 - €1.200</strong>{" "}
                    per m²
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/kozijnen")}
                className="bg-teal-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die kozijnen lieten plaatsen via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-teal-600 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-teal-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-teal-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-teal-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-teal-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een ervaren specialist en investeer in de waarde en
                het comfort van uw huis.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-teal-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-teal-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-teal-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-teal-500 to-green-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Zijn uw kozijnen aan vervanging toe?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Investeer in comfort en veiligheid. Vraag een vrijblijvende
              offerte aan voor nieuwe kozijnen.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/kozijnen")}
              className="bg-white text-teal-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/kozijnen")}
          className="w-full group inline-flex items-center justify-center bg-teal-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_KozijnSpecialistPage;
