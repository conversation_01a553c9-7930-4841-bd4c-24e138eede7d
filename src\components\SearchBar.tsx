import { Search, X } from "lucide-react";
import { useState } from "react";

import { Input } from "./ui/input";

interface JobSearchBarProps {
  onSearch: (query: string) => void;
}

export const SearchBar = ({ onSearch }: JobSearchBarProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    onSearch(value);
  };

  const handleClear = () => {
    setSearchQuery("");
    onSearch("");
  };

  return (
    <div className="relative mb-6">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <Input
          type="text"
          placeholder="Zoek op titel, beschrijving of locatie..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10 pr-10 py-3 w-full bg-white border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-primary/20 text-base"
        />
        {searchQuery && (
          <button
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>
    </div>
  );
};
