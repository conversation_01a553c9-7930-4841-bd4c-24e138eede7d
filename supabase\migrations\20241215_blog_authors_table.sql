-- Migration: Create blog authors table
-- Description: Creates the blog authors table migrated from the separate blog project

-- Create the authors table
CREATE TABLE IF NOT EXISTS "authors" (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  avatar_url TEXT,
  bio TEXT,
  linkedin_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on name for faster lookups
CREATE INDEX IF NOT EXISTS "idx_authors_name" ON "authors" (name);

-- Enable row-level security
ALTER TABLE "authors" ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Public can read authors
CREATE POLICY "Public can view authors" ON "authors"
FOR SELECT TO anon, authenticated USING (true);

-- Only authenticated users with admin role can insert/update/delete
CREATE POLICY "Admins can manage authors" ON "authors"
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
);

-- Add foreign key constraint to posts table
ALTER TABLE "posts"
ADD CONSTRAINT "fk_posts_author"
FOREIGN KEY (author_id) REFERENCES "authors"(id);

-- Insert default author
INSERT INTO "authors" (name, avatar_url, bio, linkedin_url)
VALUES (
  'Klus Blog Team', 
  'https://heyboss.heeyo.ai/chat-images/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_HFZMLdJH.png', 
  'Expert team van Klus Blog. Wij delen professionele inzichten en praktische tips voor moderne professionals.', 
  'https://www.linkedin.com/company/klusblog'
) ON CONFLICT DO NOTHING;
