export interface ChatProfile {
  id: string;
  first_name: string;
  last_name: string;
  full_name: string | null;
  profile_photo_url: string | null;
  company_name: string | null;
}

export interface ChatJob {
  id: string;
  title: string;
  user_id: string;
  created_at: string;
  status: string;
}

export interface Chat {
  id: string;
  status: string;
  jobs: ChatJob;
  profiles: ChatProfile;
}

export interface ChatListProps {
  onSelectChat: (chat: Chat) => void;
  activeId?: string;
  chats: any[];
  isLoading: boolean;
}
