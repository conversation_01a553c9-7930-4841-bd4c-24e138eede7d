import { useForm } from "react-hook-form";
import { useLocation, useNavigate } from "react-router-dom";
import { useSet<PERSON>tom } from "jotai";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { ProfileFormFields } from "@/components/profile/ProfileFormFields";
import { ProfileFormData, UserType } from "@/types/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { profileSchema } from "@/schemas/profileSchema";
import { useAuth } from "./hooks/useAuth";
import { isRegisteringAtom } from "./SessionProvider";

interface RegistrationFormProps {
  userType: UserType;
  onSubmit: (data: ProfileFormData) => Promise<void>;
  email: string;
}

export const RegistrationForm = ({
  userType,
  onSubmit,
  email,
}: RegistrationFormProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { initializeSession } = useAuth();
  const setIsRegistering = useSetAtom(isRegisteringAtom);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: email,
      city: "",
      street_address: "",
      house_number: "",
      house_number_addition: "",
      phone_number: "",
      company_name: "",
      postal_code: "",
      user_type: userType,
    },
    mode: "onChange",
  });

  const handleSubmit = async (data: ProfileFormData) => {
    setIsRegistering(true);
    try {
      // Ensure user_type is explicitly set before submission
      const submissionData = {
        ...data,
        user_type: userType,
        email: email,
      };

      await onSubmit(submissionData);
      await initializeSession();

      // Get returnUrl from search params
      const params = new URLSearchParams(location.search);
      const returnUrl = params.get("returnUrl");
      navigate(returnUrl || "/");
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "Registratie fout",
        description: error.message || "Er is een onverwachte fout opgetreden.",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="space-y-4">
          <ProfileFormFields form={form} isVakman={userType === "vakman"} />
        </div>
        <Button
          type="submit"
          className="w-full"
          disabled={form.formState.isSubmitting}
        >
          {form.formState.isSubmitting
            ? "Bezig met registreren..."
            : "Registreren"}
        </Button>
      </form>
    </Form>
  );
};
