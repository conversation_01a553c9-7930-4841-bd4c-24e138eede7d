import { AlertTriangle } from "lucide-react";

import { Alert, AlertDescription } from "@/components/ui/alert";

interface ProfileAlertProps {
  isProfileComplete: boolean;
}

export const ProfileAlert = ({ isProfileComplete }: ProfileAlertProps) => {
  if (isProfileComplete) return null;

  return (
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription>
        Je profiel is nog niet compleet. Vul je voornaam, achternaam, adres en
        huisnummer in om gebruik te kunnen maken van alle functionaliteiten.
      </AlertDescription>
    </Alert>
  );
};
