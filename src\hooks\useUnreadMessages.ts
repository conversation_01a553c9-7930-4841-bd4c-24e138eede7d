import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useUnreadMessages = (userId: string) => {
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const [unreadResponseCount, setUnreadResponseCount] = useState(0);
  const { toast } = useToast();

  const fetchUnreadCount = async () => {
    try {
      // Execute both queries in parallel for better performance
      const [messagesResult, userJobsResult] = await Promise.all([
        supabase
          .from("messages")
          .select("*", { count: "exact" })
          .eq("receiver_id", userId)
          .eq("read", false),
        supabase.from("jobs").select("id").eq("user_id", userId),
      ]);

      // Handle messages count
      if (messagesResult.error) throw messagesResult.error;
      setUnreadMessageCount(messagesResult.count ?? 0);

      // Handle jobs and responses
      if (userJobsResult.error) throw userJobsResult.error;
      const jobIds = userJobsResult.data?.map((job) => job.id) ?? [];

      if (jobIds.length > 0) {
        const { data: responseResult, error: responseError } = await supabase
          .from("job_responses")
          .select("*", { count: "exact" })
          .is("viewed_at", null)
          .in("job_id", jobIds);

        if (responseError) throw responseError;
        setUnreadResponseCount(responseResult?.length ?? 0);
      } else {
        setUnreadResponseCount(0);
      }
    } catch (error) {
      console.error("Error fetching unread counts:", error);
      setUnreadMessageCount(0);
      setUnreadResponseCount(0);
    }
  };

  useEffect(() => {
    if (!userId) return;

    fetchUnreadCount();

    // Subscribe to new messages
    const messagesChannel = supabase
      .channel("messages")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "messages",
          filter: `receiver_id=eq.${userId}`,
        },
        (payload: any) => {
          if (payload.eventType === "INSERT" && !payload.new.read) {
            setUnreadMessageCount((prev) => prev + 1);
            toast({
              title: "Nieuw bericht ontvangen",
              description: "Je hebt een nieuw bericht ontvangen.",
            });
          } else if (payload.eventType === "UPDATE" && payload.new.read) {
            setUnreadMessageCount((prev) => Math.max(0, prev - 1));
          }
        }
      )
      .subscribe();

    // Subscribe to new job responses
    const responsesChannel = supabase
      .channel("responses")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "job_responses",
        },
        async (payload: any) => {
          const { data: job } = await supabase
            .from("jobs")
            .select("user_id")
            .eq("id", payload.new.job_id)
            .single();

          if (job?.user_id === userId && !payload.new.viewed_at) {
            setUnreadResponseCount((prev) => prev + 1);
            toast({
              title: "Nieuwe reactie op je klus",
              description: "Een vakman heeft gereageerd op je klusopdracht.",
            });
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(messagesChannel);
      supabase.removeChannel(responsesChannel);
    };
  }, [userId, toast]);

  const resetUnreadMessageCount = async () => {
    try {
      const { error } = await supabase
        .from("messages")
        .update({ read: true })
        .eq("receiver_id", userId)
        .eq("read", false);

      if (error) throw error;
      setUnreadMessageCount(0);
    } catch (error) {
      console.error("Error resetting unread messages:", error);
    }
  };

  const resetUnreadResponseCount = async () => {
    try {
      // First get the job IDs
      const { data: jobs } = await supabase
        .from("jobs")
        .select("id")
        .eq("user_id", userId);

      const jobIds = jobs?.map((job) => job.id) || [];

      // Then update the responses for those jobs
      const { error } = await supabase
        .from("job_responses")
        .update({ viewed_at: new Date().toISOString() })
        .is("viewed_at", null)
        .in("job_id", jobIds);

      if (error) throw error;
      setUnreadResponseCount(0);
    } catch (error) {
      console.error("Error resetting unread responses:", error);
    }
  };

  const resetAllUnreadCounts = async () => {
    await Promise.all([resetUnreadMessageCount(), resetUnreadResponseCount()]);
  };

  return {
    unreadMessageCount,
    unreadResponseCount,
    totalUnreadCount: unreadMessageCount + unreadResponseCount,
    resetUnreadMessageCount,
    resetUnreadResponseCount,
    resetAllUnreadCounts,
    refreshUnreadCount: fetchUnreadCount,
  };
};
