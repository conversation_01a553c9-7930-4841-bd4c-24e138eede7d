/**
 * @description This component renders a comprehensive and SEO-optimized detail page for parquet flooring services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout, mirroring the structure of other top-tier service pages for consistency. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for parquet specialists.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Layers,
  ShieldCheck,
  Award,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Ruler,
} from "lucide-react";

const Service_ParketvloerSpecialistPage = () => {
  usePageTitle(
    "Parketvloer Specialist Nodig? | Klusgebied - Leggen, Schuren & Lakken"
  );
  const navigate = useNavigate();

  const parketServices = [
    {
      icon: Layers,
      title: "Traditioneel Parket Leggen",
      description:
        "Massief houten vloeren, gelegd in patronen zoals visgraat of Hongaarse punt.",
      points: [
        "Authentieke uitstraling met massief hout.",
        "Perfect voor patronen zoals visgraat en blokpatroon.",
        "Duurzaam en gaat generaties lang mee.",
        "Professioneel geschuurd en afgewerkt op locatie.",
      ],
    },
    {
      icon: Ruler,
      title: "Lamellenparket (Multiplank)",
      description:
        "De uitstraling van een massieve vloer met de stabiliteit van een gelaagde opbouw.",
      points: [
        "Stabiele opbouw, minder gevoelig voor krimp en uitzetting.",
        "Geschikt voor vloerverwarming.",
        "Snellere installatie dan traditioneel parket.",
        "Verkrijgbaar in vele houtsoorten en afwerkingen.",
      ],
    },
    {
      icon: Award,
      title: "Parket Schuren & Afwerken",
      description:
        "Uw bestaande houten vloer weer als nieuw maken door schuren en afwerken.",
      points: [
        "Verwijdert krassen, vlekken en slijtage.",
        "Stofvrij schuren voor een schone omgeving.",
        "Keuze uit afwerking met lak, olie of hardwaxolie.",
        "Een tweede leven voor uw kostbare vloer.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Vloeronderhoud & Reparatie",
      description:
        "Periodiek onderhoud en reparatie van beschadigingen aan uw parketvloer.",
      points: [
        "Dieptereiniging en aanbrengen van nieuwe beschermlaag.",
        "Reparatie van krassen, deuken of waterschade.",
        "Advies voor dagelijks onderhoud.",
        "Verlengt de levensduur van uw parketvloer.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Award className="w-8 h-8 text-white" />,
      title: "Ambachtelijk Vakmanschap",
      description:
        "Onze specialisten hebben oog voor detail en leveren perfect afgewerkte vloeren.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Stofvrij Schuren",
      description:
        "Wij gebruiken moderne apparatuur voor een schone en stofvrije werkomgeving.",
    },
    {
      icon: <Layers className="w-8 h-8 text-white" />,
      title: "Duurzame Materialen",
      description:
        "Wij werken met hoogwaardige, duurzame houtsoorten en afwerkingen.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een nieuwe parketvloer?",
      answer:
        "De prijs is afhankelijk van de houtsoort en het legpatroon, en varieert van €40 tot €120 per m². Vraag een offerte aan voor een exacte prijs.",
    },
    {
      question: "Hoe lang duurt het schuren van een vloer?",
      answer:
        "Het schuren en lakken van een gemiddelde woonkamer (ca. 40m²) duurt meestal 2 tot 3 dagen, inclusief droogtijd.",
    },
    {
      question: "Welk onderhoud heeft een parketvloer nodig?",
      answer:
        "Regelmatig stofzuigen en dweilen met een speciaal parket-reinigingsmiddel is voldoende. Periodiek onderhoud met olie of was kan nodig zijn.",
    },
    {
      question: "Is parket geschikt voor vloerverwarming?",
      answer:
        "Ja, veel soorten parket zijn geschikt voor vloerverwarming. Het is belangrijk om de juiste houtsoort en ondervloer te kiezen. Onze specialisten adviseren u graag.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Boer",
      location: "Haarlem",
      rating: 5,
      quote:
        "Onze visgraatvloer is prachtig gelegd. Echte vakmensen met passie voor hun werk. We zijn er ontzettend blij mee!",
      highlighted: true,
    },
    {
      name: "Mark van Vliet",
      location: "Den Haag",
      rating: 5,
      quote:
        "Mijn 30 jaar oude parketvloer is geschuurd en opnieuw gelakt. Hij ziet er weer uit als nieuw! Fantastisch werk geleverd.",
      highlighted: false,
    },
    {
      name: "Linda Chen",
      location: "Amstelveen",
      rating: 4,
      quote:
        "Goede service en duidelijke communicatie. De reparatie van de waterschade is onzichtbaar uitgevoerd.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1712038487428-7f7f5d76b69b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXR2bG9lciUyQyUyMHZpc2dyYWF0JTJDJTIwaW50ZXJpZXVyfGVufDB8fHx8MTc1MTc0MTUyN3ww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1613744627911-e7e34634fee3?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXQlMkMlMjBob3V0ZW4lMjB2bG9lciUyQyUyMHJlbm92YXRpZXxlbnwwfHx8fDE3NTE3NDE1Mjd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1675050794600-665b38f67c2a?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXQlMjBvbmRlcmhvdWQlMkMlMjB2bG9lciUyMHNjaHVyZW4lMkMlMjB2YWttYW5zY2hhcHxlbnwwfHx8fDE3NTE3NDE1Mjd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1505576391880-b3f9d713dc4f?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je wensen voor de vloer. Voeg afmetingen en foto's toe.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Specialisten reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde parketteurs.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste specialist voor jouw droomvloer.",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Parketspecialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "parketspecialist",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Parketvloer Specialist Nodig? | Klusgebied - Leggen, Schuren & Lakken
        </title>
        <meta
          name="description"
          content="Vind een vakkundige parketspecialist voor het leggen, schuren, lakken en onderhouden van uw houten vloer. Plaats uw klus gratis en ontvang offertes van geverifieerde vakmannen."
        />
        {/* ... other meta tags ... */}
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-amber-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-amber-100 border border-amber-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <Layers className="w-5 h-5 text-amber-600" />
                    <span className="text-amber-800 font-semibold text-sm">
                      Parketvloer Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Parketvloer Specialist?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-amber-500 to-orange-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Ambachtelijk & Duurzaam
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Voor het leggen, schuren en onderhouden van uw houten vloer.
                    Vind een geverifieerde specialist in uw regio.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-amber-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-amber-600 transition-all duration-300 shadow-lg hover:shadow-amber-500/30 transform hover:-translate-y-1"
                    >
                      Vind jouw parketteur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 189 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1712038487428-7f7f5d76b69b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXJrZXR2bG9lciUyQyUyMHZpc2dyYWF0JTJDJTIwaW50ZXJpZXVyfGVufDB8fHx8MTc1MTc0MTUyN3ww&ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Prachtig gelegde visgraat parketvloer"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte specialist voor jouw
                vloer.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-amber-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-amber-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Parket Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vakmanschap voor een houten vloer die generaties lang meegaat.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {parketServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-amber-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-amber-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-amber-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een parketvloer?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De prijs is afhankelijk van houtsoort, legpatroon en afwerking.
                U ontvangt altijd een duidelijke offerte vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-amber-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Parket leggen:{" "}
                    <strong className="text-slate-900">€40–€120</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-amber-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vloer schuren & lakken:{" "}
                    <strong className="text-slate-900">€25–€45</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-amber-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Reparaties: Op basis van offerte</span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus")}
                className="bg-amber-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-amber-600 transition-all duration-300 shadow-lg hover:shadow-amber-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een parketteur vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-amber-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-amber-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-amber-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-amber-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een specialist via Klusgebied en wees verzekerd van
                een prachtige en duurzame vloer.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-amber-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-amber-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-amber-500 to-orange-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor een prachtige houten vloer?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en ontvang vrijblijvend offertes van de beste
              parketspecialisten in uw regio.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-amber-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je parket klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-amber-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-amber-600 transition-all duration-300 shadow-lg hover:shadow-amber-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_ParketvloerSpecialistPage;
