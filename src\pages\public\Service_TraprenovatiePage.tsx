/**
 * @description This component renders a comprehensive and SEO-optimized detail page for staircase renovation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for staircase renovations. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  TrendingUp,
  ShieldCheck,
  <PERSON>rkles,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  <PERSON>r<PERSON>heck,
  CheckCircle,
  MapPin,
  ChevronLeft,
  ChevronRight,
  Quote,
} from "lucide-react";

const Service_TraprenovatiePage = () => {
  usePageTitle("Traprenovatie | Klusgebied - Een Nieuwe Look in 1 Dag");
  const navigate = useNavigate();
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const heroSection = document.getElementById("service-hero");
      if (heroSection) {
        const heroBottom = heroSection.getBoundingClientRect().bottom;
        setIsSticky(heroBottom < 0);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const pageTitle = "Traprenovatie";
  const heroProps = {
    title: (
      <>
        Traprenovatie Nodig?{" "}
        <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-green-500">
          Een Nieuwe Look in 1 Dag.
        </span>
      </>
    ),
    subtitle:
      "Geef uw trap een complete make-over. Snel, vakkundig en een blikvanger in uw interieur. Kies uit hout, laminaat of PVC voor een duurzaam en stijlvol resultaat.",
    imageUrl:
      "https://images.unsplash.com/photo-1627812961713-6a0576f95374?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0ZWQlMjBzdGFpcmNhc2UlMkMlMjB3b29kZW4lMjBzdGFpcnMlMkMlMjBob21lJTIwaW50ZXJpb3J8ZW58MHx8fHwxNzUxNzQyNDU0fDA&ixlib=rb-4.1.0&w=1920&h=1080&q=80&fit=crop",
    imageAlt: "Een prachtig gerenoveerde houten trap in een modern interieur",
  };

  const howItWorksProps = {
    title: "Uw Trap Gerenoveerd in 3 Stappen",
    steps: [
      {
        icon: Edit3,
        title: "Kies uw stijl",
        description:
          "Beschrijf uw wensen en kies het materiaal en de kleur die bij uw interieur passen. Vraag gratis advies.",
      },
      {
        icon: MessageSquare,
        title: "Ontvang offertes",
        description:
          "Vergelijk vrijblijvende offertes van de beste trapspecialisten bij u in de buurt.",
      },
      {
        icon: UserCheck,
        title: "Plan de renovatie",
        description:
          "Kies de beste vakman, plan de renovatie (vaak in 1 dag!) en geniet van uw als nieuwe trap.",
      },
    ],
  };

  const servicesProps = {
    title: "Onze Traprenovatie Diensten",
    description: "Voor elke stijl en elk budget een passende oplossing.",
    services: [
      {
        title: "Overzettreden (Hout/Laminaat)",
        description:
          "Bekleden van uw bestaande trap met nieuwe, slijtvaste overzettreden.",
        longDescription:
          "De meest populaire methode voor traprenovatie. Wij plaatsen nieuwe, op maat gemaakte treden over uw bestaande trap. U kunt kiezen uit een breed scala aan materialen zoals laminaat, PVC of echt hout, in vele kleuren en dessins. Snel, schoon en een prachtig resultaat.",
      },
      {
        title: "Trap Schilderen",
        description:
          "Uw trap een compleet nieuwe look geven met een professionele verflaag.",
        longDescription:
          "Is uw trap nog in goede staat maar bent u uitgekeken op de kleur? Een professionele schilderbeurt kan wonderen doen. Wij schuren de trap volledig, repareren eventuele beschadigingen en brengen meerdere lagen slijtvaste traplak aan voor een duurzaam en strak resultaat.",
      },
      {
        title: "Trapbekleding (Tapijt)",
        description: "Bekleden van uw trap met zacht en geluiddempend tapijt.",
        longDescription:
          "Tapijt op de trap voelt warm en zacht aan, werkt geluiddempend en is veilig (antislip). Wij bieden een ruime keuze in tapijtsoorten en kleuren en zorgen voor een vakkundige plaatsing, of u nu de hele trede bekleed wilt hebben of alleen een loper.",
      },
      {
        title: "Balustrade & Leuning Vervangen",
        description:
          "Moderniseren van uw trap door het vervangen van de leuning en balustrade.",
        longDescription:
          "De leuning en balustrade zijn bepalend voor de uitstraling en veiligheid van uw trap. Wij kunnen uw oude balustrade vervangen door een moderne variant van bijvoorbeeld RVS, glas of staal. Dit geeft uw trap en hal direct een compleet nieuwe, frisse look.",
      },
    ],
  };

  const benefitsProps = {
    title: "De Voordelen van Traprenovatie",
    description: "Een veilige, mooie en waardevaste investering in uw huis.",
    benefits: [
      {
        icon: <Sparkles className="w-10 h-10 text-white" />,
        title: "Nieuwe Look in 1 Dag",
        description:
          "Een traprenovatie met overzettreden is vaak al binnen één dag klaar.",
      },
      {
        icon: <ShieldCheck className="w-10 h-10 text-white" />,
        title: "Veilig & Slijtvast",
        description:
          "Onze materialen zijn van hoge kwaliteit, antislip en gaan jarenlang mee.",
      },
      {
        icon: <TrendingUp className="w-10 h-10 text-white" />,
        title: "Waardeverhogend",
        description:
          "Een mooie, veilige trap is een blikvanger en verhoogt de waarde van uw woning.",
      },
    ],
    themeColor: "teal",
  };

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1627812961713-6a0576f95374?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0ZWQlMjBzdGFpcmNhc2UlMkMlMjB3b29kZW4lMjBzdGFpcnMlMkMlMjBob21lJTIwaW50ZXJpb3J8ZW58MHx8fHwxNzUxNzQyNDU0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1740403016700-28f659e3c7ec?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBzdGFpcmNhc2UlMkMlMjBzdHlsaXNoJTIwc3RhaXJzJTJDJTIwaG9tZSUyMHJlbm92YXRpb258ZW58MHx8fHwxNzUxNzQyNDU0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1740403016700-28f659e3c7ec?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzdGFpcmNhc2UlMjBkZXNpZ24lMkMlMjBlbGVnYW50JTIwc3RhaXJzJTJDJTIwaG9tZSUyMGltcHJvdmVtZW50fGVufDB8fHx8MTc1MTc0MjQ1NHww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1714122901841-324b7e0f979d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzdGFpciUyMHJlbm92YXRpb24lMkMlMjBiZWF1dGlmdWwlMjBzdGFpcmNhc2UlMkMlMjBpbnRlcmlvciUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0NTR8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const reviews = [
    {
      quote:
        "Onze oude, krakende trap is omgetoverd tot een prachtig en stil exemplaar. Ongelooflijk wat er in één dag mogelijk is!",
      author: "Familie de Jong",
      location: "Haarlem",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.1.0&w=128&h=128&fit=crop",
    },
    {
      quote:
        "Zeer professioneel en netjes gewerkt. De nieuwe trap is de eyecatcher van ons huis geworden.",
      author: "Mark T.",
      location: "Almere",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.1.0&w=128&h=128&fit=crop",
    },
    {
      quote:
        "Duidelijk advies gekregen over de materialen. Het eindresultaat is boven verwachting mooi.",
      author: "Chantal van Vliet",
      location: "Nieuwegein",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.1.0&w=128&h=128&fit=crop",
    },
  ];

  const pricingProps = {
    title: "Indicatieve Prijzen voor Traprenovatie",
    description:
      "Prijzen zijn voor een standaard dichte trap met 12-14 treden. De uiteindelijke prijs hangt af van de vorm en materiaalkeuze.",
    tiers: [
      {
        name: "Laminaat / PVC",
        price: "vanaf €1.500",
        unit: "per trap",
        features: [
          "Zeer slijtvast",
          "Onderhoudsvriendelijk",
          "Vele designs",
          "Populairste keuze",
        ],
        cta: "Offerte aanvragen",
        popular: true,
      },
      {
        name: "Echt Hout",
        price: "vanaf €2.500",
        unit: "per trap",
        features: [
          "Authentieke look",
          "Warme uitstraling",
          "Kan geschuurd worden",
          "Duurzame investering",
        ],
        cta: "Offerte aanvragen",
      },
      {
        name: "Trap Schilderen",
        price: "vanaf €700",
        unit: "per trap",
        features: [
          "Budgetvriendelijk",
          "Frisse, nieuwe look",
          "Elke kleur mogelijk",
          "Arbeidsintensief",
        ],
        cta: "Offerte aanvragen",
      },
    ],
  };

  const faqs = [
    {
      question: "Wat kost een traprenovatie?",
      answer:
        "De kosten voor het renoveren van een dichte trap met 12-14 treden beginnen rond de €1.500. De prijs is afhankelijk van het materiaal, de vorm van de trap en extra opties zoals verlichting.",
    },
    {
      question: "Is mijn trap geschikt voor renovatie?",
      answer:
        "Vrijwel elke trap, of deze nu van hout, beton of staal is, kan gerenoveerd worden met overzettreden. Onze specialist kan dit ter plekke beoordelen.",
    },
    {
      question: "Hoeveel overlast geeft een traprenovatie?",
      answer:
        "De overlast is minimaal. Het werk is meestal binnen 1 tot 2 dagen afgerond en u kunt de trap tijdens de renovatie vaak gewoon blijven gebruiken.",
    },
    {
      question: "Welk materiaal kan ik het beste kiezen?",
      answer:
        "Laminaat/PVC is zeer slijtvast en onderhoudsvriendelijk. Echt hout heeft een warmere, authentieke uitstraling. Wij adviseren u graag over de beste keuze voor uw situatie.",
    },
  ];

  const localSeoProps = {
    title: "Lokale Traprenovatie Specialisten",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "traprenovatie",
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    serviceType: "Traprenovatie",
    provider: {
      "@type": "Organization",
      name: "Klusgebied",
      url: "https://www.klusgebied.nl/",
    },
    areaServed: {
      "@type": "Country",
      name: "NL",
    },
    description:
      "Laat uw trap renoveren door een specialist. Vaak binnen 1 dag een compleet nieuwe look met hout, laminaat of PVC. Vraag gratis offertes aan.",
    name: "Traprenovatie Service",
  };

  const ReviewSlider = ({ reviews }) => {
    const [current, setCurrent] = useState(0);
    const touchStartX = useRef(0);
    const touchEndX = useRef(0);

    const nextSlide = () =>
      setCurrent(current === reviews.length - 1 ? 0 : current + 1);
    const prevSlide = () =>
      setCurrent(current === 0 ? reviews.length - 1 : current - 1);

    const handleTouchStart = (e) => {
      touchStartX.current = e.touches[0].clientX;
    };
    const handleTouchMove = (e) => {
      touchEndX.current = e.touches[0].clientX;
    };
    const handleTouchEnd = () => {
      if (touchStartX.current - touchEndX.current > 50) nextSlide();
      if (touchStartX.current - touchEndX.current < -50) prevSlide();
    };

    return (
      <div
        className="relative w-full max-w-4xl mx-auto"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className="overflow-hidden">
          <div
            className="flex transition-transform ease-out duration-500"
            style={{ transform: `translateX(-${current * 100}%)` }}
          >
            {reviews.map((review, index) => (
              <div
                key={index}
                className="w-full flex-shrink-0 flex flex-col md:flex-row items-center justify-center text-center p-8"
              >
                <img
                  src={review.image}
                  alt={review.author}
                  className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover mb-4 md:mb-0 md:mr-8 shadow-lg"
                />
                <div className="md:text-left">
                  <Quote className="w-8 h-8 text-teal-200 mb-4" />
                  <p className="text-lg md:text-xl font-medium text-white italic mb-4">
                    "{review.quote}"
                  </p>
                  <p className="font-bold text-white text-lg">
                    {review.author}
                  </p>
                  <p className="text-teal-200">{review.location}</p>
                  <div className="flex justify-center md:justify-start mt-2">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-5 h-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        <button
          onClick={prevSlide}
          className="absolute top-1/2 left-0 md:-left-12 transform -translate-y-1/2 bg-white/20 hover:bg-white/40 text-white p-3 rounded-full transition-all duration-300"
        >
          <ChevronLeft size={24} />
        </button>
        <button
          onClick={nextSlide}
          className="absolute top-1/2 right-0 md:-right-12 transform -translate-y-1/2 bg-white/20 hover:bg-white/40 text-white p-3 rounded-full transition-all duration-300"
        >
          <ChevronRight size={24} />
        </button>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>{`Klusgebied | ${pageTitle}`}</title>
        <meta name="description" content={heroProps.subtitle} />
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>
      <main>
        <section
          id="service-hero"
          className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden bg-slate-900"
        >
          <div className="absolute inset-0">
            <img
              src={heroProps.imageUrl}
              alt={heroProps.imageAlt}
              className="w-full h-full object-cover opacity-30"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent"></div>
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <button
              onClick={() => navigate("/diensten")}
              className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300 motion-preset-slide-up"
            >
              <ArrowLeft size={20} />
              <span>Alle diensten</span>
            </button>
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
                {heroProps.title}
              </h1>
              <p className="text-lg md:text-xl text-slate-300 mb-8 motion-preset-slide-up motion-delay-100">
                {heroProps.subtitle}
              </p>
              <div className="motion-preset-slide-up motion-delay-200">
                <button
                  onClick={() => navigate("/plaats-een-klus/traprenovatie")}
                  className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
                >
                  Vind een trapspecialist{" "}
                  <ArrowRight className="inline-block ml-2" />
                </button>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {howItWorksProps.title}
              </h2>
            </div>
            <div className="grid md:grid-cols-3 gap-8 text-center">
              {howItWorksProps.steps.map((step, index) => (
                <div
                  key={index}
                  className="p-6 motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 text-teal-600 mb-6 shadow-sm">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600">{step.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {servicesProps.title}
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                {servicesProps.description}
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {servicesProps.services.map((service, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl shadow-md p-8 motion-preset-slide-up hover:shadow-xl transition-shadow duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <h3 className="text-xl font-bold text-slate-800 mb-2">
                    {service.title}
                  </h3>
                  <p className="text-slate-600 mb-4">{service.description}</p>
                  <div className="border-t border-slate-200 pt-4 mt-4">
                    <p className="text-slate-700 text-left leading-relaxed">
                      {service.longDescription}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                {benefitsProps.title}
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                {benefitsProps.description}
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefitsProps.benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-20 w-20 rounded-full bg-teal-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />

        <section className="py-16 lg:py-24 bg-gradient-to-br from-slate-900 to-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Wat Klanten Zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Echte verhalen van tevreden huiseigenaren.
              </p>
            </div>
            <ReviewSlider reviews={reviews} />
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {pricingProps.title}
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                {pricingProps.description}
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8 items-stretch">
              {pricingProps.tiers.map((tier, index) => (
                <div
                  key={index}
                  className={`relative border rounded-2xl p-8 flex flex-col ${
                    tier.popular
                      ? "border-teal-500 border-2"
                      : "border-slate-200"
                  }`}
                >
                  {tier.popular && (
                    <div className="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Populair
                    </div>
                  )}
                  <h3 className="text-xl font-bold text-slate-900">
                    {tier.name}
                  </h3>
                  <div className="mt-4 mb-8">
                    <span className="text-4xl font-bold">{tier.price}</span>
                    <span className="text-slate-500"> {tier.unit}</span>
                  </div>
                  <ul className="space-y-4 text-slate-600 flex-grow">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="flex items-center">
                        <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => navigate("/plaats-een-klus/traprenovatie")}
                    className={`w-full mt-8 py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${
                      tier.popular
                        ? "bg-teal-500 text-white hover:bg-teal-600"
                        : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                    }`}
                  >
                    {tier.cta}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        <FAQSection faqs={faqs} />

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(`/stad/${city.toLowerCase().replace(/\s+/g, "-")}`)
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-teal-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-gradient-to-r from-teal-500 to-green-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Is uw trap aan een nieuwe look toe?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvende offerte aan en ontdek de mogelijkheden om
              uw trap te transformeren.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/traprenovatie")}
              className="bg-white text-teal-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      {isSticky && (
        <div className="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-sm p-4 border-t border-slate-200 shadow-lg lg:hidden z-40 motion-preset-slide-up">
          <button
            onClick={() => navigate("/plaats-een-klus/traprenovatie")}
            className="w-full bg-teal-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md text-lg"
          >
            Vind een trapspecialist
          </button>
        </div>
      )}
      <Footer />
    </div>
  );
};

export default Service_TraprenovatiePage;
