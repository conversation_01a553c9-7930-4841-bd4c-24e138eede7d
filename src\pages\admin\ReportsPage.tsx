import { useMemo, useState } from "react";
import { FileText } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import { BackToDashboard } from "@/components/BackToDashboard";
import { supabase } from "@/integrations/supabase/client";
import { FilterSection } from "@/components/admin/reports/FilterSection";
import { StatsOverview } from "@/components/admin/reports/StatsOverview";
import { DetailedTable } from "@/components/admin/reports/DetailedTable";

const ReportsPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [sortBy, setSortBy] = useState("name");

  // Query voor vakman statistieken
  const { data: vakmanStats, isLoading } = useQuery({
    queryKey: ["vakmanStats"],
    queryFn: async () => {
      // Fetch all craftsmen (vakmannen) with basic information
      const { data: vakmannen, error: vakmanError } = await supabase
        .from("profiles")
        .select(
          `
          id,
          first_name,
          last_name,
          company_name,
          created_at,
          balance,
          kvk_number,
          btw_number,
          phone_number,
          email,
          street_address,
          house_number,
          house_number_addition
        `
        )
        .eq("user_type", "vakman");

      if (vakmanError) throw new Error("Failed to fetch craftsmen data");
      if (!vakmannen) return [];

      // Fetch all required data in parallel for better performance
      const vakmannenWithStats = await Promise.all(
        vakmannen.map(async (vakman) => {
          // Fetch completed jobs count and reviews in parallel
          const [jobsResponse, reviewsResponse] = await Promise.all([
            supabase
              .from("job_responses")
              .select("*", { count: "exact" })
              .eq("vakman_id", vakman.id)
              .eq("status", "completed"),
            supabase
              .from("vakman_reviews")
              .select("rating")
              .eq("vakman_id", vakman.id),
          ]);

          if (jobsResponse.error) throw new Error("Failed to fetch jobs data");
          if (reviewsResponse.error)
            throw new Error("Failed to fetch reviews data");

          // Calculate average rating
          const reviews = reviewsResponse.data || [];
          const averageRating = reviews.length
            ? reviews.reduce((sum, review) => sum + review.rating, 0) /
              reviews.length
            : 0;

          return {
            ...vakman,
            completed_jobs: jobsResponse.count || 0,
            average_rating: Number(averageRating.toFixed(2)),
            total_reviews: reviews.length,
          };
        })
      );

      return vakmannenWithStats;
    },
    staleTime: 5 * 60 * 1000, // Cache data for 5 minutes
    retry: 2, // Retry failed requests twice
  });

  // Filter en sorteer de data
  const filteredVakmannen = useMemo(() => {
    if (!vakmanStats) return [];

    const normalize = (str: string | null | undefined): string =>
      (str || "").toLowerCase();

    const matchesSearch = (vakman: (typeof vakmanStats)[0]): boolean => {
      if (!searchTerm) return true;

      const searchLower = searchTerm.toLowerCase();
      const fullName = `${vakman.first_name} ${vakman.last_name}`;
      const fullAddress = `${vakman.street_address} ${vakman.house_number} ${
        vakman.house_number_addition || ""
      }`;

      const searchableFields = [
        fullName,
        vakman.company_name,
        fullAddress,
        vakman.email,
        vakman.phone_number,
      ];

      return searchableFields.some((field) =>
        normalize(field).includes(searchLower)
      );
    };

    const getSortValue = (vakman: (typeof vakmanStats)[0]): string | number => {
      switch (sortBy) {
        case "name":
          return normalize(
            vakman.company_name || `${vakman.first_name} ${vakman.last_name}`
          );
        case "rating":
          return vakman.average_rating;
        case "jobs":
          return vakman.completed_jobs;
        default:
          return "";
      }
    };

    return vakmanStats.filter(matchesSearch).sort((a, b) => {
      const aValue = getSortValue(a);
      const bValue = getSortValue(b);

      return typeof aValue === "string"
        ? (aValue as string).localeCompare(bValue as string)
        : (bValue as number) - (aValue as number);
    });
  }, [vakmanStats, searchTerm, sortBy]);

  return (
    <div className="max-w-7xl mx-auto py-6 px-6 sm:px-0 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">Vakmannen Rapportage</h1>
        <Button
          variant="outline"
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          <FileText className="h-4 w-4" />
          Exporteer als PDF
        </Button>
      </div>

      <BackToDashboard />

      <FilterSection
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        filterType={filterType}
        setFilterType={setFilterType}
        sortBy={sortBy}
        setSortBy={setSortBy}
      />

      <StatsOverview vakmanStats={vakmanStats || []} />

      <DetailedTable
        filteredVakmannen={filteredVakmannen || []}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ReportsPage;
