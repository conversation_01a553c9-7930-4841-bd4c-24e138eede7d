/**
 * @description This component renders a comprehensive and SEO-optimized detail page for insulation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for insulation technicians.
 */
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  TrendingUp,
  ShieldCheck,
  ArrowRight,
  ThermometerSun,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Leaf,
} from "lucide-react";

const Service_IsolatiemonteurPage = () => {
  usePageTitle("Isolatie Nodig? | Klusgebied - Vloer, Muur & Dakisolatie");
  const navigate = useNavigate();

  const insulationServices = [
    {
      icon: ThermometerSun,
      title: "Spouwmuurisolatie",
      description:
        "Vullen van de spouwmuur met isolatiemateriaal voor een warmer huis.",
      points: [
        "Meest effectieve manier om energie te besparen.",
        "Terugverdientijd van slechts 3-5 jaar.",
        "Installatie vaak binnen één dag, zonder overlast.",
        "Gebruik van hoogwaardige EPS-parels of glaswol.",
      ],
    },
    {
      icon: ThermometerSun,
      title: "Vloerisolatie",
      description:
        "Isoleren van de kruipruimte om koude voeten en vochtproblemen te voorkomen.",
      points: [
        "Voelbaar warmer aan de voeten.",
        "Voorkomt vocht en schimmel vanuit de kruipruimte.",
        "Verhoogt het wooncomfort aanzienlijk.",
        "Diverse materialen mogelijk (PUR, isolatiekussens).",
      ],
    },
    {
      icon: ThermometerSun,
      title: "Dakisolatie",
      description:
        "Voorkom dat warmte ontsnapt via het dak, de grootste bron van warmteverlies.",
      points: [
        "Bespaar tot 30% op uw stookkosten.",
        "Maakt uw zolder een comfortabele leefruimte.",
        "Isolatie van binnenuit of buitenaf mogelijk.",
        "Verhoogt de waarde van uw woning.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Geluidsisolatie",
      description:
        "Verminder overlast van buren of verkeer voor meer rust in huis.",
      points: [
        "Effectieve oplossingen voor muren, vloeren en plafonds.",
        "Creëer een rustige en vredige leefomgeving.",
        "Verhoogt uw privacy en woonplezier.",
        "Advies op maat voor uw specifieke situatie.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <TrendingUp className="w-8 h-8 text-white" />,
      title: "Lagere Energierekening",
      description: "Bespaar tot honderden euro's per jaar op uw stookkosten.",
    },
    {
      icon: <ThermometerSun className="w-8 h-8 text-white" />,
      title: "Meer Wooncomfort",
      description:
        "Een warmer huis in de winter en een koeler huis in de zomer.",
    },
    {
      icon: <Leaf className="w-8 h-8 text-white" />,
      title: "Duurzaam & Milieuvriendelijk",
      description:
        "Verlaag uw CO2-uitstoot en verhoog de waarde van uw woning.",
    },
  ];

  const faqs = [
    {
      question: "Wat is de terugverdientijd van isolatie?",
      answer:
        "De terugverdientijd is afhankelijk van het type isolatie en uw energieverbruik, maar ligt gemiddeld tussen de 3 en 7 jaar. Spouwmuurisolatie verdient zich het snelst terug.",
    },
    {
      question: "Kom ik in aanmerking voor subsidie?",
      answer:
        "Ja, de overheid biedt vaak landelijke en gemeentelijke subsidies voor isolatiemaatregelen. Onze specialisten kunnen u hierover informeren.",
    },
    {
      question: "Geeft isolatie overlast tijdens de installatie?",
      answer:
        "De meeste isolatiewerkzaamheden, zoals spouwmuur- en vloerisolatie, worden van buitenaf uitgevoerd en geven minimale overlast. Vaak is de klus binnen één dag geklaard.",
    },
    {
      question: "Welk type isolatie is het beste voor mijn huis?",
      answer:
        "Dit hangt af van het bouwjaar en type van uw woning. Onze adviseur komt graag langs voor een gratis en vrijblijvend advies op maat.",
    },
  ];

  const reviews = [
    {
      name: "Familie Jansen",
      location: "Groningen",
      rating: 5,
      quote:
        "Onze spouwmuur is geïsoleerd en we merkten direct verschil. Het huis blijft veel langer warm. Super investering!",
      highlighted: true,
    },
    {
      name: "Mark de Vries",
      location: "Breda",
      rating: 5,
      quote:
        "Nooit meer koude voeten dankzij de vloerisolatie. De mannen werkten snel en netjes. Zeer tevreden.",
      highlighted: false,
    },
    {
      name: "Linda Hermans",
      location: "Apeldoorn",
      rating: 5,
      quote:
        "De zolder is nu een volwaardige werkkamer dankzij de dakisolatie. Het is er nu aangenaam in zomer en winter.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1631277190979-1704e8c7d574?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwaW5zdGFsbGF0aW9uJTJDJTIwd2FsbCUyMGluc3VsYXRpb24lMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQwODA2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1630973268659-7c85e88b85ee?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxlbmVyZ3klMjBlZmZpY2llbmN5JTJDJTIwaW5zdWxhdGlvbiUyMGJlbmVmaXRzJTJDJTIwY296eSUyMGhvbWV8ZW58MHx8fHwxNzUxNzQwODA2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1670860976490-476dd4ebc621?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwbWF0ZXJpYWxzJTJDJTIwdGhlcm1hbCUyMGluc3VsYXRpb24lMkMlMjBzdXN0YWluYWJsZSUyMGxpdmluZ3xlbnwwfHx8fDE3NTE3NDA4MDZ8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Vraag advies aan",
      description:
        "Beschrijf uw woning en uw wensen. Wij geven een eerste indicatie en advies.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes",
      description:
        "Gecertificeerde isolatiebedrijven sturen u een offerte op maat na een inspectie.",
      microcopy: "Duidelijkheid over kosten en besparing",
    },
    {
      icon: UserCheck,
      title: "Kies & laat isoleren",
      description:
        "Kies de beste specialist, plan de werkzaamheden en begin direct met besparen.",
      microcopy: "Vergelijk en kies de beste deal",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Isolatiespecialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "isolatiespecialist",
    color: "green",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Isolatie Nodig? | Klusgebied - Vloer, Muur & Dakisolatie</title>
        <meta
          name="description"
          content="Bespaar direct op uw energierekening en verhoog uw wooncomfort met professionele vloer-, muur- of dakisolatie."
        />
      </Helmet>
      <main>
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-green-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-green-100 border border-green-200/80 rounded-full px-4 py-2 mb-6">
                    <ThermometerSun className="w-5 h-5 text-green-600" />
                    <span className="text-green-800 font-semibold text-sm">
                      Isolatie Service
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Woning Isoleren?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-teal-500 mt-2">
                      Bespaar & Verduurzaam
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Bespaar direct op uw energierekening en verhoog uw
                    wooncomfort met professionele vloer-, muur- of dakisolatie.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/huisisolatie")}
                      className="group inline-flex items-center justify-center bg-green-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
                    >
                      Vind een isolatiespecialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Isolatiemateriaal wordt aangebracht in een muur"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Laat uw woning isoleren in 3 simpele stappen.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-green-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Isolatie Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Voor elke situatie de juiste isolatieoplossing.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {insulationServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-green-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-green-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost isolatie?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van het type isolatie en de
                oppervlakte. Vraag een vrijblijvende offerte aan voor een prijs
                op maat.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Spouwmuurisolatie:{" "}
                    <strong className="text-slate-900">€15–€25</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vloerisolatie:{" "}
                    <strong className="text-slate-900">€20–€40</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Dakisolatie:{" "}
                    <strong className="text-slate-900">€40–€70</strong> per m²
                  </span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/plaats-een-klus/huisisolatie")}
                className="bg-green-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun woning lieten isoleren.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-green-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-green-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-green-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-green-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Voordelen van Isoleren
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Een slimme investering in uw portemonnee, comfort en het milieu.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-green-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-gradient-to-r from-green-500 to-teal-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om te besparen?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend isolatieadvies aan en ontdek hoeveel u kunt
              besparen op uw energierekening.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/huisisolatie")}
              className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu advies aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/huisisolatie")}
          className="w-full group inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_IsolatiemonteurPage;
