/**
 * @description This component renders a comprehensive and SEO-optimized detail page for plumber services in Amsterdam. It features a dynamic hero section, detailed service descriptions, geo-targeted content for Amsterdam neighborhoods, a pricing table, customer testimonials, and multiple strong calls-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for plumbing services in Amsterdam.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import usePageTitle from "@/hooks/usePageTitle";
import {
  Wrench,
  ShieldCheck,
  Clock,
  ArrowRight,
  Droplets,
  Pipette,
  Thermometer,
  Star,
} from "lucide-react";
import FAQSection from "@/components/landing/FAQSection";

const Service_LoodgieterPage = () => {
  usePageTitle(
    "Loodgieter Amsterdam | Klusgebied - Direct Hulp bij Lekkage & Sanitair"
  );
  const navigate = useNavigate();

  const plumberServices = [
    {
      icon: Pipette,
      title: "Lekkage Opsporen & Repareren",
      description:
        "Van een druppelende kraan in de Jordaan tot een leidingbreuk in Zuid. Wij sporen elke lekkage op zonder onnodig hak- en breekwerk.",
      imageUrl:
        "https://heyboss.heeyo.ai/1752189266-9ceffe0d-insideandoutpropertyinspectors.com-wp-content-uploads-2021-05-Leaky-pipe1.jpeg",
    },
    {
      icon: Wrench,
      title: "Sanitair Installatie & Renovatie",
      description:
        "Een nieuwe badkamer in De Pijp of een toilet vervangen in Noord? Onze loodgieters installeren al uw sanitair vakkundig.",
      imageUrl:
        "https://heyboss.heeyo.ai/1752189267-9f5cecc0-4612653.fs1.hubspotusercontent-na1.net-hub-4612653-hubfs-Featured-20Project-20Photos-BATHROOM1-width-640-name-BATHROOM1.png",
    },
    {
      icon: Thermometer,
      title: "CV-ketel & Verwarming",
      description:
        "Storing aan uw CV in Centrum of onderhoud nodig in West? Onze monteurs staan voor u klaar voor een warm en comfortabel huis.",
      imageUrl:
        "https://heyboss.heeyo.ai/1752189267-a2bcd381-www.ahs.com-contentassets-beb606c2c7a0416996d9e00d11f0e908-contractorwh1rgb-new.png",
    },
    {
      icon: Droplets,
      title: "Afvoer & Riool Ontstoppen",
      description:
        "Een verstopte gootsteen in uw grachtenpand of een rioolprobleem in IJburg? Wij lossen elke verstopping snel en professioneel op.",
      imageUrl:
        "https://heyboss.heeyo.ai/1752189266-96212bfe-gopaschal.com-app-uploads-2021-11-Unclog-a-Sink.jpeg",
    },
  ];

  const benefits = [
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "24/7 Spoedservice in Amsterdam",
      description:
        "Dag en nacht bereikbaar voor urgente lekkages en verstoppingen in de hele stad.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Gecertificeerde Vakmannen",
      description:
        "Al onze Amsterdamse loodgieters zijn erkend en hebben jarenlange ervaring.",
    },
    {
      icon: <Wrench className="w-8 h-8 text-white" />,
      title: "Garantie op Werk",
      description:
        "Wij staan achter ons werk en bieden garantie op alle reparaties en installaties.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een loodgieter in Amsterdam?",
      answer:
        "Het uurtarief van een loodgieter in Amsterdam ligt gemiddeld tussen de €60 en €85, afhankelijk van de klus en het tijdstip. Voor spoedklussen kan een toeslag gelden. Vraag altijd een offerte aan via Klusgebied voor een duidelijke prijsopgave.",
    },
    {
      question: "Hoe snel kan een loodgieter in Amsterdam ter plaatse zijn?",
      answer:
        "Bij spoedgevallen zoals een grote lekkage streven we ernaar om binnen 1 uur een loodgieter ter plaatse te hebben in Amsterdam en omstreken.",
    },
    {
      question:
        "Mijn afvoer is verstopt in mijn Amsterdamse appartement, wat nu?",
      answer:
        "Probeer eerst de verstopping te verhelpen met een plopper. Vermijd agressieve chemicaliën, deze kunnen oude leidingen in Amsterdamse panden beschadigen. Lukt het niet, schakel dan een professional in.",
    },
    {
      question:
        "Ik heb loden leidingen in mijn huis in Amsterdam, wat moet ik doen?",
      answer:
        "Loden leidingen kunnen schadelijk zijn voor de gezondheid. Het is sterk aan te raden deze te laten vervangen. Onze loodgieters zijn gespecialiseerd in het veilig verwijderen en vervangen van loden leidingen.",
    },
  ];

  const reviews = [
    {
      name: "Anja de Wit",
      location: "De Pijp, Amsterdam",
      rating: 5,
      quote:
        "De loodgieter was er binnen een uur voor een lekkage. Super snel en vakkundig opgelost. Een echte aanrader voor iedereen in Amsterdam!",
      image:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Peter Verhoeven",
      location: "Amsterdam-Noord",
      rating: 5,
      quote:
        "Mijn nieuwe badkamer is prachtig geïnstalleerd. De vakman dacht goed mee over de beperkte ruimte in mijn appartement en werkte heel netjes.",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Familie El Amrani",
      location: "Amsterdam-West",
      rating: 4,
      quote:
        "Goed en betrouwbaar. De afvoer was snel ontstopt en de prijs was eerlijk. Volgende keer weer via Klusgebied.",
      image:
        "https://images.unsplash.com/photo-1678380326532-89e3ea5c5ce4?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx5b3VuZyUyMGZhbWlseSUyQyUyMHBvcnRyYWl0JTJDJTIwaGFwcHl8ZW58MHx8fHwxNzUxNDcyMDQ0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const amsterdamNeighborhoods = [
    {
      name: "Centrum",
      image:
        "https://heyboss.heeyo.ai/1752189266-10e60a3b-upload.wikimedia.org-wikipedia-commons-1-15-Amsterdam-ist-auch-eine-sehr-gr-C3-BCne-Stadt.-panoramio.jpg",
      description:
        "Lekkages in historische grachtenpanden en moderne appartementen.",
    },
    {
      name: "De Pijp",
      image:
        "https://heyboss.heeyo.ai/1752189266-b5ba9aca-assets.vogue.com-photos-58913999736d5f2410e37356-master-w-2560-2Cc-limit-0-holding-de-pijp-amsterdam-travel-guide.jpg",
      description: "Sanitair installaties en het vervangen van oude leidingen.",
    },
    {
      name: "Amsterdam-West",
      image:
        "https://heyboss.heeyo.ai/1752189267-84fe663d-upload.wikimedia.org-wikipedia-commons-1-10-Amsterdam-kattensloot.JPG",
      description: "CV-ketel onderhoud en het oplossen van verstoppingen.",
    },
    {
      name: "Amsterdam-Noord",
      image:
        "https://heyboss.heeyo.ai/1752189268-b5fcf691-upload.wikimedia.org-wikipedia-commons-9-9d-Sint-Augustinuskerk.jpg",
      description: "Complete badkamerrenovaties en nieuwbouwprojecten.",
    },
    {
      name: "IJburg",
      image:
        "https://heyboss.heeyo.ai/1752189267-ba28b579-upload.wikimedia.org-wikipedia-commons-e-ee-IJburg-West-2C-Amsterdam-2C-Netherlands-panoramio-288-29.jpg",
      description: "Installatie van moderne verwarmingssystemen en sanitair.",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Loodgieter Amsterdam | Klusgebied - Direct Hulp bij Lekkage & Sanitair
        </title>
        <meta
          name="description"
          content="Loodgieter nodig in Amsterdam? Klusgebied verbindt je direct met geverifieerde vakmannen voor lekkages, CV-storingen, sanitair en verstoppingen. 24/7 spoedservice."
        />
        <link
          rel="canonical"
          href="https://www.klusgebied.nl/#/diensten/loodgieter"
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 lg:pt-48 lg:pb-28 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://heyboss.heeyo.ai/1752189267-ca72c0a3-eastwick.edu-wp-content-uploads-2024-09-Eastwick-BlogArticle-Sept2024-Unlocking-Career-Success-The-Path-of-a-Plumber-Apprentice.png"
              alt="Loodgieter in Amsterdam aan het werk"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-slate-900/60"></div>
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="motion-preset-fade-in-down">
              <div className="inline-flex items-center justify-center bg-white/10 backdrop-blur-sm p-4 rounded-2xl mb-6">
                <Droplets className="h-12 w-12 text-teal-300" />
              </div>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight">
                Loodgieter nodig in Amsterdam?
              </h1>
              <p className="mt-6 max-w-3xl mx-auto text-lg sm:text-xl text-slate-200">
                Van een lekkage in een grachtenpand tot een nieuwe badkamer in
                Noord. Klusgebied verbindt je direct met de beste geverifieerde
                loodgieters in jouw buurt.
              </p>
              <div className="mt-10">
                <button
                  onClick={() =>
                    navigate("/plaats-een-klus/waterleiding-vervangen")
                  }
                  className="bg-teal-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/30 hover:-translate-y-1 inline-flex items-center"
                >
                  Plaats mijn loodgietersklus{" "}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Why Klusgebied Section */}
        <section className="py-20 lg:py-28 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-4">
                Waarom Klusgebied voor uw loodgieter in Amsterdam?
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                In een stad als Amsterdam wil je zekerheid. Geen gedoe, geen
                verrassingen, gewoon goed werk.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="text-center p-8 bg-slate-50 rounded-2xl shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300 motion-preset-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="inline-block bg-teal-100 text-teal-500 p-4 rounded-full mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-slate-800 mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-slate-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              ))}
              \
            </div>
          </div>
        </section>

        {/* Popular Jobs Section */}
        <section className="py-20 lg:py-28 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-4">
                Populaire loodgietersklussen in Amsterdam
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Onze Amsterdamse vakmensen hebben ervaring met elke uitdaging
                die de stad biedt.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {plumberServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden group flex flex-col md:flex-row motion-preset-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <img
                    src={service.imageUrl}
                    alt={service.title}
                    className="w-full md:w-1/3 h-48 md:h-auto object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="p-6 flex flex-col justify-center">
                    <service.icon className="w-8 h-8 text-teal-500 mb-3" />
                    <h3 className="text-xl font-bold text-slate-800 mb-2">
                      {service.title}
                    </h3>
                    <p className="text-slate-600">{service.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Table Section */}
        <section className="py-20 lg:py-28 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-4">
                Wat kost een loodgieter in Amsterdam?
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Transparante prijzen, zodat u weet waar u aan toe bent. Geen
                verrassingen achteraf.
              </p>
            </div>
            <div className="bg-slate-50 rounded-2xl shadow-xl p-8">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-b-2 border-slate-200">
                    <th className="p-4 text-lg font-bold text-slate-800">
                      Dienst
                    </th>
                    <th className="p-4 text-lg font-bold text-slate-800">
                      Indicatieprijs (incl. BTW)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-slate-200">
                    <td className="p-4">Uurtarief loodgieter</td>
                    <td className="p-4 font-semibold">€60 - €85</td>
                  </tr>
                  <tr className="border-b border-slate-200">
                    <td className="p-4">Spoedtoeslag (avond/weekend)</td>
                    <td className="p-4 font-semibold">+ 50% - 100%</td>
                  </tr>
                  <tr className="border-b border-slate-200">
                    <td className="p-4">Lekkage opsporen</td>
                    <td className="p-4 font-semibold">
                      Vanaf €350 (vaak vergoed)
                    </td>
                  </tr>
                  <tr className="border-b border-slate-200">
                    <td className="p-4">Afvoer ontstoppen</td>
                    <td className="p-4 font-semibold">€90 - €150</td>
                  </tr>
                  <tr className="border-b border-slate-200">
                    <td className="p-4">CV-ketel onderhoud</td>
                    <td className="p-4 font-semibold">€80 - €120</td>
                  </tr>
                  <tr>
                    <td className="p-4">Toilet of kraan vervangen</td>
                    <td className="p-4 font-semibold">
                      €150 - €300 (excl. materiaal)
                    </td>
                  </tr>
                </tbody>
              </table>
              <p className="text-sm text-slate-500 mt-4">
                *Dit zijn indicatieve prijzen. De uiteindelijke prijs is
                afhankelijk van de specifieke situatie. Vraag altijd een offerte
                aan.
              </p>
            </div>
          </div>
        </section>

        {/* Neighborhoods Section */}
        <section className="py-20 lg:py-28 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-4">
                Actief in heel Amsterdam
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Of u nu in een historisch grachtenpand woont of in een modern
                appartement, onze loodgieters kennen de stad.
              </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {amsterdamNeighborhoods.map((hood) => (
                <div
                  key={hood.name}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden group transform hover:-translate-y-2 transition-transform duration-300"
                >
                  <img
                    src={hood.image}
                    alt={`Loodgieter in ${hood.name}`}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-2xl font-bold text-slate-800 mb-2">
                      {hood.name}
                    </h3>
                    <p className="text-slate-600">{hood.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Customer Experiences */}
        <section className="py-20 lg:py-28 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-4">
                Ervaringen van Amsterdammers
              </h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Echte verhalen van uw buren.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {reviews.map((review, index) => (
                <div
                  key={index}
                  className="bg-slate-50 p-8 rounded-2xl shadow-lg flex flex-col"
                >
                  <div className="flex items-center mb-4">
                    <img
                      src={review.image}
                      alt={review.name}
                      className="w-14 h-14 rounded-full object-cover mr-4"
                    />
                    <div>
                      <div className="font-bold text-slate-800">
                        {review.name}
                      </div>
                      <div className="text-sm text-slate-500">
                        {review.location}
                      </div>
                    </div>
                  </div>
                  <div className="flex mb-4">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-5 h-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>
                  <p className="text-slate-700 italic flex-grow">
                    "{review.quote}"
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <FAQSection faqs={faqs} />

        {/* Final CTA */}
        <section className="bg-teal-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Direct een loodgieter in Amsterdam nodig?
            </h2>
            <p className="text-lg text-teal-100 mb-8 max-w-2xl mx-auto">
              Wacht niet langer op een oplossing. Plaats uw klus gratis en
              ontvang binnen enkele uren reacties van de beste lokale vakmensen.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/waterleiding-vervangen")
                }
                className="bg-white text-teal-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
              >
                Plaats mijn loodgietersklus
              </button>
              <button
                onClick={() => navigate("/app")}
                className="bg-white/20 text-white px-8 py-4 rounded-xl font-bold hover:bg-white/30 transition-all duration-300 shadow-lg text-lg"
              >
                Download de app
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Service_LoodgieterPage;
