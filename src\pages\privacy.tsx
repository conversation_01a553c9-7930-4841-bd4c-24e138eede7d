import { useNavigate } from "react-router-dom";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Privacy() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#f8fafc] to-[#e0f7fa] flex items-center justify-center px-4 py-12">
      <Card className="max-w-2xl w-full p-8 shadow-xl rounded-2xl bg-white/90">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 tracking-tight">
          Privacyverklaring
        </h1>
        <p className="text-gray-600 mb-6 text-lg">
          Jouw privacy is belangrijk voor ons. In deze privacyverklaring lees je
          hoe wij omgaan met jouw gegevens binnen het Klus platform.
        </p>
        <section className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Welke g<PERSON><PERSON><PERSON> verzamelen wij?
          </h2>
          <ul className="list-disc pl-6 text-gray-700 space-y-1">
            <li>Naam, e-mailadres en contactgegevens</li>
            <li>Adresgegevens voor het uitvoeren van klussen</li>
            <li>Betaalgegevens voor transacties</li>
            <li>Profielinformatie en portfolio</li>
            <li>Communicatie via het platform</li>
            <li>Gebruiksgegevens en analytische data</li>
          </ul>
        </section>
        <section className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Waarvoor gebruiken wij jouw gegevens?
          </h2>
          <ul className="list-disc pl-6 text-gray-700 space-y-1">
            <li>Om het platform veilig en betrouwbaar te laten functioneren</li>
            <li>Voor communicatie tussen gebruikers</li>
            <li>Voor het uitvoeren van betalingen en facturatie</li>
            <li>Voor klantenservice en ondersteuning</li>
            <li>Voor het verbeteren van onze diensten</li>
            <li>Om te voldoen aan wettelijke verplichtingen</li>
          </ul>
        </section>
        <section className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Delen wij jouw gegevens?
          </h2>
          <p className="text-gray-700">
            Wij delen jouw gegevens alleen met derden als dat nodig is voor het
            uitvoeren van onze dienstverlening (zoals betaalproviders) of als
            wij daartoe wettelijk verplicht zijn. Wij verkopen jouw gegevens
            nooit aan derden.
          </p>
        </section>
        <section className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Jouw rechten
          </h2>
          <ul className="list-disc pl-6 text-gray-700 space-y-1">
            <li>Inzage in jouw gegevens</li>
            <li>Gegevens laten corrigeren of verwijderen</li>
            <li>Bezwaar maken tegen verwerking</li>
            <li>Gegevensoverdraagbaarheid</li>
          </ul>
          <p className="text-gray-700 mt-2">
            Neem contact met ons op via{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-[#40cfc1] underline"
            >
              <EMAIL>
            </a>{" "}
            voor privacy-gerelateerde verzoeken.
          </p>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Beveiliging
          </h2>
          <p className="text-gray-700">
            Wij nemen passende technische en organisatorische maatregelen om
            jouw gegevens te beschermen tegen verlies of onrechtmatig gebruik.
          </p>
        </section>
        <div className="flex justify-end">
          <Button variant="outline" onClick={() => navigate(-1)}>
            Terug
          </Button>
        </div>
      </Card>
    </div>
  );
}
