/**
 * @description This component renders a dedicated landing page for the CV Ketel (Boiler) Maintenance Contract. It provides detailed information about three selectable contract tiers, allowing users to compare benefits, features, and pricing dynamically. The page is designed with a world-class, conversion-focused layout, is fully responsive, and displays all contract options side-by-side on desktop for easy comparison. Key variables include contractsData for storing contract details and navigation handlers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  ArrowLeft,
  Heater,
  Check,
  X,
  Star,
  ShieldCheck,
  TrendingUp,
  Wrench,
} from "lucide-react";

const contractsData = [
  {
    name: "Klusgebied CV Comfort",
    description:
      "Voor particuliere woningeigenaren of huurders die hun CV-ketel jaarlijks willen laten onderhouden voor veiligheid, prestaties en wettelijke dekking, zonder extra service of dekking bij storingen.",
    price: "15,95",
    included: [
      "1x per jaar preventieve onderhoudsbeurt",
      "Reiniging van alle vitale onderdelen",
      "Afstelling met CO-/CO₂-analyse",
      "Drukcontrole en inspectie expansievat",
      "Visuele inspectie rookgasafvoer",
      "Digitale onderhoudsrapportage",
      "Onderhoudsherinnering per e-mail/sms",
      "Geen voorrijkosten bij onderhoud",
    ],
    excluded: [
      "24/7 storingshulp",
      "Reparatie van defecten",
      "Spoedhulp buiten planning",
    ],
    isMostChosen: false,
    themeColor: "bg-orange-500 hover:bg-orange-600",
  },
  {
    name: "Klusgebied CV Zeker",
    description:
      "Voor gezinnen of huiseigenaren die een betrouwbare oplossing zoeken voor zowel preventief onderhoud als directe hulp bij storingen, zonder zorgen over kleine reparaties.",
    price: "39,95",
    included: [
      "Alle voordelen van CV Comfort",
      "24/7 storingsdienst (binnen 24u)",
      "1x per jaar gratis kleine reparatie (€200 waarde)",
      "Volledige rookgasafvoerinspectie",
      "Prioriteitsplanning in de winter",
      "Geen voorrijkosten bij storingen",
      "Telefonische helpdesk voor advies",
    ],
    excluded: [
      "Vervanging van dure onderdelen",
      "Onbeperkt aantal storingsbezoeken",
      "Slimme thermostaat of energieanalyse",
    ],
    isMostChosen: true,
    themeColor: "bg-teal-500 hover:bg-teal-600",
  },
  {
    name: "Klusgebied CV All-in Wonen",
    description:
      "Voor particulieren die volledige technische ontzorging willen: onderhoud, storingen, onderdelen én vervangservice. Ideaal voor wie comfort en continuïteit boven alles stelt.",
    price: "79,95",
    included: [
      "Alle voordelen van CV Zeker",
      "2x per jaar onderhoudsbeurt",
      "Spoedservice binnen 12 uur",
      "Volledige dekking onderdelen & arbeid",
      "Rookgasafvoer vervanging (1x/5j)",
      "Gratis slimme thermostaat",
      "Energieprestatieanalyse",
      "Gratis vervangtoestel bij uitval",
    ],
    excluded: [],
    isMostChosen: false,
    themeColor: "bg-orange-500 hover:bg-orange-600",
  },
];

const CvKetelContractPage = () => {
  usePageTitle("Onderhoudscontract CV-ketel | Klusgebied");
  const navigate = useNavigate();

  const comparisonData = [
    {
      feature: "Aantal onderhoudsbeurten",
      comfort: "1x per jaar",
      zeker: "1x per jaar",
      allin: "2x per jaar",
    },
    {
      feature: "Storingsdienst",
      comfort: { icon: "X" },
      zeker: { icon: "Check", text: "binnen 24u" },
      allin: { icon: "Check", text: "binnen 12u" },
    },
    {
      feature: "Kleine reparaties",
      comfort: { icon: "X" },
      zeker: { icon: "Check", text: "tot €200" },
      allin: { icon: "Check", text: "onbeperkt" },
    },
    {
      feature: "Onderdelen & arbeid inbegrepen",
      comfort: { icon: "X" },
      zeker: { icon: "X" },
      allin: { icon: "Check", text: "volledig" },
    },
    {
      feature: "Rookgasafvoerinspectie",
      comfort: "Visueel",
      zeker: "Volledig",
      allin: "+ Vervanging (1x/5j)",
    },
    {
      feature: "Voorrijkosten",
      comfort: { icon: "Check", text: "gepland" },
      zeker: { icon: "Check", text: "altijd" },
      allin: { icon: "Check", text: "altijd" },
    },
    {
      feature: "Slimme thermostaat",
      comfort: { icon: "X" },
      zeker: { icon: "X" },
      allin: { icon: "Check", text: "gratis (jaarbetaling)" },
    },
    {
      feature: "Energieverbruikanalyse",
      comfort: { icon: "X" },
      zeker: { icon: "X" },
      allin: { icon: "Check", text: "jaarlijks" },
    },
    {
      feature: "Vervangtoestel bij langdurige storing",
      comfort: { icon: "X" },
      zeker: { icon: "X" },
      allin: { icon: "Check", text: "inbegrepen" },
    },
    {
      feature: "Prijs per maand",
      comfort: "€15,95",
      zeker: "€39,95",
      allin: "€79,95",
    },
    {
      feature: "Jaarprijs",
      comfort: "€191,40",
      zeker: "€479,40",
      allin: "€959,40",
    },
  ];

  const renderCell = (data) => {
    if (typeof data === "string") {
      return <span className="text-slate-700">{data}</span>;
    }
    if (typeof data === "object" && data.icon) {
      return (
        <div className="flex items-center justify-center">
          {data.icon === "Check" ? (
            <Check className="w-6 h-6 text-teal-500" />
          ) : (
            <X className="w-6 h-6 text-red-400" />
          )}
          {data.text && (
            <span className="ml-2 text-slate-600 text-sm">{data.text}</span>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative py-20 lg:py-24 bg-slate-800 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1565163255712-3752009dfd6c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxib2lsZXIlMkMlMjBjb250cmFjdHxlbnwwfHx8fDE3NTE3Mjc1NDZ8MA&ixlib=rb-4.1.0&w=1920&h=1080&fit=crop"
              alt="CV Ketel onderhoud"
              className="w-full h-full object-cover opacity-20"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900 to-slate-800/50"></div>
          </div>
          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <button
              onClick={() => navigate("/")}
              className="flex items-center space-x-2 text-white/80 hover:text-white mb-8 transition-all duration-300 hover:scale-105 mx-auto"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-medium">Terug naar home</span>
            </button>
            <Heater className="w-16 h-16 text-teal-400 mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              Onderhoudscontract CV-ketel
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-slate-300 leading-relaxed">
              Kies het contract dat bij u past. Zekerheid en comfort met
              professioneel onderhoud.
            </p>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 lg:py-24">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
              {contractsData.map((contract, index) => (
                <div
                  key={contract.name}
                  className={`relative bg-white rounded-3xl shadow-lg transition-all duration-500 h-full flex flex-col group motion-preset-slide-up motion-delay-${
                    index * 100
                  } ${
                    contract.isMostChosen
                      ? "border-4 border-teal-500 scale-100 lg:scale-105 shadow-2xl shadow-teal-500/30 z-10"
                      : "border-2 border-slate-100 hover:shadow-2xl hover:scale-102"
                  }`}
                >
                  {contract.isMostChosen && (
                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-bold inline-flex items-center space-x-2 shadow-lg">
                      <Star className="w-4 h-4" />
                      <span>Meest Gekozen</span>
                    </div>
                  )}
                  <div className="p-8 flex flex-col flex-grow">
                    <div className="text-center mb-6">
                      <h2 className="text-2xl font-bold text-slate-800 mb-2">
                        {contract.name.replace("Klusgebied CV ", "")}
                      </h2>
                      <p className="text-slate-600 text-sm min-h-[6rem]">
                        {contract.description}
                      </p>
                    </div>

                    <div className="text-center my-4">
                      <div className="inline-block bg-orange-100/50 rounded-full p-4">
                        <Heater className="w-12 h-12 text-orange-500" />
                      </div>
                    </div>

                    <div className="text-center text-4xl font-bold text-slate-900 mb-2">
                      €{contract.price}
                      <span className="text-lg font-normal text-slate-500">
                        {" "}
                        /maand
                      </span>
                    </div>
                    <p className="text-center text-xs text-slate-400 mb-8">
                      Exclusief BTW, jaarlijks gefactureerd.
                    </p>

                    <div className="mt-auto mb-8 pt-6 border-t border-slate-200 flex-grow">
                      <h3 className="text-base font-semibold text-center mb-4 text-slate-700">
                        Wat is inbegrepen?
                      </h3>
                      <ul className="space-y-3">
                        {contract.included.map((item) => (
                          <li key={item} className="flex items-start text-sm">
                            <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-slate-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                      {contract.excluded.length > 0 && (
                        <>
                          <h3 className="text-base font-semibold text-center mb-4 mt-6 text-slate-700">
                            Wat is niet inbegrepen?
                          </h3>
                          <ul className="space-y-3">
                            {contract.excluded.map((item) => (
                              <li
                                key={item}
                                className="flex items-start text-sm"
                              >
                                <X className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                                <span className="text-slate-500 opacity-90">
                                  {item}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </div>

                    <button
                      className={`w-full text-white font-bold py-3 rounded-xl text-lg transition-all duration-300 shadow-lg hover:scale-105 ${contract.themeColor}`}
                    >
                      Kies {contract.name.replace("Klusgebied CV ", "")}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Waarom is CV-ketel onderhoud essentieel?
              </h2>
              <p className="text-lg text-slate-600 leading-relaxed">
                Regelmatig onderhoud aan uw CV-ketel is geen overbodige luxe.
                Het is cruciaal voor de veiligheid, efficiëntie en levensduur
                van uw installatie. Een goed onderhouden ketel verbruikt minder
                energie, wat u direct terugziet op uw energierekening, en
                verkleint de kans op gevaarlijke situaties zoals
                koolmonoxidelekkage.
              </p>
            </div>
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="p-6">
                <ShieldCheck className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Veiligheid
                </h3>
                <p className="text-slate-600">
                  Voorkom koolmonoxidevergiftiging en andere risico's door een
                  jaarlijkse controle.
                </p>
              </div>
              <div className="p-6">
                <TrendingUp className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Efficiëntie
                </h3>
                <p className="text-slate-600">
                  Een schone en goed afgestelde ketel verbruikt tot 15% minder
                  energie.
                </p>
              </div>
              <div className="p-6">
                <Wrench className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Levensduur
                </h3>
                <p className="text-slate-600">
                  Verleng de levensduur van uw CV-ketel en voorkom dure,
                  onverwachte reparaties.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Vergelijk onze CV-ketel contracten
              </h2>
              <p className="text-lg text-slate-600 max-w-3xl mx-auto">
                Vind het onderhoudscontract dat perfect aansluit bij uw wensen
                en budget. Hieronder ziet u alle details overzichtelijk naast
                elkaar.
              </p>
            </div>
            <div className="overflow-x-auto bg-white rounded-2xl shadow-xl border border-slate-200/80">
              <table className="w-full min-w-[1000px] text-left border-collapse">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="p-6 bg-slate-50 text-lg font-bold text-slate-700 rounded-tl-2xl">
                      Functie
                    </th>
                    <th className="p-6 bg-slate-50 text-lg font-bold text-slate-700 text-center">
                      CV Comfort
                    </th>
                    <th className="p-6 bg-teal-500/10 text-lg font-bold text-teal-600 text-center border-x-2 border-teal-500">
                      CV Zeker
                    </th>
                    <th className="p-6 bg-slate-50 text-lg font-bold text-slate-700 text-center rounded-tr-2xl">
                      CV All-in Wonen
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {comparisonData.map((row, index) => (
                    <tr
                      key={index}
                      className="border-b border-slate-200 last:border-b-0 hover:bg-slate-50/50 transition-colors duration-200"
                    >
                      <td className="p-5 font-semibold text-slate-800">
                        {row.feature}
                      </td>
                      <td className="p-5 text-center">
                        {renderCell(row.comfort)}
                      </td>
                      <td className="p-5 text-center bg-teal-500/5 border-x-2 border-teal-500">
                        {renderCell(row.zeker)}
                      </td>
                      <td className="p-5 text-center">
                        {renderCell(row.allin)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default CvKetelContractPage;
