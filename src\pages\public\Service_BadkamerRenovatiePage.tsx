/**
 * @description This component renders a comprehensive and SEO-optimized detail page for bathroom renovation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for bathroom renovations. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Bath,
  CheckSquare,
  <PERSON>lette,
  ArrowRight,
  Star,
  <PERSON>3,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Circle,
  MapPin,
  Euro,
} from "lucide-react";

const Service_BadkamerRenovatiePage = () => {
  usePageTitle("Badkamer Renovatie | Klusgebied - Van A tot Z geregeld");
  const navigate = useNavigate();

  const services = [
    {
      icon: Bath,
      title: "Totaalrenovatie",
      description:
        "Wij nemen de volledige renovatie van uw badkamer uit handen, van sloop tot oplevering.",
      points: [
        "Eén aanspreekpunt voor het hele project.",
        "Inclusief sloop, leidingwerk, tegelwerk en installatie.",
        "Advies over indeling, materialen en sanitair.",
        "Oplevering binnen de afgesproken tijd en budget.",
      ],
    },
    {
      icon: CheckSquare,
      title: "Tegelwerk",
      description:
        "Vakkundig plaatsen van wand- en vloertegels in elk gewenst patroon.",
      points: [
        "Verwerken van alle soorten tegels (keramiek, natuursteen, mozaïek).",
        "Gegarandeerd strak en waterdicht tegelwerk.",
        "Advies over voegkleur en tegelpatronen.",
        "Ook voor het verwijderen van oude tegels.",
      ],
    },
    {
      icon: Palette,
      title: "Sanitair Installatie",
      description:
        "Installatie van toiletten, wastafels, douches, baden en kranen.",
      points: [
        "Vakkundige montage van alle merken sanitair.",
        "Installatie van inloopdouches en vrijstaande baden.",
        "Aansluiten van kranen en douchesets.",
        "Nette en waterdichte afwerking.",
      ],
    },
    {
      icon: Edit3,
      title: "Leidingwerk Aanpassen",
      description:
        "Verleggen en aanpassen van waterleidingen en afvoeren voor een nieuwe indeling.",
      points: [
        "Aanpassen van leidingen voor een nieuwe indeling.",
        "Installatie van nieuwe waterleidingen en afvoeren.",
        "Werken volgens de laatste normen en voorschriften.",
        "Minimale overlast tijdens de werkzaamheden.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <CheckSquare className="w-8 h-8 text-white" />,
      title: "All-in-One Service",
      description:
        "Eén aanspreekpunt voor het gehele project, van ontwerp tot de laatste kitrand.",
    },
    {
      icon: <Palette className="w-8 h-8 text-white" />,
      title: "Design op Maat",
      description:
        "Wij helpen u met het ontwerpen van een badkamer die perfect past bij uw stijl en wensen.",
    },
    {
      icon: <Bath className="w-8 h-8 text-white" />,
      title: "Vaste Prijs & Planning",
      description:
        "Geen verrassingen achteraf. U ontvangt een duidelijke offerte en een realistische planning.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een complete badkamerrenovatie?",
      answer:
        "Een gemiddelde badkamerrenovatie kost tussen de €7.500 en €15.000. De prijs is sterk afhankelijk van de grootte, materiaalkeuze en de complexiteit van het werk.",
    },
    {
      question: "Hoe lang duurt een badkamerrenovatie?",
      answer:
        "Gemiddeld duurt een volledige renovatie 2 tot 3 weken. Dit is inclusief sloop, leidingwerk, tegelen, installatie en afwerking.",
    },
    {
      question: "Kan ik tijdens de verbouwing thuis blijven wonen?",
      answer:
        "Ja, dat is meestal mogelijk. Houd er wel rekening mee dat u tijdelijk geen gebruik kunt maken van de badkamer en er enige overlast van stof en geluid kan zijn.",
    },
    {
      question: "Helpen jullie ook met het ontwerp van de badkamer?",
      answer:
        "Zeker! Onze specialisten denken graag met u mee over een praktische en stijlvolle indeling en helpen u bij het kiezen van de juiste materialen en sanitair.",
    },
  ];

  const reviews = [
    {
      name: "Familie Visser",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Onze droombadkamer is werkelijkheid geworden! Het hele proces was soepel en de communicatie was top. Een absolute aanrader.",
      highlighted: true,
    },
    {
      name: "Linda de Wit",
      location: "Utrecht",
      rating: 5,
      quote:
        "Prachtig tegelwerk en alles perfect geïnstalleerd. De vakman was een echte professional met oog voor detail.",
      highlighted: false,
    },
    {
      name: "Jeroen Bakker",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Van begin tot eind een geweldige ervaring. Duidelijke afspraken en een resultaat dat onze verwachtingen overtrof.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1618236444666-105ec54b5b69?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0aW9uJTJDJTIwYmF0aHJvb218ZW58MHx8fHwxNzUyMTEwMzcxfDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1706670455091-f17bd0809f2d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxyZW5vdmF0aW9uJTJDJTIwYmF0aHJvb218ZW58MHx8fHwxNzUyMTEwMzcxfDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1629969337555-e384ed2d1439?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzYW5pdGFyeSUyMGluc3RhbGxhdGlvbiUyQyUyMHN0eWxpc2glMjBiYXRocm9vbSUyQyUyMG1vZGVybiUyMGZpeHR1cmVzfGVufDB8fHx8MTc1MTc0MjM4MXww&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Deel uw wensen",
      description:
        "Beschrijf uw ideale badkamer en upload eventueel inspiratiefoto's.",
      microcopy: "Vrijblijvend en binnen 2 minuten",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes",
      description:
        "Ontvang voorstellen van gespecialiseerde badkamerinstallateurs.",
      microcopy: "Binnen 48 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start renovatie",
      description:
        "Vergelijk de specialisten en start de transformatie van uw badkamer.",
      microcopy: "Kies op basis van portfolio en reviews",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Badkamer Specialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "badkamer renovatie",
    color: "cyan",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Badkamer Renovatie | Klusgebied - Van A tot Z geregeld</title>
        <meta
          name="description"
          content="Laat uw badkamer renoveren door een ervaren specialist. Van ontwerp tot oplevering, Klusgebied verbindt u met de beste vakmensen voor een droomresultaat."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-cyan-100 border border-cyan-200/80 rounded-full px-4 py-2 mb-6">
                    <Bath className="w-5 h-5 text-cyan-600" />
                    <span className="text-cyan-800 font-semibold text-sm">
                      Badkamer Renovatie
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Uw Droombadkamer{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-500 to-blue-500 mt-2">
                      Gerealiseerd
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Van ontwerp tot oplevering, wij transformeren uw oude
                    badkamer in een oase van rust en comfort.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <button
                    onClick={() =>
                      navigate(
                        "/plaats-een-klus/badkamer-renovatie-installatie"
                      )
                    }
                    className="group inline-flex items-center justify-center bg-cyan-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-cyan-600 transition-all duration-300 shadow-lg hover:shadow-cyan-500/30 transform hover:-translate-y-1"
                  >
                    Start uw badkamerrenovatie
                    <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                  </button>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1618236444666-105ec54b5b69?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0aW9uJTJDJTIwYmF0aHJvb218ZW58MHx8fHwxNzUyMTEwMzcxfDA&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Moderne, luxe badkamer"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Realiseer uw droombadkamer in 3 eenvoudige stappen.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-cyan-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-cyan-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Badkamer Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet pakket voor een zorgeloze renovatie.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-cyan-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-cyan-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-cyan-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een badkamerrenovatie?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van de grootte, materiaalkeuze en
                complexiteit. U ontvangt altijd een gedetailleerde offerte.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Basis renovatie:{" "}
                    <strong className="text-slate-900">€5.000–€10.000</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Gemiddelde renovatie:{" "}
                    <strong className="text-slate-900">€10.000–€17.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Luxe renovatie:{" "}
                    <strong className="text-slate-900">Vanaf €17.500</strong>
                  </span>
                </li>
              </ul>
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/badkamer-renovatie-installatie")
                }
                className="bg-cyan-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-cyan-600 transition-all duration-300 shadow-lg hover:shadow-cyan-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun badkamer lieten renoveren.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-cyan-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-cyan-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-cyan-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-cyan-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een ervaren team en geniet van een vakkundig
                gerenoveerde badkamer.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-cyan-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-cyan-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-cyan-500 to-blue-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor de badkamer van uw dromen?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvende offerte aan en ontdek de mogelijkheden
              voor uw badkamer.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/badkamer-renovatie-installatie")
              }
              className="bg-white text-cyan-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() =>
            navigate("/plaats-een-klus/badkamer-renovatie-installatie")
          }
          className="w-full group inline-flex items-center justify-center bg-cyan-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-cyan-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_BadkamerRenovatiePage;
