import { useRef, useEffect } from "react";

import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageBubble } from "../../chat/MessageBubble";
import { MessageInput } from "../../chat/MessageInput";
import { Message } from "@/types/chat";

interface JobChatUIProps {
  messages: Message[];
  currentUser: any;
  onSendMessage: (message: string) => void;
  jobId: string;
  receiverId: string;
}

export const JobChatUI = ({
  messages,
  currentUser,
  onSendMessage,
  jobId,
  receiverId,
}: JobChatUIProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="space-y-4">
      <ScrollArea className="h-[400px] pr-4">
        <div className="space-y-4">
          {messages.length === 0 ? (
            <p className="text-center text-muted-foreground">
              Nog geen berichten
            </p>
          ) : (
            messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isCurrentUser={message.sender_id === currentUser?.id}
              />
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      <MessageInput
        onSend={onSendMessage}
        jobId={jobId}
        receiverId={receiverId}
      />
    </div>
  );
};
