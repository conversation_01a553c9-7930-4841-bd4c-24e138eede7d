import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import UsersTableRow from "./UsersTableRow";

interface UsersTableProps {
  users: any[];
  loading: boolean;
  onUserClick: (user: any) => void;
  removeUser: (userId: string) => void;
}

export const UsersTable = ({
  users,
  loading,
  onUserClick,
  removeUser,
}: UsersTableProps) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Naam</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Geregistreerd</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Saldo</TableHead>
          <TableHead>Actie</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {loading
          ? Array.from({ length: 5 }).map((_, i) => (
              <TableRow key={i}>
                <TableCell>
                  <Skeleton className="h-4 w-[200px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[200px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[100px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[100px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[100px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[100px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[100px]" />
                </TableCell>
              </TableRow>
            ))
          : users.map((user) => (
              <UsersTableRow
                key={user.id}
                user={user}
                onUserClick={onUserClick}
                removeUser={removeUser}
              />
            ))}
      </TableBody>
    </Table>
  );
};
