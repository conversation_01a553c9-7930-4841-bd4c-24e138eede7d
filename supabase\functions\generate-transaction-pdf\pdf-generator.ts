import { PDFDocument, rgb } from "https://esm.sh/pdf-lib@1.17.1";

// Company constants
const COMPANY_INFO = {
  name: "Klusgebied B.V.",
  address: "Slotermeerlaan 58",
  postalCode: "1064 HC Amsterdam",
  kvk: "97419982",
  vestigingsnr: "000062663151",
  btw: "NL123456789B01",
  email: "<EMAIL>",
  website: "www.klusgebied.nl",
};

export const generateTransactionPDF = async (
  transaction: any,
  profile: any
) => {
  console.log("Starting PDF generation for transaction:", transaction.id);

  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
  const { width, height } = page.getSize();

  let currentY = height - 50;

  // Header - BETALINGSBEWIJS / KWITANTIE
  page.drawText("BETALINGSBEWIJS / KWITANTIE", {
    x: width / 2 - 120,
    y: currentY,
    size: 16,
    color: rgb(0, 0, 0),
  });
  currentY -= 50;

  // Company details section
  currentY = addCompanyDetails(page, currentY);

  // Transaction details section
  currentY = addTransactionInfo(page, currentY, transaction, profile);

  // Payment table
  currentY = addPaymentTable(page, currentY, transaction);

  // Footer text
  addFooterText(page, currentY);

  console.log("PDF generation completed, saving document...");
  return pdfDoc.save();
};

const addCompanyDetails = (page: any, startY: number) => {
  let currentY = startY;
  const leftMargin = 38;

  // Company name
  page.drawText(COMPANY_INFO.name, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Address
  page.drawText(COMPANY_INFO.address, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Postal code and city
  page.drawText(COMPANY_INFO.postalCode, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // KvK number
  page.drawText(`KvK: ${COMPANY_INFO.kvk}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Vestigingsnr
  page.drawText(`Vestigingsnr: ${COMPANY_INFO.vestigingsnr}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // BTW number
  page.drawText(`BTW: ${COMPANY_INFO.btw}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Email
  page.drawText(`E-mail: ${COMPANY_INFO.email}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Website
  page.drawText(`Website: ${COMPANY_INFO.website}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 40;

  return currentY;
};

const addTransactionInfo = (
  page: any,
  startY: number,
  transaction: any,
  profile: any
) => {
  let currentY = startY;
  const leftMargin = 38;

  // Generate invoice number based on transaction ID
  const invoiceNumber = `KB-${new Date(
    transaction.created_at
  ).getFullYear()}${String(
    new Date(transaction.created_at).getMonth() + 1
  ).padStart(2, "0")}${String(
    new Date(transaction.created_at).getDate()
  ).padStart(2, "0")}-${transaction.id.slice(-3)}`;

  // Format date
  const paymentDate = new Date(transaction.created_at).toLocaleDateString(
    "nl-NL",
    {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }
  );

  // Invoice number
  page.drawText(`Factuurnummer: ${invoiceNumber}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Payment date
  page.drawText(`Datum betaling: ${paymentDate}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Payment method
  page.drawText(`Betaalmethode: iDEAL via Mollie`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Reference client
  const clientName = profile
    ? `${profile.first_name} ${profile.last_name}`
    : "Onbekend";
  page.drawText(`Referentie klant: ${clientName} (Tet Solutions)`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  // Client number (using transaction ID)
  page.drawText(`Klantnummer: #${transaction.id.slice(-6)}`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 40;

  // Description section
  page.drawText(`Omschrijving betaling:`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 20;

  page.drawText(
    `Saldo-opwaardering voor gebruik van het Klusgebied platform.`,
    {
      x: leftMargin,
      y: currentY,
      size: 12,
      color: rgb(0, 0, 0),
    }
  );
  currentY -= 20;

  page.drawText(
    `Tegoed wordt toegevoegd aan het vakman-account om te kunnen reageren op geplaatste klussen via de app`,
    {
      x: leftMargin,
      y: currentY,
      size: 12,
      color: rgb(0, 0, 0),
    }
  );
  currentY -= 20;

  page.drawText(`of website.`, {
    x: leftMargin,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0),
  });
  currentY -= 40;

  return currentY;
};

const addPaymentTable = (page: any, startY: number, transaction: any) => {
  let currentY = startY;
  const leftMargin = 38;
  const rowHeight = 25;

  // Table headers
  const headers = ["Omschrijving", "Aantal", "Excl. BTW", "BTW (21%)"];
  const columnWidths = [200, 80, 120, 120];
  let currentX = leftMargin;

  // Draw table header
  headers.forEach((header, index) => {
    page.drawRectangle({
      x: currentX,
      y: currentY - rowHeight,
      width: columnWidths[index],
      height: rowHeight,
      borderColor: rgb(0, 0, 0),
      borderWidth: 1,
    });

    page.drawText(header, {
      x: currentX + 5,
      y: currentY - 15,
      size: 10,
      color: rgb(0, 0, 0),
    });

    currentX += columnWidths[index];
  });

  currentY -= rowHeight;

  // Calculate amounts
  const amount = transaction.amount;
  const btwRate = 0.21;
  const amountExclBtw = amount / (1 + btwRate);
  const btwAmount = amount - amountExclBtw;

  // Table data row
  const rowData = [
    "Saldotegoed opwaardering",
    "1",
    `EUR ${amountExclBtw.toFixed(2)}`,
    `EUR ${btwAmount.toFixed(2)}`,
  ];

  currentX = leftMargin;
  rowData.forEach((data, index) => {
    page.drawRectangle({
      x: currentX,
      y: currentY - rowHeight,
      width: columnWidths[index],
      height: rowHeight,
      borderColor: rgb(0, 0, 0),
      borderWidth: 1,
    });

    page.drawText(data, {
      x: currentX + 5,
      y: currentY - 15,
      size: 10,
      color: rgb(0, 0, 0),
    });

    currentX += columnWidths[index];
  });

  currentY -= rowHeight;

  // Total row
  page.drawRectangle({
    x: leftMargin,
    y: currentY - rowHeight,
    width: 400,
    height: rowHeight,
    borderColor: rgb(0, 0, 0),
    borderWidth: 1,
  });

  page.drawText("Totaal incl. BTW", {
    x: leftMargin + 5,
    y: currentY - 15,
    size: 10,
    color: rgb(0, 0, 0),
  });

  page.drawRectangle({
    x: leftMargin + 400,
    y: currentY - rowHeight,
    width: 120,
    height: rowHeight,
    borderColor: rgb(0, 0, 0),
    borderWidth: 1,
  });

  page.drawText(`EUR ${amount.toFixed(2)}`, {
    x: leftMargin + 405,
    y: currentY - 15,
    size: 10,
    color: rgb(0, 0, 0),
  });

  currentY -= rowHeight * 2;

  return currentY;
};

const addFooterText = (page: any, startY: number) => {
  const leftMargin = 38;
  let currentY = startY;

  // Footer text
  page.drawText(
    "Dit betalingsbewijs is automatisch gegenereerd en dient als bewijs van transactie.",
    {
      x: leftMargin,
      y: currentY,
      size: 10,
      color: rgb(0, 0, 0),
    }
  );
  currentY -= 20;

  page.drawText("Bewaar dit voor je eigen administratie.", {
    x: leftMargin,
    y: currentY,
    size: 10,
    color: rgb(0, 0, 0),
  });
  currentY -= 40;

  page.drawText(
    "Dank voor je opwaardering. Je tegoed is direct beschikbaar in je Klusgebied-account.",
    {
      x: leftMargin,
      y: currentY,
      size: 10,
      color: rgb(0, 0, 0),
    }
  );
  currentY -= 60;

  // Bottom text
  page.drawText(
    "Dit document is automatisch gegenereerd door Klusgebied B.V.",
    {
      x: page.getSize().width / 2 - 150,
      y: 50,
      size: 8,
      color: rgb(0, 0, 0),
    }
  );
};
