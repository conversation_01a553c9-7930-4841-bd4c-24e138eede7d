import { FC, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Shield, ShieldCheck } from "lucide-react";

import { Switch } from "@/components/ui/switch";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { USER_TYPES } from "@/config/routes";

interface MFAToggleProps {
  initialEnabled?: boolean;
  onToggle?: (enabled: boolean) => Promise<void>;
}

const MFAToggle: FC<MFAToggleProps> = ({
  initialEnabled = false,
  onToggle,
}) => {
  const navigate = useNavigate();
  const { userProfile } = useAuth();

  const [enabled, setEnabled] = useState(initialEnabled);
  const [isLoading, setIsLoading] = useState(false);

  // Determine if user is admin
  const isAdmin = userProfile?.user_type === USER_TYPES.ADMIN;

  useEffect(() => {
    checkMFAStatus();
  }, []);

  const checkMFAStatus = async () => {
    try {
      const { data, error } =
        await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
      if (error) throw error;

      setEnabled(data.nextLevel === "aal2");
    } catch (error) {
      console.error("Error checking MFA status:", error);
    }
  };

  const disableMFA = async () => {
    setIsLoading(true);

    try {
      // Fetch MFA factors
      const factors = await supabase.auth.mfa.listFactors();
      if (factors.error) {
        throw factors.error;
      }

      if (isAdmin) {
        // For admin users, handle TOTP factors
        const totpFactor = factors.data.totp[0];
        if (!totpFactor?.id) {
          throw new Error("No valid TOTP factor found");
        }

        // Unenroll the TOTP factor
        const { error: unenrollError } = await supabase.auth.mfa.unenroll({
          factorId: totpFactor.id,
        });

        if (unenrollError) {
          throw unenrollError;
        }
      } else {
        // For non-admin users, handle phone factors
        const phoneFactor = factors.data.phone[0];
        if (!phoneFactor) {
          throw new Error("No phone factors found!");
        }

        // Unenroll the phone factor
        const { error: unenrollError } = await supabase.auth.mfa.unenroll({
          factorId: phoneFactor.id,
        });

        if (unenrollError) {
          throw unenrollError;
        }
      }

      // Refresh session to update authentication state
      await supabase.auth.refreshSession();
    } catch (error) {
      console.error("Failed to disable MFA:", error);
      throw error; // Re-throw to handle in parent component
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggle = async (checked: boolean) => {
    try {
      if (onToggle) {
        await onToggle(checked);
      }
      setEnabled(checked);

      if (checked) {
        navigate("/profiel/mfa");
      } else {
        await disableMFA();
      }
    } catch (error) {
      console.error("Failed to toggle 2FA:", error);
      setEnabled(enabled); // Revert to previous state on error
    }
  };

  return (
    <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border border-gray-100 bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center space-x-3">
        {enabled ? (
          <ShieldCheck className="h-8 w-5 text-green-500" />
        ) : (
          <Shield className="h-5 w-5 text-gray-400" />
        )}
        <div className="space-y-1">
          <h3 className="font-medium text-gray-900">
            Twee-factorauthenticatie
          </h3>
          <p className="text-sm text-gray-500">
            {enabled
              ? "Uw account is beschermd met een extra beveiligingslaag"
              : "Voeg een extra beveiligingslaag toe aan uw account"}
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          checked={enabled}
          onCheckedChange={handleToggle}
          disabled={isLoading}
          aria-label="Toggle 2FA"
        />
        <span className="text-sm font-medium text-gray-700">
          {isLoading
            ? "Updaten..."
            : enabled
            ? "Geactiveerd"
            : "Niet geactiveerd"}
        </span>
      </div>
    </div>
  );
};

export default MFAToggle;
