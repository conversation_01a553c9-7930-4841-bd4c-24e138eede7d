/**
 * @description This component renders a comprehensive and SEO-optimized detail page for CV Ketel (Boiler) services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for boiler services.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Heater,
  ShieldCheck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Wrench,
} from "lucide-react";

const Service_CVKetelServicePage = () => {
  usePageTitle("CV Ketel Service | Klusgebied - Onderhoud & Reparatie");
  const navigate = useNavigate();

  const cvServices = [
    {
      icon: Clock,
      title: "Periodiek Onderhoud",
      description: "Voorkom storingen en verleng de levensduur van uw ketel.",
      points: [
        "Jaarlijkse of tweejaarlijkse controle.",
        "Reinigen van de brander en warmtewisselaar.",
        "Controle op veiligheid en efficiëntie.",
        "Voldoet aan de eisen van verzekeraars.",
      ],
    },
    {
      icon: Wrench,
      title: "24/7 Storingsdienst",
      description: "Direct hulp bij een storing. Dag en nacht bereikbaar.",
      points: [
        "Snelle respons bij acute problemen.",
        "Ervaren monteurs voor alle merken.",
        "Duidelijke communicatie over de reparatie.",
        "We gaan pas weg als het is opgelost.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Reparatie Alle Merken",
      description: "Vakkundige reparatie van alle gangbare CV-ketel merken.",
      points: [
        "Ervaring met Remeha, Intergas, Vaillant, Nefit etc.",
        "Gebruik van originele of hoogwaardige onderdelen.",
        "Garantie op de uitgevoerde reparatie.",
        "Eerlijk advies over reparatie vs. vervanging.",
      ],
    },
    {
      icon: Heater,
      title: "Waterdruk Bijvullen & Ontluchten",
      description:
        "Hulp bij het op peil brengen van de waterdruk en het ontluchten van radiatoren.",
      points: [
        "Oplossen van een te lage waterdruk.",
        "Verwijderen van lucht uit het systeem voor betere warmte.",
        "Voorkomt borrelende geluiden en inefficiëntie.",
        "Instructie voor hoe u dit de volgende keer zelf kunt doen.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Voorkom Storingen",
      description:
        "Regelmatig onderhoud verkleint de kans op onverwachte en kostbare storingen aanzienlijk.",
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Verleng Levensduur",
      description:
        "Een goed onderhouden ketel gaat jaren langer mee en presteert beter.",
    },
    {
      icon: <Wrench className="w-8 h-8 text-white" />,
      title: "Veilige Werking",
      description:
        "Onze monteurs controleren op koolmonoxide en andere veiligheidsrisico's.",
    },
  ];

  const faqs = [
    {
      question: "Hoe vaak moet mijn CV-ketel onderhouden worden?",
      answer:
        "Wij adviseren om uw CV-ketel elke 1 tot 2 jaar te laten onderhouden. Dit is vaak ook een vereiste voor de garantie en verzekering.",
    },
    {
      question: "Wat kost een onderhoudsbeurt voor een CV-ketel?",
      answer:
        "Een eenmalige onderhoudsbeurt kost gemiddeld tussen de €80 en €120. Een onderhoudscontract is vaak voordeliger en biedt extra zekerheid.",
    },
    {
      question: "Mijn verwarming wordt niet warm, wat kan ik doen?",
      answer:
        "Controleer eerst of de thermostaat hoog genoeg staat en of de waterdruk van de ketel voldoende is (meestal tussen 1.5 en 2.0 bar). Zo niet, probeer de ketel bij te vullen. Lukt dit niet, bel dan onze storingsdienst.",
    },
    {
      question: "Wat houdt een onderhoudsbeurt precies in?",
      answer:
        "Tijdens een onderhoudsbeurt wordt de ketel van binnen en buiten gereinigd, worden alle onderdelen gecontroleerd, de afstellingen geoptimaliseerd en de veiligheid getest.",
    },
  ];

  const reviews = [
    {
      name: "Sanne de Wit",
      location: "Den Haag",
      rating: 5,
      quote:
        "De monteur was stipt op tijd voor het jaarlijkse onderhoud. Vriendelijk, professioneel en gaf nuttige tips. Volgend jaar weer!",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Groningen",
      rating: 5,
      quote:
        "Midden in de winter een storing. Binnen 2 uur was er een monteur die het probleem direct verhielp. Wat een topservice!",
      highlighted: false,
    },
    {
      name: "Familie de Leeuw",
      location: "Breda",
      rating: 5,
      quote:
        "Duidelijke uitleg over wat er aan de hand was met onze ketel. Eerlijke prijs voor de reparatie. Zeer betrouwbaar.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1565163255712-3752009dfd6c?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1729183672500-46c52a897de5?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1692987909694-772e94e69ddf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyYWRpYXRvciUyMGluc3RhbGxhdGlvbiUyQyUyMGhvbWUlMjBoZWF0aW5nJTJDJTIwSFZBQyUyMHNlcnZpY2V8ZW58MHx8fHwxNzUxNzQwNTQ0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Meld uw storing of onderhoud",
      description:
        "Beschrijf het probleem of vraag periodiek onderhoud aan. Wij nemen snel contact op.",
      microcopy: "Vrijblijvend en 24/7 bereikbaar",
    },
    {
      icon: MessageSquare,
      title: "Ontvang een afspraakbevestiging",
      description:
        "We plannen direct een afspraak in met een gecertificeerde monteur bij u in de buurt.",
      microcopy: "Duidelijkheid over tijd en kosten",
    },
    {
      icon: UserCheck,
      title: "De monteur lost het op",
      description:
        "De vakman voert het onderhoud of de reparatie vakkundig uit, zodat u er weer warmpjes bij zit.",
      microcopy: "Snel, veilig en met garantie",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale CV Monteurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "CV monteur",
    color: "orange",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>CV Ketel Service | Klusgebied - Onderhoud & Reparatie</title>
        <meta
          name="description"
          content="Heeft uw CV-ketel onderhoud nodig of een storing? Onze 24/7 storingsdienst staat voor u klaar. Vraag direct hulp aan van een geverifieerde monteur."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-orange-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-orange-100 border border-orange-200/80 rounded-full px-4 py-2 mb-6">
                    <Heater className="w-5 h-5 text-orange-600" />
                    <span className="text-orange-800 font-semibold text-sm">
                      CV Ketel Service
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    CV Ketel Service?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500 mt-2">
                      Onderhoud & Reparatie
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voorkom storingen met periodiek onderhoud. 24/7 spoedservice
                    voor reparaties. Voor alle merken CV-ketels.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/cv")}
                      className="group inline-flex items-center justify-center bg-orange-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
                    >
                      Plan direct onderhoud
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1565163255712-3752009dfd6c?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele CV monteur voert onderhoud uit"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hulp in 3 Simpele Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de juiste monteur voor uw CV-ketel.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-orange-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze CV-ketel Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet servicepakket voor een veilige en efficiënte
                verwarming.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {cvServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-orange-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-orange-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost CV-ketel service?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Kies voor een eenmalige onderhoudsbeurt of een voordelig
                onderhoudscontract voor extra zekerheid.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Eenmalig onderhoud:{" "}
                    <strong className="text-slate-900">€80–€120</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Onderhoudscontract:{" "}
                    <strong className="text-slate-900">vanaf €7,50</strong> per
                    maand
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Spoedreparatie:{" "}
                    <strong className="text-slate-900">
                      Uurtarief + toeslag
                    </strong>
                  </span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/onderhoudscontract/cv-ketel")}
                className="bg-orange-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
              >
                Bekijk onderhoudscontracten{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun CV-ketel lieten onderhouden
                via Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-orange-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-orange-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-orange-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-orange-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een erkende monteur en wees verzekerd van een veilige
                en betrouwbare verwarming.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-orange-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-orange-500 to-red-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Storing of onderhoud nodig?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Onze erkende CV monteurs staan voor u klaar. Plaats uw klus en
              ontvang snel een reactie.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/cv")}
              className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je CV klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/cv")}
          className="w-full group inline-flex items-center justify-center bg-orange-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_CVKetelServicePage;
