import { Trash2, <PERSON>, Users2, <PERSON>, Clock } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

interface ReferralListProps {
  referrals: any[];
  onDelete: (referralId: string) => Promise<void>;
}

export const ReferralList = ({ referrals, onDelete }: ReferralListProps) => {
  const getCompletedJobsCount = (referral: any) => {
    if (!referral.referred_user?.job_responses) return 0;
    return referral.referred_user.job_responses.filter(
      (response: any) => response.status === "completed"
    ).length;
  };

  return (
    <div className="mt-8 animate-fade-in">
      <div className="flex items-center gap-2 mb-6">
        <Users2 className="h-5 w-5 text-primary" />
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          Verstuurde uitnodigingen
        </h4>
      </div>

      <div className="grid gap-6">
        {referrals.map((referral, index) => (
          <div
            key={referral.id}
            className="transform transition-all duration-300 hover:scale-[1.01] animate-fade-in"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              {/* Header Section */}
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                      <Mail className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-base font-medium text-gray-900 dark:text-white mb-1">
                        {referral.referred_email}
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                        <Clock className="h-4 w-4" />
                        <span>
                          Uitgenodigd op{" "}
                          {new Date(referral.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDelete(referral.id)}
                    className="text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Status Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-6">
                {/* Registration Status Card */}
                <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Registratie Status
                    </span>
                    <span
                      className={`text-sm font-medium px-2.5 py-0.5 rounded-full ${
                        referral.status === "completed"
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                          : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                      }`}
                    >
                      {referral.status === "completed"
                        ? "Voltooid"
                        : "In afwachting"}
                    </span>
                  </div>
                  {referral.status === "completed" ? (
                    <div className="flex items-center justify-center h-8">
                      <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                        <Check className="h-4 w-4 text-green-500" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Progress value={0} className="h-2" />
                      <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[40px] text-right">
                        0%
                      </span>
                    </div>
                  )}
                </div>

                {/* Completed Jobs Card */}
                <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Afgeronde Klussen
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {getCompletedJobsCount(referral)}/10
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress
                      value={Math.min(
                        (getCompletedJobsCount(referral) / 10) * 100,
                        100
                      )}
                      className="h-2"
                    />
                    <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[40px] text-right">
                      {Math.min(
                        (getCompletedJobsCount(referral) / 10) * 100,
                        100
                      ).toFixed(0)}
                      %
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
