import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ReviewVakmanForm } from "../ReviewVakmanForm";

interface ReviewSectionProps {
  isOwner: boolean;
  acceptedVakman: string | null;
  hasReviewed: boolean;
  showReviewForm: boolean;
  setShowReviewForm: (show: boolean) => void;
  jobId: string;
  checkReviewStatus: (craftmanId: string) => Promise<void>;
}

export const ReviewSection = ({
  isOwner,
  acceptedVakman,
  hasReviewed,
  showReviewForm,
  setShowReviewForm,
  jobId,
  checkReviewStatus,
}: ReviewSectionProps) => {
  if (!acceptedVakman) return null;

  return (
    <>
      {isOwner && !hasReviewed && (
        <Dialog open={showReviewForm} onOpenChange={setShowReviewForm}>
          <DialogTrigger asChild>
            <Button className="w-full"><PERSON><PERSON><PERSON><PERSON> de vakman</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Beoordeel de vakman</DialogTitle>
            </DialogHeader>
            <ReviewVakmanForm
              jobId={jobId}
              vakmanId={acceptedVakman}
              onReviewSubmitted={() => {
                setShowReviewForm(false);
                checkReviewStatus(acceptedVakman);
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};
