/**
 * @description This component showcases the core values and success of the Klusgebied platform through a visually stunning and trustworthy highlight section. It features a dynamic composition of images representing professional teams, paired with compelling text about quality, reliability, and the strong community of vakmannen. The component is designed to build user confidence and replace a simple job example with a more impactful brand statement. Key variables include the image URLs for the visual composition and the structured text content highlighting platform benefits.
 */
import React from "react";
import { CheckCircle, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

const ExampleJob = () => {
  const navigate = useNavigate();

  return (
    <section className="bg-slate-50 py-20 lg:py-28">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
          {/* Image Composition */}
          <div className="relative h-[400px] lg:h-[500px] motion-preset-slide-right">
            <div className="absolute w-[60%] h-[70%] top-0 left-0 rounded-2xl shadow-2xl overflow-hidden group transform hover:scale-105 transition-transform duration-500">
              <img
                src="https://images.unsplash.com/photo-1542412466-09bc6fed0395?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxidXNpbmVzcyUyQyUyMHRlYW18ZW58MHx8fHwxNzUxODA5MzUzfDA&ixlib=rb-4.1.0&w=1024&h=1024"
                alt="Een team van professionele vakmannen"
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                loading="lazy"
              />
            </div>
            <div className="absolute w-[55%] h-[65%] bottom-0 right-0 rounded-2xl shadow-2xl overflow-hidden group transform hover:scale-105 transition-transform duration-500 border-4 border-white">
              <img
                src="https://images.unsplash.com/photo-1681993302983-96d079223a70?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxidXNpbmVzcyUyQyUyMHRlYW18ZW58MHx8fHwxNzUxODA5MzUzfDA&ixlib=rb-4.1.0&w=1024&h=1024"
                alt="Tevreden klanten en vakmannen"
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                loading="lazy"
              />
            </div>
            <div className="absolute w-[40%] h-[45%] top-1/4 left-1/3 -translate-x-1/2 -translate-y-1/4 rounded-2xl shadow-2xl overflow-hidden group transform hover:scale-105 transition-transform duration-500 border-4 border-white">
              <img
                src="https://images.unsplash.com/photo-1516880711640-ef7db81be3e1?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwzfHxidXNpbmVzcyUyQyUyMHRlYW18ZW58MHx8fHwxNzUxODA5MzUzfDA&ixlib=rb-4.1.0&w=1024&h=1024"
                alt="Een vakman die een project plant"
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                loading="lazy"
                width={216}
                height={216}
              />
            </div>
          </div>

          {/* Text Content */}
          <div className="motion-preset-slide-left">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-6">
              Gebouwd op Vertrouwen, Gedreven door Vakmanschap
            </h2>
            <p className="text-lg text-slate-600 mb-8 leading-relaxed">
              Klusgebied is meer dan een platform; het is een community waar
              kwaliteit en betrouwbaarheid samenkomen. We verbinden de beste,
              geverifieerde professionals met klanten die waarde hechten aan
              echt vakmanschap.
            </p>
            <ul className="space-y-4 mb-10">
              <li className="flex items-start">
                <CheckCircle className="w-6 h-6 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                <span className="text-slate-700">
                  <strong>Geverifieerd Netwerk:</strong> Elke professional wordt
                  door ons gescreend op kwaliteit en betrouwbaarheid.
                </span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-6 h-6 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                <span className="text-slate-700">
                  <strong>Transparant Proces:</strong> Van heldere offertes tot
                  directe communicatie, u weet altijd waar u aan toe bent.
                </span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-6 h-6 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                <span className="text-slate-700">
                  <strong>Succes Gegarandeerd:</strong> Met duizenden succesvol
                  afgeronde klussen en een hoge klanttevredenheid.
                </span>
              </li>
            </ul>
            <button
              onClick={() => navigate("/over-ons")}
              className="group inline-flex items-center bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-teal-500/20 hover:-translate-y-0.5"
            >
              <span>Ontdek ons verhaal</span>
              <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExampleJob;
