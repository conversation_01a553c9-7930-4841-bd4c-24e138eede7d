import { useState } from "react";
import { Check, X, ChevronDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import ConfirmModal from "@/components/modal/ConfirmModal";

interface DiplomaDocument {
  id: string;
  name: string;
  status: string;
  user_id: string;
  user_name: string;
}

interface BulkDiplomaApprovalProps {
  documents: DiplomaDocument[];
  onApprove: (documentIds: string[]) => Promise<void>;
  onReject: (documentIds: string[]) => Promise<void>;
}

export function BulkDiplomaApproval({
  documents,
  onApprove,
  onReject,
}: BulkDiplomaApprovalProps) {
  const [selectedDocs, setSelectedDocs] = useState<Set<string>>(new Set());
  const [isOpen, setIsOpen] = useState(true);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isApproving, setIsApproving] = useState(false);

  const { toast } = useToast();

  const handleSelectAll = () => {
    if (selectedDocs.size === documents.length) {
      setSelectedDocs(new Set());
    } else {
      setSelectedDocs(new Set(documents.map((doc) => doc.id)));
    }
  };

  const handleToggleDocument = (docId: string) => {
    const newSelection = new Set(selectedDocs);
    if (newSelection.has(docId)) {
      newSelection.delete(docId);
    } else {
      newSelection.add(docId);
    }
    setSelectedDocs(newSelection);
  };

  const handleBulkApprove = async () => {
    setIsApproving(true);
    try {
      await onApprove(Array.from(selectedDocs));
      setSelectedDocs(new Set());
    } catch (error) {
      console.error(error);
    } finally {
      setIsApproving(false);
    }
  };

  const handleBulkReject = async () => {
    setIsRejecting(true);
    try {
      await onReject(Array.from(selectedDocs));
      setSelectedDocs(new Set());
    } catch (error) {
      console.error(error);
    } finally {
      setIsRejecting(false);
      setIsRejectModalOpen(false);
    }
  };

  return (
    <>
      <Card className="mb-6">
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Openstaande Diploma's</CardTitle>
                <CardDescription>
                  {documents.length} document
                  {documents.length !== 1 ? "en" : ""} wachtend op goedkeuring
                </CardDescription>
              </div>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ChevronDown
                    className={`h-4 w-4 transition-transform ${
                      isOpen ? "transform rotate-180" : ""
                    }`}
                  />
                </Button>
              </CollapsibleTrigger>
            </div>
          </CardHeader>
          <CollapsibleContent>
            <CardContent>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
                <div
                  className="flex items-center space-x-2 cursor-pointer"
                  onClick={handleSelectAll}
                >
                  <Checkbox
                    checked={
                      selectedDocs.size === documents.length &&
                      documents.length > 0
                    }
                  />
                  <span className="text-sm text-muted-foreground">
                    {selectedDocs.size} van {documents.length} geselecteerd
                  </span>
                </div>
                <div className="flex w-full sm:w-auto gap-2">
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => setIsRejectModalOpen(true)}
                    disabled={selectedDocs.size === 0}
                    className="flex-1 sm:flex-none"
                  >
                    <X className="h-4 w-4 mr-1" />
                    <span>Afkeuren</span>
                  </Button>
                  <Button
                    size="sm"
                    variant="default"
                    onClick={handleBulkApprove}
                    disabled={selectedDocs.size === 0 || isApproving}
                    className="flex-1 sm:flex-none"
                  >
                    {isApproving ? (
                      <span className="flex items-center">
                        <svg
                          className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        Bezig...
                      </span>
                    ) : (
                      <>
                        <Check className="h-4 w-4 mr-1" />
                        <span>Goedkeuren</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-2">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center space-x-3 p-2 rounded-lg cursor-pointer hover:bg-muted"
                      onClick={() => handleToggleDocument(doc.id)}
                    >
                      <Checkbox checked={selectedDocs.has(doc.id)} />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{doc.name}</p>
                        <p className="text-sm text-muted-foreground truncate">
                          {doc.user_name}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
      <ConfirmModal
        isOpen={isRejectModalOpen}
        setIsOpen={setIsRejectModalOpen}
        loading={isRejecting}
        confirmHandler={handleBulkReject}
        title="Documenten afkeuren"
        content={`Weet je zeker dat je ${selectedDocs.size} document${
          selectedDocs.size !== 1 ? "en" : ""
        } wilt afkeuren?`}
        yesText="Afkeuren"
        noText="Annuleren"
        variant="danger"
      />
    </>
  );
}
