/**
 * @description This component renders a comprehensive and SEO-optimized detail page for CV installer services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for CV installers.
 */
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Thermometer,
  ShieldCheck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Flame,
} from "lucide-react";

const Service_CVInstallateurPage = () => {
  usePageTitle(
    "CV Installateur Nodig? | Klusgebied - Installatie & Vervanging"
  );
  const navigate = useNavigate();

  const cvServices = [
    {
      icon: Flame,
      title: "Nieuwe CV-ketel Installatie",
      description:
        "Vakkundige installatie van de nieuwste generatie HR-ketels.",
      points: [
        "Advies over de beste ketel voor uw woning.",
        "Installatie volgens de laatste veiligheidsnormen.",
        "Inclusief afvoeren van uw oude ketel.",
        "Volledige garantie op installatie en toestel.",
      ],
    },
    {
      icon: Thermometer,
      title: "CV-ketel Vervangen",
      description:
        "Vervanging van uw oude, onzuinige ketel voor een modern en efficiënt model.",
      points: [
        "Bespaar tot 20% op uw energierekening.",
        "Verhoog het warmwatercomfort in huis.",
        "Snelle vervanging, vaak binnen één dag.",
        "Wij regelen alles van A tot Z.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Advies en Capaciteitsberekening",
      description:
        "Zeker zijn van de juiste ketel? Wij berekenen de benodigde capaciteit.",
      points: [
        "Voorkom een te kleine of te grote ketel.",
        "Nauwkeurige berekening op basis van uw woning en verbruik.",
        "Onafhankelijk advies over merken en types.",
        "Een perfect afgestemd systeem voor optimaal rendement.",
      ],
    },
    {
      icon: Clock,
      title: "Radiatoren & Leidingen Aanleggen",
      description:
        "Installatie van nieuwe radiatoren en het aanleggen of verleggen van leidingen.",
      points: [
        "Plaatsen van designradiatoren en standaard radiatoren.",
        "Aanleg van complete verwarmingssystemen.",
        "Verleggen van leidingen bij een verbouwing.",
        "Nette en professionele afwerking.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Gecertificeerde Monteurs",
      description:
        "Al onze installateurs zijn erkend en werken volgens de strengste veiligheidsnormen.",
    },
    {
      icon: <Flame className="w-8 h-8 text-white" />,
      title: "Energiezuinige Oplossingen",
      description:
        "Wij installeren de nieuwste HR-ketels die u helpen besparen op uw energierekening.",
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Snelle Installatie",
      description:
        "Uw nieuwe CV-ketel is vaak al binnen één dag vakkundig geïnstalleerd.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een nieuwe CV-ketel inclusief installatie?",
      answer:
        "De kosten voor een nieuwe HR-ketel inclusief installatie liggen gemiddeld tussen de €1.800 en €3.500. De prijs is afhankelijk van het merk, het vermogen (CW-klasse) en de complexiteit van de installatie.",
    },
    {
      question: "Hoe lang duurt het installeren van een nieuwe CV-ketel?",
      answer:
        "In de meeste gevallen kan een CV-ketel binnen één werkdag vervangen en geïnstalleerd worden. Onze monteur zorgt dat u dezelfde dag nog warm water en verwarming heeft.",
    },
    {
      question: "Welk merk CV-ketel is het beste?",
      answer:
        "Merken als Remeha, Intergas en Vaillant staan bekend om hun betrouwbaarheid en goede prestaties. Onze specialist kan u adviseren welk merk en type het beste past bij uw woning en wensen.",
    },
    {
      question: "Krijg ik garantie op de installatie?",
      answer:
        "Jazeker. U krijgt volledige fabrieksgarantie op de CV-ketel en daarnaast geven wij standaard garantie op al het installatiewerk.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Boer",
      location: "Amersfoort",
      rating: 5,
      quote:
        "Onze nieuwe CV-ketel werd super snel en vakkundig geïnstalleerd. De monteur was vriendelijk en liet alles netjes achter. Top service!",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Groningen",
      rating: 5,
      quote:
        "Duidelijk advies gekregen over welke ketel het beste bij ons huis past. De installatie was vlekkeloos. We merken nu al verschil op de energierekening.",
      highlighted: false,
    },
    {
      name: "Linda de Wit",
      location: "Breda",
      rating: 5,
      quote:
        "Eindelijk van onze oude, lawaaiige ketel af. De nieuwe is stil en we hebben veel sneller warm water. Zeer tevreden over Klusgebied.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1729183672500-46c52a897de5?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1565163255712-3752009dfd6c?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1692987909694-772e94e69ddf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyYWRpYXRvciUyMGluc3RhbGxhdGlvbiUyQyUyMGhvbWUlMjBoZWF0aW5nJTJDJTIwSFZBQyUyMHNlcnZpY2V8ZW58MHx8fHwxNzUxNzQwNTQ0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats uw aanvraag",
      description:
        "Beschrijf uw situatie. Gaat het om vervanging of een nieuwe installatie? Wij helpen u verder.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang advies & offertes",
      description:
        "Gecertificeerde installateurs geven advies en sturen u vrijblijvende offertes op maat.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & plan de installatie",
      description:
        "Vergelijk de offertes, kies de beste vakman en plan de installatie. Geniet van een warm en comfortabel huis.",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale CV Installateurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "CV installateur",
    color: "orange",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          CV Installateur Nodig? | Klusgebied - Installatie & Vervanging
        </title>
        <meta
          name="description"
          content="Vind snel een betrouwbare CV installateur voor de installatie of vervanging van uw CV-ketel. Plaats uw klus gratis en ontvang offertes van geverifieerde vakmannen."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-orange-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-orange-100 border border-orange-200/80 rounded-full px-4 py-2 mb-6">
                    <Thermometer className="w-5 h-5 text-orange-600" />
                    <span className="text-orange-800 font-semibold text-sm">
                      CV Installateur
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    CV Installateur nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500 mt-2">
                      Vakkundig & Snel
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor professionele installatie van uw nieuwe CV-ketel.
                    Energiezuinig, betrouwbaar en met volledige garantie.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/cv")}
                      className="group inline-flex items-center justify-center bg-orange-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw installateur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1729183672500-46c52a897de5?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele CV installateur aan het werk"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Uw Nieuwe CV-ketel in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte installateur voor uw
                nieuwe CV-ketel.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-orange-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Installatie Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet aanbod voor een warm en comfortabel huis.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {cvServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-orange-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-orange-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een nieuwe CV-ketel?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn inclusief installatie en afvoeren van de oude
                ketel. U ontvangt altijd een duidelijke offerte vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    HR-ketel (CW4):{" "}
                    <strong className="text-slate-900">€1.800–€2.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    HR-ketel (CW5):{" "}
                    <strong className="text-slate-900">€2.200–€3.000</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    HRe-ketel (met stroomopwekking):{" "}
                    <strong className="text-slate-900">vanaf €9.000</strong>
                  </span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/plaats-een-klus/cv")}
                className="bg-orange-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een CV-ketel lieten installeren
                via Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-orange-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-orange-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-orange-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-orange-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Betrouwbare installateurs die staan voor kwaliteit en een warm
                huis.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-orange-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-orange-500 to-red-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor een nieuwe, zuinige CV-ketel?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvende offerte aan en ontdek hoeveel u kunt
              besparen.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/cv")}
              className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/cv")}
          className="w-full group inline-flex items-center justify-center bg-orange-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_CVInstallateurPage;
