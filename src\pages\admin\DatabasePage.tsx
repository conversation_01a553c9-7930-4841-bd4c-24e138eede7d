import { Database, Loader2 } from "lucide-react";

import { BackToDashboard } from "@/components/BackToDashboard";
import { TableStatsCard } from "@/components/admin/database/TableStatsCard";
import { useTableStats } from "@/components/admin/database/useTableStats";

const DatabasePage = () => {
  const { tableStats, isLoading, TABLE_NAMES } = useTableStats();

  return (
    <div className="max-w-7xl mx-auto px-6 sm:px-0 py-6 space-y-6">
      <BackToDashboard />
      <div className="flex items-center gap-4 mb-8">
        <div className="p-3 bg-gradient-to-br from-primary to-accent rounded-lg">
          <Database className="h-6 w-6 text-white" />
        </div>
        <div>
          <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
            Database Overzicht
          </h1>
          <p className="text-muted-foreground">
            Real-time statistieken van alle database tabellen
          </p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {TABLE_NAMES.map((tableName) => (
            <TableStatsCard
              key={tableName}
              tableName={tableName}
              stats={tableStats[tableName]}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default DatabasePage;
