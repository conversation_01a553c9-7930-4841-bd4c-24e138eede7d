import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  read: boolean;
  action_url?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface NotificationFilters {
  read?: boolean;
  type?: Notification["type"];
  limit?: number;
  offset?: number;
}

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  // Fetch notifications from the database
  const fetchNotifications = useCallback(
    async (filters: NotificationFilters = {}) => {
      if (!userProfile?.id) return;

      try {
        setLoading(true);

        let query = (supabase as any)
          .from("notifications")
          .select("*")
          .eq("user_id", userProfile.id)
          .order("created_at", { ascending: false });

        // Apply filters
        if (filters.read !== undefined) {
          query = query.eq("read", filters.read);
        }

        if (filters.type) {
          query = query.eq("type", filters.type);
        }

        if (filters.limit) {
          query = query.limit(filters.limit);
        }

        if (filters.offset) {
          query = query.range(
            filters.offset,
            filters.offset + (filters.limit || 10) - 1
          );
        }

        const { data, error } = await query;

        if (error) {
          console.error("Error fetching notifications:", error);
          toast({
            variant: "destructive",
            title: "Fout bij laden",
            description: "Kon notificaties niet laden. Probeer het opnieuw.",
          });
          return;
        }

        setNotifications(data || []);

        // Update unread count
        const unread = (data || []).filter((n) => !n.read).length;
        setUnreadCount(unread);
      } catch (error) {
        console.error("Error fetching notifications:", error);
        toast({
          variant: "destructive",
          title: "Fout bij laden",
          description: "Kon notificaties niet laden. Probeer het opnieuw.",
        });
      } finally {
        setLoading(false);
      }
    },
    [userProfile?.id, toast]
  );

  // Fetch unread count only
  const fetchUnreadCount = useCallback(async () => {
    if (!userProfile?.id) return;

    try {
      const { count, error } = await (supabase as any)
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userProfile.id)
        .eq("read", false);

      if (error) {
        console.error("Error fetching unread count:", error);
        return;
      }

      setUnreadCount(count || 0);
    } catch (error) {
      console.error("Error fetching unread count:", error);
    }
  }, [userProfile?.id]);

  // Mark notification as read
  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await (supabase as any)
          .from("notifications")
          .update({ read: true })
          .eq("id", notificationId)
          .eq("user_id", userProfile?.id);

        if (error) {
          console.error("Error marking notification as read:", error);
          toast({
            variant: "destructive",
            title: "Fout",
            description: "Kon notificatie niet markeren als gelezen.",
          });
          return false;
        }

        // Update local state
        setNotifications((prev) =>
          prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n))
        );

        // Update unread count
        setUnreadCount((prev) => Math.max(0, prev - 1));

        return true;
      } catch (error) {
        console.error("Error marking notification as read:", error);
        return false;
      }
    },
    [toast, userProfile?.id]
  );

  // Mark notification as unread
  const markAsUnread = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await (supabase as any)
          .from("notifications")
          .update({ read: false })
          .eq("id", notificationId)
          .eq("user_id", userProfile?.id);

        if (error) {
          console.error("Error marking notification as unread:", error);
          toast({
            variant: "destructive",
            title: "Fout",
            description: "Kon notificatie niet markeren als ongelezen.",
          });
          return false;
        }

        // Update local state
        setNotifications((prev) =>
          prev.map((n) => (n.id === notificationId ? { ...n, read: false } : n))
        );

        // Update unread count
        setUnreadCount((prev) => prev + 1);

        return true;
      } catch (error) {
        console.error("Error marking notification as unread:", error);
        return false;
      }
    },
    [toast, userProfile?.id]
  );

  // Mark multiple notifications as read
  const markMultipleAsRead = useCallback(
    async (notificationIds: string[]) => {
      try {
        const { error } = await (supabase as any)
          .from("notifications")
          .update({ read: true })
          .in("id", notificationIds)
          .eq("user_id", userProfile?.id);

        if (error) {
          console.error("Error marking notifications as read:", error);
          toast({
            variant: "destructive",
            title: "Fout",
            description: "Kon notificaties niet markeren als gelezen.",
          });
          return 0;
        }

        toast({
          title: "Gemarkeerd als gelezen",
          description: `${notificationIds.length} notificatie(s) gemarkeerd als gelezen.`,
        });

        // Update local state
        setNotifications((prev) =>
          prev.map((n) =>
            notificationIds.includes(n.id) ? { ...n, read: true } : n
          )
        );

        // Update unread count
        setUnreadCount((prev) => Math.max(0, prev - notificationIds.length));

        return notificationIds.length;
      } catch (error) {
        console.error("Error marking notifications as read:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description: "Kon notificaties niet markeren als gelezen.",
        });
        return 0;
      }
    },
    [toast, userProfile?.id]
  );

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const { error } = await (supabase as any)
        .from("notifications")
        .update({ read: true })
        .eq("user_id", userProfile?.id)
        .eq("read", false);

      if (error) {
        console.error("Error marking all notifications as read:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description: "Kon notificaties niet markeren als gelezen.",
        });
        return false;
      }

      // Update local state
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));

      setUnreadCount(0);

      const unreadCount = notifications.filter((n) => !n.read).length;

      toast({
        title: "Alle notificaties gelezen",
        description: `${unreadCount} notificatie(s) gemarkeerd als gelezen.`,
      });

      return true;
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      return false;
    }
  }, [toast, userProfile?.id, notifications]);

  // Delete notification
  const deleteNotification = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await (supabase as any)
          .from("notifications")
          .delete()
          .eq("id", notificationId)
          .eq("user_id", userProfile?.id);

        if (error) {
          console.error("Error deleting notification:", error);
          toast({
            variant: "destructive",
            title: "Fout",
            description: "Kon notificatie niet verwijderen.",
          });
          return false;
        }

        // Update local state
        const deletedNotification = notifications.find(
          (n) => n.id === notificationId
        );
        setNotifications((prev) => prev.filter((n) => n.id !== notificationId));

        // Update unread count if deleted notification was unread
        if (deletedNotification && !deletedNotification.read) {
          setUnreadCount((prev) => Math.max(0, prev - 1));
        }

        return true;
      } catch (error) {
        console.error("Error deleting notification:", error);
        return false;
      }
    },
    [notifications, toast, userProfile?.id]
  );

  // Delete multiple notifications
  const deleteMultipleNotifications = useCallback(
    async (notificationIds: string[]) => {
      try {
        const { error } = await (supabase as any)
          .from("notifications")
          .delete()
          .in("id", notificationIds)
          .eq("user_id", userProfile?.id);

        if (error) {
          console.error("Error deleting notifications:", error);
          toast({
            variant: "destructive",
            title: "Fout",
            description: "Kon notificaties niet verwijderen.",
          });
          return 0;
        }

        toast({
          title: "Verwijderd",
          description: `${notificationIds.length} notificatie(s) verwijderd.`,
        });

        // Update local state
        const deletedUnreadCount = notifications.filter(
          (n) => notificationIds.includes(n.id) && !n.read
        ).length;

        setNotifications((prev) =>
          prev.filter((n) => !notificationIds.includes(n.id))
        );

        // Update unread count
        setUnreadCount((prev) => Math.max(0, prev - deletedUnreadCount));

        return notificationIds.length;
      } catch (error) {
        console.error("Error deleting notifications:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description: "Kon notificaties niet verwijderen.",
        });
        return 0;
      }
    },
    [notifications, toast, userProfile?.id]
  );

  // Set up real-time subscription
  useEffect(() => {
    if (!userProfile?.id) return;

    const channel = supabase
      .channel("notifications")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          filter: `user_id=eq.${userProfile.id}`,
        },
        (payload) => {
          console.log("Notification change:", payload);

          if (payload.eventType === "INSERT") {
            const newNotification = payload.new as Notification;
            setNotifications((prev) => [newNotification, ...prev]);
            if (!newNotification.read) {
              setUnreadCount((prev) => prev + 1);
            }
          } else if (payload.eventType === "UPDATE") {
            const updatedNotification = payload.new as Notification;
            setNotifications((prev) =>
              prev.map((n) =>
                n.id === updatedNotification.id ? updatedNotification : n
              )
            );
          } else if (payload.eventType === "DELETE") {
            const deletedNotification = payload.old as Notification;
            setNotifications((prev) =>
              prev.filter((n) => n.id !== deletedNotification.id)
            );
            if (!deletedNotification.read) {
              setUnreadCount((prev) => Math.max(0, prev - 1));
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userProfile?.id]);

  // Initial fetch
  useEffect(() => {
    if (userProfile?.id) {
      fetchNotifications();
    }
  }, [userProfile?.id, fetchNotifications]);

  return {
    notifications,
    loading,
    unreadCount,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAsUnread,
    markMultipleAsRead,
    markAllAsRead,
    deleteNotification,
    deleteMultipleNotifications,
  };
};
