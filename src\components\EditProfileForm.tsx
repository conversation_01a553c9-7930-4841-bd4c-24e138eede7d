import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { profileSchema } from "@/schemas/profileSchema";
import type { ProfileFormData } from "@/types/auth";
import { useToast } from "@/hooks/use-toast";
import { ProfilePhotoUpload } from "./profile/ProfilePhotoUpload";
import { ProfileFormFields } from "./profile/ProfileFormFields";

interface EditProfileFormProps {
  profile: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export const EditProfileForm = ({
  profile,
  onSuccess,
  onCancel,
}: EditProfileFormProps) => {
  const { toast } = useToast();

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      first_name: profile?.first_name || "",
      last_name: profile?.last_name || "",
      email: profile?.email || "",
      city: profile?.city || "",
      street_address: profile?.street_address || "",
      house_number: profile?.house_number || "",
      house_number_addition: profile?.house_number_addition || "",
      phone_number: profile?.phone_number || "",
      user_type: profile?.user_type || "klusaanvrager",
      postal_code: profile?.postal_code,
      btw_number: profile?.btw_number || undefined,
      company_name: profile?.company_name || undefined,
      kvk_number: profile?.kvk_number || undefined,
    },
  });

  const onSubmit = async (values: ProfileFormData) => {
    try {
      // Format phone number if present
      if (values.phone_number) {
        values.phone_number = values.phone_number
          .replace(/\s+/g, "")
          .replace(/-/g, "");
        // .replace(/^\+31/, '0');
      }

      // Prepare update data - alleen de velden die een klusplaatser mag updaten
      const updateData = {
        first_name: values.first_name,
        last_name: values.last_name,
        full_name: `${values.first_name} ${values.last_name}`.trim(),
        email: values.email,
        city: values.city,
        street_address: values.street_address,
        house_number: values.house_number,
        house_number_addition: values.house_number_addition,
        postal_code: values.postal_code,
        phone_number: values.phone_number,
        company_name: values.company_name,
        kvk_number: values.kvk_number,
        btw_number: values.btw_number,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from("profiles")
        .update(updateData)
        .eq("id", profile.id)
        .select()
        .single();

      if (error) {
        console.error("Error updating profile:", error);
        throw error;
      }

      // Show success message
      toast({
        title: "Profiel bijgewerkt",
        description: "Je profielgegevens zijn succesvol opgeslagen.",
      });

      // Call the success callback
      onSuccess();
    } catch (error: any) {
      console.error("Error in form submission:", error);

      // Show error message
      toast({
        variant: "destructive",
        title: "Fout bij opslaan",
        description:
          error.message ||
          "Er is een fout opgetreden bij het opslaan van je profiel.",
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <ProfilePhotoUpload profile={profile} onSuccess={onSuccess} />
        <ProfileFormFields
          form={form}
          isVakman={profile?.user_type === "vakman"}
          isEditMode={true}
        />
        <div className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Annuleren
          </Button>
          <Button type="submit">Opslaan</Button>
        </div>
      </form>
    </Form>
  );
};
