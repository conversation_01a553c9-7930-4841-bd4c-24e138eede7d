import { Label } from "@/components/ui/label";
import { UserType } from "@/types/auth";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface UserTypeSelectProps {
  value: UserType;
  onChange: (value: UserType) => void;
}

export const UserTypeSelect = ({ value, onChange }: UserTypeSelectProps) => {
  return (
    <div className="space-y-2">
      <Label>Ik ben een</Label>
      <Select
        value={value}
        onValueChange={(value) => {
          onChange(value as UserType);
        }}
      >
        <SelectTrigger className="w-full bg-white">
          <SelectValue placeholder="Selecteer type gebruiker" />
        </SelectTrigger>
        <SelectContent className="bg-white">
          <SelectItem value="klusplaatser">Klusplaatser</SelectItem>
          <SelectItem value="vakman">Vakman</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
