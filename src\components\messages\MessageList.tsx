import { useEffect, useRef } from "react";

import { Card } from "@/components/ui/card";
import { Message } from "@/types/chat";

interface MessageListProps {
  messages: Message[];
}

export const MessageList = ({ messages }: MessageListProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <div className="space-y-4 max-h-[400px] overflow-y-auto p-4 bg-muted/30 rounded-lg">
      {messages.map((message) => (
        <Card key={message.id} className="p-4">
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <span className="font-medium">
                {message.sender?.full_name ||
                  `${message.sender?.first_name} ${message.sender?.last_name}`}
              </span>
              <span className="text-sm text-muted-foreground">
                {new Date(message.created_at).toLocaleString()}
              </span>
            </div>
            <p>{message.content}</p>
          </div>
        </Card>
      ))}
      {messages.length === 0 && (
        <p className="text-center text-muted-foreground">
          Nog geen berichten in deze conversatie
        </p>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};
