import { NavigateFunction } from "react-router-dom";
import { LogOut, Briefcase, Euro, User, <PERSON>ch, Bell } from "lucide-react";

import {
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ROUTE_PATHS } from "@/config/routes";

interface MenuItemsProps {
  onLogout: () => void;
  navigate: NavigateFunction;
}

export const MenuItems = ({ onLogout, navigate }: MenuItemsProps) => {
  const handleNavigation = (view: string) => {
    switch (view) {
      case "dashboard":
        navigate(ROUTE_PATHS.HOME);
        break;
      case "available-jobs":
        navigate("/banen");
        break;
      case "balance":
        navigate("/evenwicht");
        break;
      case "profile":
        navigate("/profiel");
        break;
      case "notifications":
        navigate(ROUTE_PATHS.NOTIFICATIONS);
        break;
      // case "onderhoud":
      //   navigate("/onderhoud");
      //   break;
      default:
        navigate(ROUTE_PATHS.HOME);
    }
  };

  return (
    <>
      <DropdownMenuItem
        onClick={() => handleNavigation("available-jobs")}
        className="hover:!bg-muted !text-black py-3"
      >
        <Briefcase className="mr-3 h-4 w-4" />
        Beschikbare Klussen
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => handleNavigation("balance")}
        className="hover:!bg-muted !text-black py-3"
      >
        <Euro className="mr-3 h-4 w-4" />
        Saldo
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => handleNavigation("profile")}
        className="hover:!bg-muted !text-black py-3"
      >
        <User className="mr-3 h-4 w-4" />
        Profiel
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => handleNavigation("notifications")}
        className="hover:!bg-muted !text-black py-3"
      >
        <Bell className="mr-3 h-4 w-4" />
        Notificaties
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={() => handleNavigation("onderhoud")}
        className="hover:!bg-muted !text-black py-3"
      >
        <Wrench className="mr-3 h-4 w-4" />
        Onderhoud
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem
        onClick={onLogout}
        className="hover:!bg-muted py-3 !text-red-500"
      >
        <LogOut className="mr-3 h-4 w-4" />
        Uitloggen
      </DropdownMenuItem>
    </>
  );
};
