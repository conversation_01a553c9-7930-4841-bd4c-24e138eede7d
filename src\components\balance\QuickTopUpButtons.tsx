import { Euro } from "lucide-react";

import { Button } from "@/components/ui/button";

interface QuickTopUpButtonsProps {
  onSelectAmount: (amount: string) => void;
  isAddingFunds: boolean;
  selectedAmount: string;
}

export const QuickTopUpButtons = ({
  onSelectAmount,
  isAddingFunds,
  selectedAmount,
}: QuickTopUpButtonsProps) => {
  const amounts = ["20", "50", "100"];

  return (
    <div className="grid grid-cols-3 gap-2">
      {amounts.map((amount) => (
        <Button
          key={amount}
          variant="outline"
          onClick={() => onSelectAmount(amount)}
          disabled={isAddingFunds}
          className={`p-4 h-auto text-lg ${
            selectedAmount === amount ? "bg-primary text-white" : ""
          }`}
        >
          <Euro className="h-4 w-4 mr-2" />€{amount}
        </Button>
      ))}
    </div>
  );
};
