import { useState, MouseEvent } from "react";
import { useSetAtom } from "jotai";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import ConfirmModal from "@/components/modal/ConfirmModal";
import { adminUsersAtom } from "@/states/admin";

interface UsersTableRowProps {
  user: any;
  onUserClick: (user: any) => void;
  removeUser: (userId: string) => void;
}

const UsersTableRow = ({
  user,
  onUserClick,
  removeUser,
}: UsersTableRowProps) => {
  const { toast } = useToast();
  const setAdminUsers = useSetAtom(adminUsersAtom);

  const [status, setStatus] = useState<string>(user.status);
  const [acceptLoading, setAcceptLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [blockLoading, setBlockLoading] = useState(false);

  const badgeData = {
    in_review: {
      color: "warning",
      content: "In beoordeling",
    },
    active: {
      color: "default",
      content: "Actief",
    },
    inactive: {
      color: "secondary",
      content: "Inactief",
    },
  };

  const handleAccept = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setAcceptLoading(true);

    try {
      const { error } = await supabase
        .from("profiles")
        .update({ status: "active" })
        .eq("id", user.id);
      if (error) throw error;

      setStatus("active");
      setAdminUsers((adminUsers) =>
        adminUsers.map((u) =>
          u.id === user.id ? { ...u, status: "active" } : u
        )
      );
    } catch (error) {
      console.error(error);
    } finally {
      setAcceptLoading(false);
    }
  };

  const handleDeleteClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsConfirmOpen(true);
  };

  const handleDelete = async () => {
    setDeleteLoading(true);
    try {
      const { error: deleteError } = await supabase.functions.invoke(
        "delete-admin-user",
        {
          body: {
            id: user?.id,
          },
        }
      );
      if (deleteError) throw deleteError;

      removeUser(user?.id);
      toast({
        title: "Gebruiker verwijderd",
        description: "De gebruiker is succesvol verwijderd.",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Fout bij verwijderen",
        description:
          "Er is iets misgegaan bij het verwijderen van de gebruiker.",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
      setIsConfirmOpen(false);
    }
  };

  const blockUser = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setBlockLoading(true);
    const newStatus = status === "inactive" ? "active" : "inactive";
    const actionText = status === "inactive" ? "gedeblokkeerd" : "geblokkeerd";

    try {
      const { error } = await supabase
        .from("profiles")
        .update({ status: newStatus })
        .eq("id", user.id);
      if (error) throw error;

      setStatus(newStatus);
      toast({
        title: `Gebruiker ${actionText}`,
        description: `De gebruiker is succesvol ${actionText}.`,
      });
      setAdminUsers((adminUsers) =>
        adminUsers.map((u) =>
          u.id === user.id ? { ...u, status: newStatus } : u
        )
      );
    } catch (error) {
      console.error(error);
      setStatus(status);
      toast({
        title: `Fout bij ${
          status === "inactive" ? "deblokkeren" : "blokkeren"
        }`,
        description: "Er is iets misgegaan. Probeer het later opnieuw.",
        variant: "destructive",
      });
    } finally {
      setBlockLoading(false);
    }
  };

  return (
    <>
      <TableRow
        className="cursor-pointer hover:bg-muted/50"
        onClick={() => onUserClick(user)}
      >
        <TableCell>
          {user.first_name} {user.last_name}
        </TableCell>
        <TableCell>{user.email}</TableCell>
        <TableCell>
          <Badge
            variant={user.user_type === "vakman" ? "default" : "secondary"}
          >
            {user.user_type === "vakman" ? "Vakman" : "Klusaanvrager"}
          </Badge>
        </TableCell>
        <TableCell>
          {new Date(user.created_at).toLocaleDateString("nl-NL")}
        </TableCell>
        <TableCell>
          <Badge variant={badgeData[status].color}>
            {badgeData[status].content}
          </Badge>
        </TableCell>
        <TableCell>
          {user.user_type === "vakman"
            ? `€${user.balance?.toFixed(2) || "0.00"}`
            : "-"}
        </TableCell>
        <TableCell>
          <div className="flex gap-2">
            {status === "in_review" ? (
              <Button
                className="text-white"
                onClick={handleAccept}
                disabled={acceptLoading}
              >
                Accepteren
              </Button>
            ) : status === "active" ? (
              <Button
                variant="warning"
                className="text-white"
                onClick={blockUser}
                disabled={blockLoading}
              >
                Blok
              </Button>
            ) : (
              <Button
                variant="warning"
                className="text-white"
                onClick={blockUser}
                disabled={blockLoading}
              >
                Deblokkeren
              </Button>
            )}
            <Button
              variant="error"
              className="text-white"
              onClick={handleDeleteClick}
              disabled={deleteLoading}
            >
              Verwijderen
            </Button>
          </div>
        </TableCell>
      </TableRow>
      <ConfirmModal
        isOpen={isConfirmOpen}
        setIsOpen={setIsConfirmOpen}
        confirmHandler={handleDelete}
        loading={deleteLoading}
        title="Gebruiker verwijderen"
        content="Weet je zeker dat je deze gebruiker wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt."
        variant="danger"
        yesText="Verwijderen"
      />
    </>
  );
};

export default UsersTableRow;
