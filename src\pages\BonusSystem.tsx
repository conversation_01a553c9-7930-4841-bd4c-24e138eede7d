import { useQuery } from "@tanstack/react-query";
import { Clock, Star, ThumbsUp, Trophy } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { ReferralSection } from "@/components/bonus/ReferralSection";
import { BonusHeader } from "@/components/bonus/BonusHeader";
import { BonusCard } from "@/components/bonus/BonusCard";

const BonusSystem = () => {
  const { data: profile, isLoading } = useQuery({
    queryKey: ["profile"],
    queryFn: async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return null;

      const { data: profile, error } = await supabase
        .from("profiles")
        .select("balance")
        .eq("id", user.id)
        .single();

      if (error) throw error;
      return profile;
    },
  });

  // Query voor het ophalen van statistieken
  interface BonusStats {
    totalCompletedJobs: number;
    weeklyCompletedJobs: number;
    averageRating: number;
    quickResponses: number;
  }

  const { data: stats } = useQuery<BonusStats>({
    queryKey: ["bonus-stats"],
    queryFn: async () => {
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();
      if (authError) throw new Error("Authentication failed");
      if (!user) return null;

      // Parallel database queries for better performance
      const [jobsResponse, reviewsResponse] = await Promise.all([
        supabase
          .from("job_responses")
          .select("response_time_minutes, created_at, status")
          .eq("vakman_id", user.id),
        supabase
          .from("vakman_reviews")
          .select("rating")
          .eq("vakman_id", user.id),
      ]);

      if (jobsResponse.error) throw new Error("Failed to fetch jobs");
      if (reviewsResponse.error) throw new Error("Failed to fetch reviews");

      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      const allJobs = jobsResponse.data || [];
      const completedJobs = allJobs.filter((job) => job.status === "completed");
      const weeklyJobs = completedJobs.filter(
        (job) => new Date(job.created_at) >= oneWeekAgo
      );
      const quickResponses = allJobs.filter(
        (job) => job.response_time_minutes <= 60
      ).length;

      const reviews = reviewsResponse.data || [];
      const averageRating = reviews.length
        ? reviews.reduce((sum, rev) => sum + rev.rating, 0) / reviews.length
        : 0;

      return {
        totalCompletedJobs: completedJobs.length,
        weeklyCompletedJobs: weeklyJobs.length,
        averageRating,
        quickResponses,
      };
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2, // Retry failed requests twice
  });

  const bonusItems = [
    {
      title: "Snelle Reactie Bonus",
      description: "Reageer snel op nieuwe klussen en verdien extra credits",
      icon: <Clock className="h-6 w-6 text-blue-500" />,
      reward: "Tot 2 credits per reactie",
      details: [
        "2 credits: Binnen 15 minuten reageren",
        "1 credit: Binnen 1 uur reageren",
      ],
      challenge:
        "Wees de snelste! Hoe sneller je reageert op een nieuwe klus, hoe meer credits je verdient. Dit stimuleert actieve betrokkenheid en geeft klanten sneller toegang tot vakmannen.",
      tips: [
        "Zet notificaties aan voor nieuwe klussen",
        "Houd je agenda up-to-date",
        "Reageer alleen op klussen die je echt kunt uitvoeren",
      ],
      progress: stats?.quickResponses || 0,
      target: 10,
      nextMilestone: "10 snelle reacties",
      gradient: "from-blue-500 to-blue-600",
    },
    {
      title: "Positieve Beoordeling",
      description: "Ontvang credits voor goede beoordelingen van klanten",
      icon: <Star className="h-6 w-6 text-yellow-500" />,
      reward: "Tot 5 credits per beoordeling",
      details: [
        "5 credits voor een 5-sterren beoordeling",
        "3 credits voor een 4-sterren beoordeling",
        "1 credit voor een 3-sterren beoordeling",
      ],
      challenge:
        "Lever topkwaliteit werk en maak een blijvende indruk! Goede beoordelingen zijn goud waard - ze bouwen je reputatie op en leveren extra credits op.",
      tips: [
        "Communiceer duidelijk met de klant",
        "Lever werk op tijd op",
        "Ga net een stapje verder dan verwacht",
      ],
      progress: Math.round((stats?.averageRating || 0) * 20), // Convert 0-5 to 0-100
      target: 100,
      nextMilestone: "5.0 gemiddelde beoordeling",
    },
    {
      title: "Klussen Afronden",
      description: "Verdien credits door klussen succesvol af te ronden",
      icon: <ThumbsUp className="h-6 w-6 text-green-500" />,
      reward: "10 credits per week",
      details: [
        "10 credits: 5+ klussen per week",
        "5 credits: 3-4 klussen per week",
        "2 credits: 1-2 klussen per week",
      ],
      challenge:
        "Bouw een stabiele stroom van klussen op! Hoe meer klussen je succesvol afrondt, hoe meer credits je verdient. Dit beloont consistente en betrouwbare vakmannen.",
      tips: [
        "Plan je week efficiënt",
        "Focus op klussen in dezelfde regio",
        "Houd je planning realistisch",
      ],
      progress: stats?.weeklyCompletedJobs || 0,
      target: 5,
      nextMilestone: "5 klussen deze week",
    },
    {
      title: "Prestatie Mijlpalen",
      description: "Bereik belangrijke mijlpalen en ontvang bonuscredits",
      icon: <Trophy className="h-6 w-6 text-amber-500" />,
      reward: "Oplopende beloningen",
      details: [
        "15 credits: 10 afgeronde klussen",
        "40 credits: 25 afgeronde klussen",
        "100 credits: 50 afgeronde klussen",
        "250 credits: 100 afgeronde klussen",
      ],
      challenge:
        "Ga voor de lange termijn! Deze mijlpalen belonen je toewijding en groei op het platform. Elke mijlpaal die je bereikt, opent de deur naar nog grotere beloningen.",
      tips: [
        "Houd je voortgang bij",
        "Vier kleine overwinningen",
        "Blijf consistent werken aan je doelen",
      ],
      progress: stats?.totalCompletedJobs || 0,
      target: stats?.totalCompletedJobs
        ? stats.totalCompletedJobs < 10
          ? 10
          : stats.totalCompletedJobs < 25
          ? 25
          : stats.totalCompletedJobs < 50
          ? 50
          : 100
        : 10,
      nextMilestone: stats?.totalCompletedJobs
        ? stats.totalCompletedJobs < 10
          ? "10 klussen"
          : stats.totalCompletedJobs < 25
          ? "25 klussen"
          : stats.totalCompletedJobs < 50
          ? "50 klussen"
          : "100 klussen"
        : "10 klussen",
    },
  ];

  return (
    <div className="min-h-[calc(100vh-85px)] bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-6 sm:py-12">
      <div className="container mx-auto px-6 max-w-7xl">
        <BonusHeader profile={profile} isLoading={isLoading} />
        <ReferralSection />
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {bonusItems.map((item, index) => (
            <BonusCard key={index} item={item} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default BonusSystem;
