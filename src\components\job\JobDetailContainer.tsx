import { useState, useEffect } from "react";

import { supabase } from "@/integrations/supabase/client";
import { JobDetailHeader } from "./JobDetailHeader";
import { JobContent } from "./JobContent";

interface JobState {
  title: string;
  description: string;
  job_type: string;
  location: string;
  date: string;
  status: string;
  house_number: string;
  house_number_addition?: string;
  photos: string[];
  hired_craftman_id: string;
}

interface JobDetailContainerProps {
  id?: string;
  title?: string;
  description?: string;
  job_type?: string;
  location?: string;
  date?: string;
  status?: string;
  house_number?: string;
  house_number_addition?: string;
  photos?: string[] | null;
  onBack: () => void;
  isOwner: boolean;
  isDirect?: boolean;
  hired_craftman_id?: string;
}

export const JobDetailContainer = ({
  id,
  title = "",
  description = "",
  job_type = "",
  location = "",
  date = new Date().toISOString(),
  status = "open",
  house_number = "",
  house_number_addition = "",
  photos = [],
  onBack,
  isOwner,
  isDirect,
  hired_craftman_id,
}: JobDetailContainerProps) => {
  const [currentStatus, setCurrentStatus] = useState(status);
  const [currentJob, setCurrentJob] = useState<JobState>({
    title,
    description,
    job_type,
    location,
    date,
    status,
    house_number,
    house_number_addition,
    photos: Array.isArray(photos) ? photos.map((p) => String(p)) : [],
    hired_craftman_id,
  });

  useEffect(() => {
    if (!id || id === "new") {
      return;
    }

    const channel = supabase
      .channel("job_updates")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "jobs",
          filter: `id=eq.${id}`,
        },
        (payload) => {
          if (payload.eventType === "UPDATE" && payload.new) {
            const newJob = payload.new as any;
            setCurrentJob({
              title: newJob.title,
              description: newJob.description,
              job_type: newJob.job_type,
              location: newJob.postal_code,
              date: newJob.created_at,
              status: newJob.status,
              house_number: newJob.house_number,
              house_number_addition: newJob.house_number_addition || undefined,
              photos: Array.isArray(newJob.photos) ? newJob.photos : [],
              hired_craftman_id: newJob.hired_craftman_id,
            });
            setCurrentStatus(newJob.status);
          }
        }
      )
      .subscribe();

    return () => {
      void supabase.removeChannel(channel);
    };
  }, [id]);

  const handleStatusChange = (newStatus: string) => {
    setCurrentStatus(newStatus);
  };

  return (
    <div className="bg-gradient-to-b from-background via-muted/30 to-background h-[calc(100vh-85px)] overflow-y-auto">
      <div className="container mx-auto sm:px-4 px-0 py-8 animate-fade-in">
        <div className="max-w-4xl mx-auto space-y-8 bg-card rounded-xl sm:shadow-lg p-6 sm:border border-border/30">
          <JobDetailHeader
            isOwner={isOwner}
            status={currentStatus}
            onBack={onBack}
            jobId={id}
            onStatusChange={handleStatusChange}
          />

          {id && id !== "new" && (
            <JobContent
              id={id}
              currentJob={currentJob}
              currentStatus={currentStatus}
              isOwner={isOwner}
              isDirect={isDirect}
            />
          )}
        </div>
      </div>
    </div>
  );
};
