import { Mail, Phone } from "lucide-react";

import { useAuth } from "@/components/auth/hooks/useAuth";

interface ContactInformationProps {
  email?: string | null;
  phoneNumber?: string | null;
  isAccepted?: boolean;
}

export const ContactInformation = ({
  email,
  phoneNumber,
  isAccepted = false,
}: ContactInformationProps) => {
  const { userProfile } = useAuth();

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-lg">Contactgegevens</h3>
      <div
        className={`space-y-1 transition-all duration-200 ${
          !isAccepted && userProfile?.email !== email
            ? "blur-sm select-none"
            : ""
        }`}
      >
        {email && (
          <p className="flex items-center gap-2 text-muted-foreground">
            <Mail className="h-4 w-4" />
            {email}
          </p>
        )}
        {phoneNumber && (
          <p className="flex items-center gap-2 text-muted-foreground">
            <Phone className="h-4 w-4" />
            {phoneNumber}
          </p>
        )}
      </div>
      {!isAccepted && (
        <p className="text-sm text-muted-foreground italic">
          Contactgegevens worden zichtbaar na acceptatie
        </p>
      )}
    </div>
  );
};
