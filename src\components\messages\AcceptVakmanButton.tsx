import { CheckCircle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

interface AcceptVakmanButtonProps {
  onClick: () => void;
}

export const AcceptVakmanButton = ({ onClick }: AcceptVakmanButtonProps) => {
  return (
    <Button
      onClick={onClick}
      className="w-full flex items-center gap-2"
      variant="secondary"
    >
      <CheckCircle className="h-4 w-4" />
      Accepteer deze vakman
    </Button>
  );
};
