import { UseFormReturn } from "react-hook-form";
import { Briefcase } from "lucide-react";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface DetailsFieldsProps {
  form: UseFormReturn<any>;
}

export const JobDetailsFields = ({ form }: DetailsFieldsProps) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 pb-2 border-b">
        <div className="bg-blue-100 p-2 rounded-full">
          <Briefcase className="w-5 h-5 text-primary" />
        </div>
        <h2 className="text-base sm:text-lg font-semibold text-accent">
          Klus Details
        </h2>
      </div>

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem className="space-y-2">
            <FormLabel className="text-accent text-sm sm:text-base">
              Beschrijving <span className="text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Beschrijf de klus in detail..."
                className="min-h-[150px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus:border-primary resize-none text-foreground placeholder:text-muted-foreground/70"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="additionalNotes"
        render={({ field }) => (
          <FormItem className="space-y-2">
            <FormLabel className="text-accent text-sm sm:text-base">
              Voorkeursdatum
            </FormLabel>
            <FormControl>
              <Input
                className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus:border-primary resize-none text-foreground placeholder:text-muted-foreground/70"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="additionalNotes"
        render={({ field }) => (
          <FormItem className="space-y-2">
            <FormLabel className="text-accent text-sm sm:text-base">
              Extra opmerkingen
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Eventuele extra informatie..."
                className="min-h-[150px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus:border-primary resize-none text-foreground placeholder:text-muted-foreground/70"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
