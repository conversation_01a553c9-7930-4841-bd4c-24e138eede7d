import { Link, useLocation } from "react-router-dom";

import { VakmanDropdownMenu } from "@/components/VakmanDropdownMenu";
import { KlusplaatserNavigation } from "@/components/KlusplaatserNavigation";

interface HeaderProps {
  isKlusaanvrager?: boolean;
  onLogout: () => void;
  isPublic?: boolean;
}

export const Header = ({
  isKlusaanvrager = false,
  onLogout,
  isPublic,
}: HeaderProps) => {
  const { pathname } = useLocation();

  return (
    <header className="border-b border-b-[#CCCCD9] bg-white dark:bg-slate-900 sticky top-0 z-20">
      <div
        className={`mx-5 ${
          pathname === "/kies-uw-vakman" ? "" : "max-w-7xl sm:mx-auto"
        } sm:py-4 py-2`}
      >
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <span className="text-xl font-bold">Klusgebied</span>
          </Link>

          {!isPublic && (
            <nav className="flex items-center space-x-4">
              {isKlusaanvrager ? (
                <KlusplaatserNavigation />
              ) : (
                <VakmanDropdownMenu onLogout={onLogout} />
              )}
            </nav>
          )}
        </div>
      </div>
    </header>
  );
};
