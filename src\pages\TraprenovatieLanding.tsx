import { useState } from "react";
import { Link } from "react-router-dom";
import {
  CheckCircle,
  Clock,
  Shield,
  Phone,
  FileText,
  MapPin,
  Calendar,
  Info,
  Home,
  Zap,
  Layers,
} from "lucide-react";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";
import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const TraprenovatieLanding = () => {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      rating: 5,
      shortText:
        "Onze trap is binnen één dag volledig gerenoveerd! Prachtige eiken overzettreden en LED-verlichting. Zeer professioneel team...",
      fullText:
        "Onze trap is binnen één dag volledig gerenoveerd! Prachtige eiken overzettreden en LED-verlichting. Zeer professioneel team dat alles netjes heeft afgewerkt. De trap ziet er nu uit als nieuw en de LED-verlichting geeft een luxe uitstraling. Absolute aanrader!",
      verified: true,
    },
    {
      id: 2,
      name: "Dennis",
      rating: 5,
      shortText:
        "Versleten houten trap kreeg een complete metamorfose met PVC overzettreden. Geen breekwerk en binnen 6 uur klaar...",
      fullText:
        "Versleten houten trap kreeg een complete metamorfose met PVC overzettreden. Geen breekwerk en binnen 6 uur klaar. De monteurs waren zeer vakkundig en hebben alles perfect uitgevoerd. De trap is nu geluidsarm en ziet er modern uit. Zeer tevreden!",
      verified: true,
    },
    {
      id: 3,
      name: "Fatima",
      rating: 5,
      shortText:
        "Open trap volledig bekleed met antislip materiaal en nieuwe leuning. Veilig voor de kinderen en prachtig afgewerkt...",
      fullText:
        "Open trap volledig bekleed met antislip materiaal en nieuwe leuning. Veilig voor de kinderen en prachtig afgewerkt. Het team heeft ook de achterzijde netjes afgewerkt. De trap is nu veel veiliger en ziet er strak uit. Uitstekende service!",
      verified: true,
    },
    {
      id: 4,
      name: "Johan",
      rating: 5,
      shortText:
        "Traprenovatie met geluidsdemping was precies wat we nodig hadden. Geen piepende geluiden meer en moderne uitstraling...",
      fullText:
        "Traprenovatie met geluidsdemping was precies wat we nodig hadden. Geen piepende geluiden meer en moderne uitstraling. De specialisten hebben ook automatische LED-sensoren geïnstalleerd. Het resultaat overtreft onze verwachtingen. Zeer professioneel werk!",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={isContactModalOpen}
        setIsOpen={setIsContactModalOpen}
        jobType="traprenovatie"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-2xl font-bold tracking-wide">Klusgebied</h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setIsContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md text-base tracking-wide"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setIsContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md text-base tracking-wide"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-xl mb-2 tracking-wide leading-relaxed">
            Trap versleten, piepend of toe aan een nieuwe look?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Wij renoveren je trap in één dag met nieuwe overzettreden,
            stootborden en LED-verlichting – zonder breekwerk
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-xl tracking-wide leading-relaxed">
            Actief in heel Nederland. Vaak dezelfde dag beschikbaar. 100%
            garantie en de zekerheid van gecertificeerde vakmensen.
          </p>
          <button
            onClick={() => setIsContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Landelijke dekking
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Vaak dezelfde dag beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Clock className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Binnen 1 dag een nieuwe trap
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-anete-lusina-4792504.jpg"
            className="w-full h-full object-cover"
          />
        </div>
      </section>

      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-lg tracking-wide leading-relaxed">
              Onze traprenovatiespecialisten hebben ervaring met alle bekende
              materialen en merken.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-maxavans-5065814.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-octoptimist-2876692.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Versleten, piepende of verouderde trap?
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Een verouderde trap kan de uitstraling van je hele huis negatief
                beïnvloeden. Versleten treden, piepende geluiden en een
                gedateerde look zorgen voor een onprofessionele indruk.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Met een professionele traprenovatie krijgt je trap een complete
                metamorfose. We plaatsen nieuwe overzettreden, stootborden en
                kunnen LED-verlichting of antislip toevoegen voor extra
                veiligheid en sfeer.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Een gerenoveerde trap verhoogt niet alleen de waarde van je
                woning, maar zorgt ook voor meer veiligheid en een moderne
                uitstraling die jaren meegaat.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Traprenovatie Service
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-lg tracking-wide leading-relaxed">
            Wij lossen alle trapproblemen op, van complete renovaties tot
            specifieke verbeteringen. Onze diensten zijn beschikbaar in heel
            Nederland.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Layers
                  className="text-black h-[140px] w-[140px]"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Overzettreden plaatsen
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                We bekleden je bestaande treden met duurzame materialen zoals
                pvc, hout of laminaat. Geen breekwerk, wel een compleet nieuwe
                uitstraling.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Home
                  className="text-black h-[140px] w-[140px]"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Stootborden en zijkanten
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Volledig afgewerkt – van modern strak tot klassiek warm.
                Inclusief leuningen en alle afwerkingsdetails.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Zap
                  className="text-black h-[140px] w-[140px]"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                LED-verlichting installeren
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Ingebouwde LED-spots of strips voor sfeer én veiligheid.
                Automatische sensoren en dimbare opties beschikbaar.
              </p>
            </div>

            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Shield
                  className="text-black h-[140px] w-[140px]"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Antislip & geluidsdemping
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Extra veiligheid voor kinderen of ouderen. Geluidsarme afwerking
                mogelijk voor minder geluidsoverlast.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-blue-bird-7218532.jpg"
                  className="w-[360px] h-[300px] object-cover"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Een versleten houten trap krijgt een complete metamorfose met
                eiken look overzettreden en strakke witte stootborden voor een
                moderne uitstraling.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-blue-bird-7218670.jpg"
                  className="w-[360px] h-[300px] object-cover"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                PVC traprenovatie in betonlook gecombineerd met ingebouwde
                LED-strips zorgt voor een luxe uitstraling en optimale
                veiligheid.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-dominikagregus-3769443.jpg"
                  className="w-[360px] h-[300px] object-cover"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Open trap volledig bekleed met antislip materiaal en nieuwe
                leuning voor maximale veiligheid en een frisse, moderne look.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Basis renovatie
                </h3>
                <p className="text-base tracking-wide">Dichte rechte trap</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Overzettreden plaatsen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Stootborden afwerken
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Keuze uit materialen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Binnen 1 dag klaar
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      5 jaar garantie
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €950,-
                    </span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Uitgebreide renovatie
                </h3>
                <p className="text-base tracking-wide">
                  Open trap of complexe vorm
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Overzettreden plaatsen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Stootborden afwerken
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Zijkanten bekleden
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Achterzijde afwerken
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Maatwerk oplossingen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      5 jaar garantie
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €1.400,-
                    </span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Premium pakket
                </h3>
                <p className="text-base tracking-wide">
                  Met LED en extra opties
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Volledige traprenovatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      LED-verlichting installatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Antislip behandeling
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Geluidsdemping
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Premium materialen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Nieuwe leuning optioneel
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center text-base tracking-wide leading-relaxed">
                      10 jaar garantie
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €2.200,-
                    </span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-tahir-osman-109306362-13185051.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-anete-lusina-4792504.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Binnen 1 dag een compleet nieuwe uitstraling
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Keuze uit tientallen kleuren en afwerkingen
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Extra opties zoals LED-verlichting of antislipstrips
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Geen lawaai, stof of puin – minimale overlast
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Garantie op materiaal en montage
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Hoe lang duurt een traprenovatie?"
              answer="Meestal is de klus in 1 dag klaar. Complexere trappen duren maximaal 2 dagen. We werken efficiënt en zorgen voor minimale overlast tijdens de renovatie."
            />
            <FAQItem
              question="Wat zijn overzettreden precies?"
              answer="Dunne, duurzame treden die over je bestaande trap worden geplaatst – zonder sloopwerk. Ze zijn verkrijgbaar in verschillende materialen zoals hout, PVC en laminaat."
            />
            <FAQItem
              question="Kan ik kiezen uit verschillende kleuren?"
              answer="Ja, we hebben tientallen kleuren, materialen en structuren – van houtlook tot beton. Onze adviseur toont je alle opties tijdens het gratis adviesgesprek."
            />
            <FAQItem
              question="Is mijn trap geschikt voor renovatie?"
              answer="Vrijwel elke trap is te renoveren. Bij twijfel doen we eerst een gratis inspectie om te bepalen welke renovatiemogelijkheden er zijn."
            />
            <FAQItem
              question="Bieden jullie garantie?"
              answer="Ja. Minimaal 5 jaar op materiaal en montage, afhankelijk van het gekozen systeem. Bij premium pakketten bieden we zelfs 10 jaar garantie."
            />
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-fotografiagmazg-9826454.jpg"
                  className="w-[530px] h-[339px] object-cover rounded-lg"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je trap altijd in
                topconditie is. Of het nu gaat om renovatie, LED-verlichting of
                antislip, wij verbinden je met lokaal gecertificeerde vakmensen
                die het vakkundig en snel oplossen.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Heb je een versleten trap of wil je een afspraak inplannen? Wij
                maken het eenvoudig en zorgen dat je snel wordt geholpen door
                onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor een professionele traprenovatie
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 text-lg tracking-wide leading-relaxed">
                Onze traprenovatie experts zijn beschikbaar en kunnen vaak
                dezelfde dag nog op de stoep staan. Klik op de onderstaande knop
                om contact op te nemen.
              </p>
              <button
                onClick={() => setIsContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Stair/pexels-heyho-6908502.jpg"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-base italic tracking-wide leading-relaxed">
            Marco van der Berg – traprenovatie-expert in heel Nederland,
            geselecteerd door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-2xl font-bold tracking-wide">Klusgebied</h3>
              </div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4 tracking-wide">Contact</h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium text-base tracking-wide">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="text-base tracking-wide">Slotermeerlaan 58</p>
                </li>
                <li>
                  <p className="text-base tracking-wide">1064 HC Amsterdam</p>
                </li>
                <li>
                  <p className="text-base tracking-wide">KVK: 93475101</p>
                </li>
                <li>
                  <p className="text-base tracking-wide"><EMAIL></p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-base text-gray-400 tracking-wide">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1] tracking-wide">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TraprenovatieLanding;
