import {
  MessageSquare,
  Briefcase,
  Image,
  Wallet,
  Star,
  User,
  Folder<PERSON><PERSON>,
  TrendingUp,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Card } from "../ui/card";
import { Badge } from "../ui/badge";

interface DashboardItemsProps {
  profile: any;
  balance: number | null;
  reviews: any[];
  averageRating: number | null;
  unreadMessageCount: number;
  unreadResponseCount: number;
  totalUnreadCount?: number;
  onCardClick: (section: string) => void;
}

export const DashboardItems = ({
  profile,
  balance,
  reviews,
  averageRating,
  unreadMessageCount,
  unreadResponseCount,
  onCardClick,
}: DashboardItemsProps) => {
  const navigate = useNavigate();

  const dashboardItems = [
    {
      title: "Beschik<PERSON><PERSON> Klussen",
      description: "Bekijk en reageer op nieuwe klussen",
      onClick: () => onCardClick("available-jobs"),
      icon: <Briefcase className="h-5 w-5 text-white" />,
      gradient: "from-violet-500 to-violet-600",
    },
    {
      title: "Mijn Reacties",
      description: "Bekijk en beheer je reacties op klussen",
      onClick: () => onCardClick("my-responses"),
      notificationCount: unreadResponseCount,
      icon: <MessageSquare className="h-5 w-5 text-white" />,
      gradient: "from-blue-500 to-blue-600",
    },
    // {
    //   title: "Bonussysteem",
    //   description: "Verdien extra's door actief te zijn op het platform",
    //   onClick: () => onCardClick("bonus"),
    //   icon: <Gift className="h-5 w-5 text-white" />,
    //   gradient: "from-green-500 to-green-600",
    // },
    {
      title: "Chats",
      description: "Bekijk en beheer je gesprekken met klusaanvragers",
      onClick: () => onCardClick("chat"),
      notificationCount: unreadMessageCount,
      icon: <MessageSquare className="h-5 w-5 text-white" />,
      gradient: "from-cyan-500 to-cyan-600",
    },
    {
      title: `Saldo: €${balance?.toFixed(2) || "0.00"}`,
      description: "Bekijk en beheer je saldo",
      onClick: () => onCardClick("balance"),
      icon: <Wallet className="h-5 w-5 text-white" />,
      gradient: "from-amber-500 to-amber-600",
    },
    {
      title: "Boekhouding Uitbesteden",
      description:
        "Besteed je administratie uit aan een erkende boekhoudpartner",
      onClick: () => onCardClick("boekhouderij"),
      icon: <FolderOpen className="h-5 w-5 text-white" />,
      gradient: "from-teal-500 to-teal-600",
    },
    {
      title: "Klusgebied+ Investeren",
      description: "Investeer in je toekomst als vakman en groei je bedrijf",
      onClick: () => onCardClick("investment"),
      icon: <TrendingUp className="h-5 w-5 text-white" />,
      gradient: "from-purple-500 to-purple-600",
    },
    ...(profile?.user_type !== "vakman"
      ? [
          {
            title: "Beoordelingen",
            description: `${reviews.length} beoordelingen ontvangen`,
            onClick: () => onCardClick("reviews"),
            icon: <Star className="h-5 w-5 text-white" />,
            gradient: "from-pink-500 to-rose-600",
            extraContent: averageRating && (
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className={`h-3 w-3 ${
                        index < Math.round(averageRating)
                          ? "fill-yellow-400 text-yellow-400"
                          : "fill-gray-200 text-gray-200"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                  {averageRating.toFixed(1)}
                </span>
              </div>
            ),
          },
        ]
      : []),
    ...(profile?.user_type !== "vakman"
      ? [
          {
            title: "Portfolio",
            description: "Beheer je portfolio en laat je beste werk zien",
            onClick: () => onCardClick("portfolio"),
            icon: <Image className="h-5 w-5 text-white" />,
            gradient: "from-emerald-500 to-emerald-600",
          },
        ]
      : []),
    {
      title: "Mijn Profiel",
      description: "Bekijk en bewerk je profielgegevens",
      onClick: () => navigate("/profiel"),
      icon: <User className="h-5 w-5 text-white" />,
      gradient: "from-indigo-500 to-indigo-600",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 py-4">
      {dashboardItems.map((item, index) => (
        <Card
          key={index}
          className="group cursor-pointer transition-all hover:scale-105 duration-300 overflow-hidden bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl"
          onClick={item.onClick}
        >
          <div className="relative p-6">
            <div
              className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${item.gradient} rounded-full opacity-10 -mr-16 -mt-16 transition-opacity group-hover:opacity-20`}
            />

            {(item.title === "Mijn Reacties" || item.title === "Chats") &&
              item.notificationCount > 0 && (
                <div className="absolute top-4 right-2 z-10">
                  <Badge
                    variant="destructive"
                    className="animate-pulse shadow-md font-medium text-sm px-3 py-1"
                  >
                    Nieuwe berichten
                  </Badge>
                </div>
              )}

            <div className="flex items-center gap-4">
              <div
                className={`p-4 rounded-lg bg-gradient-to-br ${item.gradient} shadow-lg group-hover:shadow-xl transition-shadow`}
              >
                {item.icon}
              </div>

              <div>
                <p className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                  {item.title}
                </p>

                <p className="text-gray-600 dark:text-gray-300 text-sm group-hover:text-gray-900 dark:group-hover:text-white transition-colors mt-1">
                  {item.description}
                </p>
              </div>
            </div>

            {item.extraContent && (
              <div className="mt-4 ml-16">{item.extraContent}</div>
            )}
          </div>
        </Card>
      ))}
    </div>
  );
};
