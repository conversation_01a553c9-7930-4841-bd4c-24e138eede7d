import { useState } from "react";
import {
  Phone,
  FileText,
  MapPin,
  Calendar,
  CheckCircle,
  Info,
  Search,
  Wrench,
  Clock,
  Hammer,
  Shield,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";

import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const LekkageLanding = () => {
  const [contactModalOpen, setContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 5,
      shortText:
        "Binnen 2 uur stond er een vakman bij mij voor de deur. Lek was snel gevonden en verholpen. Zeer professioneel!",
      fullText:
        "Binnen 2 uur stond er een vakman bij mij voor de deur. Lek was snel gevonden en verholpen. Zeer professioneel! De monteur gebruikte geavanceerde apparatuur om de lekkage op te sporen zonder schade aan muren. Alles werd netjes opgeruimd en het probleem was definitief opgelost.",
      verified: true,
    },
    {
      id: 2,
      name: "Ismail",
      rating: 5,
      shortText:
        "Onze badkamer had een verborgen lekkage. Super professioneel opgelost én verzekerd. Alles netjes achtergelaten.",
      fullText:
        "Onze badkamer had een verborgen lekkage. Super professioneel opgelost én verzekerd. Alles netjes achtergelaten. De specialist heeft met infraroodcamera's de exacte locatie gevonden en de reparatie uitgevoerd zonder grote ingrepen. Zeer tevreden met de service!",
      verified: true,
    },
    {
      id: 3,
      name: "Bram",
      rating: 5,
      shortText:
        "Ze hebben alles netjes hersteld: muren, schilderwerk, alles. Zeer tevreden met de complete service.",
      fullText:
        "Ze hebben alles netjes hersteld: muren, schilderwerk, alles. Zeer tevreden met de complete service. Na het verhelpen van de lekkage hebben ze ook alle schade professioneel hersteld. Het eindresultaat ziet er uit alsof er nooit een probleem is geweest.",
      verified: true,
    },
    {
      id: 4,
      name: "Sandra",
      rating: 5,
      shortText:
        "Daklekkage binnen 24 uur opgelost. Vakkundige aanpak en duidelijke communicatie over de hele linie.",
      fullText:
        "Daklekkage binnen 24 uur opgelost. Vakkundige aanpak en duidelijke communicatie over de hele linie. Ondanks het slechte weer hebben ze snel een tijdelijke oplossing gemaakt en daarna de definitieve reparatie uitgevoerd. Echt een betrouwbare service!",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={contactModalOpen}
        setIsOpen={setContactModalOpen}
        jobType="lekkage-verhelpen"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-xl lg:text-2xl font-bold tracking-wide">
              Klusgebied
            </h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md text-base lg:text-lg tracking-wide leading-relaxed"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md text-base lg:text-lg tracking-wide leading-relaxed"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-lg lg:text-xl mb-2 tracking-wide leading-relaxed">
            Lekkage in huis of bedrijfspand?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-3xl md:text-4xl lg:text-6xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Wij sporen lekkages snel op en verhelpen ze vakkundig voor een droog
            en veilig gebouw
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-lg lg:text-xl tracking-wide leading-relaxed">
            We zijn actief in heel Nederland en kunnen vaak al dezelfde dag
            langskomen. Je krijgt 100% garantie en de zekerheid van
            gecertificeerde vakmensen.
          </p>
          <button
            onClick={() => setContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base lg:text-lg tracking-wide leading-relaxed"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base lg:text-lg tracking-wide leading-relaxed">
                Actief in Amsterdam, Almere en Utrecht
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base lg:text-lg tracking-wide leading-relaxed">
                Vaak dezelfde dag beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base lg:text-lg tracking-wide leading-relaxed">
                Spoedservice binnen 24 uur
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base lg:text-lg tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/andre-gorham-ii-JihIuB464Ng-unsplash.jpg"
            className="w-full h-full object-cover"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-base lg:text-lg tracking-wide leading-relaxed">
              Onze lekkagespecialisten hebben ervaring met alle bekende
              materialen en merken.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-655995864-1024x1024.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/pawel-czerwinski-D4KI5elMVCY-unsplash.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Lekkage in huis of bedrijfspand?
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Een onopgemerkte lekkage kan grote schade veroorzaken aan je
                eigendom. Vochtproblemen, schimmelvorming en structurele schade
                kunnen het gevolg zijn van een niet-behandelde lekkage.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Met professionele lekdetectie sporen we de bron snel op zonder
                onnodige schade aan muren of vloeren. We gebruiken geavanceerde
                apparatuur zoals infraroodcamera's en vochtmeters.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Een snelle en vakkundige reparatie voorkomt verdere schade en
                zorgt ervoor dat je weer zorgeloos kunt wonen of werken.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Lekkage Detectie & Herstel Service
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-base lg:text-lg tracking-wide leading-relaxed">
            Wij lossen alle lekkageproblemen op, van noodgevallen tot
            preventieve controles. Onze diensten zijn beschikbaar in de Randstad
            en daarbuiten.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Search
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Lekdetectie zonder sloopwerk
              </h3>
              <p className="text-gray-600 text-base lg:text-lg tracking-wide leading-relaxed">
                Spoor verborgen lekkages op met geavanceerde apparatuur zoals
                infraroodcamera's en vochtmeters. Geen onnodige schade aan muren
                of vloeren.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Wrench
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Lekkage reparatie en herstel
              </h3>
              <p className="text-gray-600 text-base lg:text-lg tracking-wide leading-relaxed">
                Vakkundige reparatie van alle soorten lekkages. Van kleine
                reparaties tot complete vervanging van leidingen en afvoeren.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Clock
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                24/7 spoedservice
              </h3>
              <p className="text-gray-600 text-base lg:text-lg tracking-wide leading-relaxed">
                Acute lekkage? Onze spoedservice is 24/7 beschikbaar voor
                noodgevallen. Vaak kunnen we dezelfde dag nog langskomen.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Hammer
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Schade herstel en afwerking
              </h3>
              <p className="text-gray-600 text-base lg:text-lg tracking-wide leading-relaxed">
                Complete herstelservice na lekkage. Van stucwerk en schilderen
                tot tegelreparatie - wij leveren alles netjes op.
              </p>
            </div>

            {/* Service Card 5 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Shield
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Preventieve controles
              </h3>
              <p className="text-gray-600 text-base lg:text-lg tracking-wide leading-relaxed">
                Voorkom lekkages met regelmatige controles. Wij inspecteren
                leidingen, afvoeren en dakbedekking om problemen vroegtijdig te
                signaleren.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-157376761-1024x1024.jpg"
                  className="w-[360px] h-[300px]"
                />
              </div>
              <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                Een verborgen lekkage in de badkamer werd opgespoord met
                infraroodtechnologie en vakkundig gerepareerd zonder sloopwerk.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-2075835395-1024x1024.jpg"
                  className="w-[360px] h-[300px]"
                />
              </div>
              <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                Daklekkage werd binnen 24 uur gelokaliseerd en gerepareerd,
                inclusief vervanging van beschadigde dakbedekking.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-2153980307-1024x1024.jpg"
                  className="w-[360px] h-[300px]"
                />
              </div>
              <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                Complete herstelservice na waterschade: van leidingvervanging
                tot stucwerk en schilderen - alles weer als nieuw.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                24/7 service – Direct hulp bij lekkages
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Wij staan dag en nacht voor je klaar.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Of het nu gaat om een acute lekkage, verborgen waterschade of
                preventieve controle, we lossen het snel op.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Neem contact met ons op en in veel gevallen kunnen we dezelfde
                dag nog langskomen. Zo voorkom je verdere schade en heb je snel
                weer een droog en veilig gebouw.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Onze eigen gecertificeerde experts werken met een persoonlijke
                en doeltreffende aanpak. Geen lange wachttijden, gewoon een
                snelle en efficiënte oplossing.
              </p>
              <p className="mb-6 text-base lg:text-lg tracking-wide leading-relaxed">
                Heb je direct hulp nodig? Neem contact met ons op.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base lg:text-lg tracking-wide leading-relaxed"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-2160304617-1024x1024.jpg"
                  className="w-[530px] h-[353px] object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-2179869620-1024x1024.jpg"
                  className="w-[530px] h-[339px]"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je lekkageproblemen altijd
                vakkundig worden opgelost. Of het nu gaat om detectie, reparatie
                of complete herstelwerkzaamheden, wij verbinden je met lokaal
                gecertificeerde vakmensen die het snel en professioneel
                aanpakken.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Heb je last van een lekkage of wil je een preventieve controle
                inplannen? Wij maken het eenvoudig en zorgen dat je snel wordt
                geholpen door onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/pawel-czerwinski-O8MImzp7ROo-unsplash.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/pexels-henlynndsouza-5711308.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl lg:text-4xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                    24/7 bereikbare spoedservice
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                    Lekdetectie zonder sloopwerk
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                    Complete herstelservice inclusief afwerking
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                    Rapportage voor verzekeraar inbegrepen
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                    Transparante prijzen zonder verrassingen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl lg:text-3xl font-bold tracking-wide leading-relaxed">
                  Lekdetectie
                </h3>
                <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                  Opsporen van lekkages
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Inspectie met apparatuur
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Infrarood en vochtmeting
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Rapportage bevindingen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Voor & na foto's
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Advies vervolgstappen
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm lg:text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl lg:text-5xl font-bold tracking-wide">
                      €150,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base lg:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl lg:text-3xl font-bold tracking-wide leading-relaxed">
                  Reparatie
                </h3>
                <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                  Lekkage verhelpen
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Detectie van lekkage
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Vakkundige reparatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Materiaal inbegrepen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Voor & na foto's
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Garantie op werkzaamheden
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Controle na reparatie
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm lg:text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl lg:text-5xl font-bold tracking-wide">
                      €250,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base lg:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl lg:text-3xl font-bold tracking-wide leading-relaxed">
                  Complete service
                </h3>
                <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                  Detectie + reparatie + herstel
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Volledige lekdetectie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Professionele reparatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Schade herstel
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Stucwerk en schilderen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Tegelreparatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base lg:text-lg tracking-wide leading-relaxed">
                      Rapportage voor verzekering
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center text-base lg:text-lg tracking-wide leading-relaxed">
                      24/7 spoedservice
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm lg:text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl lg:text-5xl font-bold tracking-wide">
                      €450,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base lg:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Kunnen jullie ook spoedklussen uitvoeren?"
              answer="Ja, wij hebben een 24/7 spoedservice waarbij we vaak binnen enkele uren op locatie kunnen zijn. Voor acute lekkages staan we dag en nacht klaar."
            />
            <FAQItem
              question="Hoe sporen jullie verborgen lekkages op?"
              answer="We gebruiken geavanceerde detectieapparatuur zoals infraroodcamera's, vochtmeters en ultrasone apparatuur. Hiermee kunnen we lekkages opsporen zonder onnodige schade aan muren of vloeren."
            />
            <FAQItem
              question="Moet ik mijn verzekering inschakelen bij lekkage?"
              answer="Als er schade is ontstaan door de lekkage, heb je vaak recht op vergoeding van je verzekering. Wij leveren een uitgebreide rapportage die je kunt gebruiken voor je verzekeringsaanvraag."
            />
            <FAQItem
              question="Kunnen jullie ook de schade herstellen na een lekkage?"
              answer="Ja, wij bieden een complete service. Van lekdetectie en reparatie tot het herstellen van schade zoals stucwerk, schilderen en tegelreparatie. Alles uit één hand."
            />
            <FAQItem
              question="Wat zijn de kosten voor lekdetectie?"
              answer="De kosten variëren afhankelijk van de complexiteit en locatie van de lekkage. Eenvoudige detectie start vanaf €150. Je ontvangt altijd vooraf een duidelijke prijsopgave zonder verrassingen."
            />
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl lg:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor snelle hulp bij lekkages
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 text-base lg:text-lg tracking-wide leading-relaxed">
                Onze lekkage experts zijn 24/7 bereikbaar en kunnen vaak
                dezelfde dag nog op de stoep staan. Klik op de onderstaande knop
                om contact op te nemen.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base lg:text-lg tracking-wide leading-relaxed"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Leakage/istockphoto-2197665899-1024x1024.jpg" />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-sm lg:text-base italic tracking-wide leading-relaxed">
            Mark van der Berg – lekkage-expert in Almere, Amsterdam en Utrecht,
            geselecteerd door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl lg:text-2xl font-bold tracking-wide">
                  Klusgebied
                </h3>
              </div>
              <p className="mb-4 text-base lg:text-lg tracking-wide leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-lg lg:text-xl font-bold mb-4 tracking-wide">
                Contact
              </h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium text-base lg:text-lg tracking-wide leading-relaxed">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                    Slotermeerlaan 58
                  </p>
                </li>
                <li>
                  <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                    1064 HC Amsterdam
                  </p>
                </li>
                <li>
                  <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                    KVK: 93475101
                  </p>
                </li>
                <li>
                  <p className="text-base lg:text-lg tracking-wide leading-relaxed">
                    <EMAIL>
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm lg:text-base text-gray-400 tracking-wide leading-relaxed">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1]">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LekkageLanding;
