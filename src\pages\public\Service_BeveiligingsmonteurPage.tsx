/**
 * @description This component renders a comprehensive and SEO-optimized detail page for security technician services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for security technicians. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Shield,
  ShieldCheck,
  Clock,
  ArrowRight,
  Smartphone,
  Star,
  Edit3,
  <PERSON>S<PERSON>re,
  <PERSON>r<PERSON><PERSON><PERSON>,
  CheckCircle,
  MapPin,
  Eye,
} from "lucide-react";

const Service_BeveiligingsmonteurPage = () => {
  usePageTitle(
    "Beveiligingsmonteur Nodig? | Klusgebied - Alarmsystemen & Camera's"
  );
  const navigate = useNavigate();

  const securityServices = [
    {
      icon: Shield,
      title: "Alarmsysteem Installatie",
      description:
        "Professionele installatie van bedrade en draadloze alarmsystemen.",
      points: [
        "BORG-gecertificeerde installatie voor uw verzekering.",
        "Bewegingsmelders, deur-/raamcontacten en sirenes.",
        "Koppeling met een particuliere alarmcentrale (PAC).",
        "Beheer en meldingen via een handige smartphone-app.",
      ],
    },
    {
      icon: Eye,
      title: "Camerabewaking (CCTV)",
      description:
        "Installatie van hoge-resolutie camera's voor binnen en buiten.",
      points: [
        "Haarscherpe HD- en 4K-camera's met nachtzicht.",
        "Live beelden en opnames terugkijken via uw smartphone.",
        "Slimme detectie van personen en voertuigen.",
        "Privacy-vriendelijke installatie conform AVG-wetgeving.",
      ],
    },
    {
      icon: UserCheck,
      title: "Toegangscontrole Systemen",
      description:
        "Systemen met pasjes, codes of biometrie voor gecontroleerde toegang.",
      points: [
        "Ideaal voor bedrijfspanden en appartementencomplexen.",
        "Systemen met pasjes, tags, codes of vingerafdrukscanners.",
        "Gedetailleerde logs van wie waar en wanneer toegang heeft gehad.",
        "Integratie met uw bestaande deuren en poorten.",
      ],
    },
    {
      icon: Clock,
      title: "Onderhoud & Service",
      description: "Periodiek onderhoud en snelle service bij storingen.",
      points: [
        "Jaarlijkse controle van alle componenten.",
        "Software-updates voor de nieuwste functies en beveiliging.",
        "24/7 storingsdienst voor urgente problemen.",
        "Verleng de levensduur van uw beveiligingssysteem.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "BORG Gecertificeerd",
      description:
        "Installatie volgens de hoogste veiligheidsnormen voor uw verzekering.",
    },
    {
      icon: <Smartphone className="w-8 h-8 text-white" />,
      title: "Controle via App",
      description:
        "Beheer uw beveiligingssysteem overal ter wereld via uw smartphone.",
    },
    {
      icon: <Eye className="w-8 h-8 text-white" />,
      title: "Gratis Veiligheidscheck",
      description:
        "Wij bieden een gratis en vrijblijvende analyse van de zwakke plekken.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een alarmsysteem?",
      answer:
        "Een basis alarmsysteem voor een woning kost tussen de €500 en €1500, inclusief installatie. De prijs is afhankelijk van het aantal sensoren en extra functies.",
    },
    {
      question: "Is een BORG-certificaat verplicht?",
      answer:
        "Voor veel inboedelverzekeringen is een BORG-gecertificeerd alarmsysteem een vereiste, zeker bij een hogere inboedelwaarde. Het geeft ook een garantie voor kwaliteit.",
    },
    {
      question: "Kan ik de camerabeelden live bekijken?",
      answer:
        "Ja, al onze moderne camerasystemen zijn te koppelen aan een app op uw smartphone, tablet of computer, zodat u altijd en overal live kunt meekijken.",
    },
    {
      question: "Wat gebeurt er als het alarm afgaat?",
      answer:
        "Afhankelijk van uw abonnement kan het systeem een melding sturen naar uw telefoon, een sirene activeren, of direct een melding doorgeven aan een particuliere alarmcentrale.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Boer",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Na een inbraak in de straat voelden we ons niet meer veilig. De monteur van Klusgebied heeft een top alarmsysteem geïnstalleerd. Snel, vakkundig en heel geruststellend.",
      highlighted: true,
    },
    {
      name: "Winkel van Jansen",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Ons nieuwe camerasysteem is perfect. Haarscherp beeld en makkelijk te bedienen via de app. De monteur dacht goed mee over de beste posities.",
      highlighted: false,
    },
    {
      name: "VvE Zonnebloem",
      location: "Utrecht",
      rating: 4,
      quote:
        "Het toegangscontrolesysteem voor ons appartementencomplex werkt uitstekend. Geen gedoe meer met sleutels. De installatie was netjes en binnen de afgesproken tijd.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1588099768531-a72d4a198538?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1677258523144-a659ca521a07?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxzZWN1cml0eSUyQyUyMHNtYXJ0JTIwaG9tZXxlbnwwfHx8fDE3NTIxODEwNzd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1578096241494-6cc439ab21ad?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwc2VjdXJpdHklMkMlMjBhbGFybSUyMHN5c3RlbSUyQyUyMHN1cnZlaWxsYW5jZSUyMHRlY2hub2xvZ3l8ZW58MHx8fHwxNzUxNzQwNzU5fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Beschrijf uw wens",
      description: "Geef aan wat u wilt beveiligen en wat uw eisen zijn.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang advies",
      description: "Krijg reacties en offertes van gecertificeerde monteurs.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & Beveilig",
      description: "Vergelijk en kies de beste specialist voor uw veiligheid.",
      microcopy: "Vergelijk profielen en certificaten",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Beveiligingsmonteurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "beveiligingsmonteur",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Beveiligingsmonteur Nodig? | Klusgebied - Alarmsystemen & Camera's
        </title>
        <meta
          name="description"
          content="Vind een BORG-gecertificeerde beveiligingsmonteur voor alarmsystemen, camerabewaking en toegangscontrole. Plaats uw klus gratis en ontvang offertes."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-red-100 border border-red-200/80 rounded-full px-4 py-2 mb-6">
                    <Shield className="w-5 h-5 text-red-600" />
                    <span className="text-red-800 font-semibold text-sm">
                      Beveiligingsdiensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Beveiligingsmonteur nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500 mt-2">
                      Een veilig gevoel
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Bescherm wat u dierbaar is met een professioneel
                    alarmsysteem of camerabewaking. Voor een veilig gevoel, dag
                    en nacht.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-red-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-red-600 transition-all duration-300 shadow-lg hover:shadow-red-500/30 transform hover:-translate-y-1"
                    >
                      Vind een beveiligingsmonteur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele beveiligingsmonteur installeert een camera"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte monteur voor uw
                veiligheid.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-red-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Beveiligingsdiensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Complete oplossingen voor de beveiliging van uw huis of
                bedrijfspand.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {securityServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-red-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-red-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun pand lieten beveiligen via
                Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-red-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-red-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted ? "text-red-100" : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted ? "text-red-200" : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een erkende monteur en wees verzekerd van een
                betrouwbaar systeem.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(`/stad/${city.toLowerCase().replace(/\s+/g, "-")}`)
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-red-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-red-600 to-orange-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Uw eigendom beter beveiligen?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw aanvraag en ontvang advies en offertes van
              gecertificeerde beveiligingsmonteurs in uw regio.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een veiligheidscheck aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-red-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-red-600 transition-all duration-300 shadow-lg hover:shadow-red-500/30 transform hover:-translate-y-1"
        >
          <ShieldCheck className="w-5 h-5 mr-2" />
          Vraag offerte aan
        </button>
      </div>
    </div>
  );
};

export default Service_BeveiligingsmonteurPage;
