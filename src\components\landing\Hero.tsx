/**
 * @description This component creates the main hero section for the Klusgebied homepage with a stunning visual background and dynamic search functionality. It is now fully reusable, accepting props for all text content, images, and informational points. The component includes smooth fade-in animations, dynamic service filtering, and responsive design that adapts beautifully across all device sizes. Key variables include props for customization and state for the interactive search functionality.
 */
import { useState, useEffect, useMemo, useRef } from "react";
import { Search, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { questionnaire } from "../werkwijze/new-jobs/questionnaire";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

const Hero = ({
  badgeText = "Nederland's #1 vakmannen platform",
  title = (
    <>
      Vind snel een vakman voor jouw klus
      <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-500">
        Klusgebied.nl
      </span>
    </>
  ),
  subtitle = "Kies uw vakman voor uw klus",
  searchPlaceholder = "Postcode en klus (bijv. 1011AB, loodgieter)",
  searchButtonText = "Zoek Vakman",
  infoPoints = [
    "Vraag gratis offertes aan",
    "Meer dan 2600 vakmannen actief",
    "Ook in jouw buurt",
  ],
  heroImageUrl = "https://heyboss.heeyo.ai/user-assets/Ontwerp zonder titel (2)_ps3zDgB6.png",
  heroImageAlt = "Klusgebied App op iPhone",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isVisible, setIsVisible] = useState(false);
  const [inputFocused, setInputFocused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const navigate = useNavigate();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Memoized filtered items for Command dropdown (like HeroSection)
  const filteredItems = useMemo(() => {
    if (!searchTerm) return questionnaire;

    const predefinedItems = questionnaire.filter((item) =>
      item.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Only add custom item if input doesn't exactly match any predefined items
    const exactMatch = predefinedItems.some(
      (item) => item.label.toLowerCase() === searchTerm.toLowerCase()
    );

    if (!exactMatch && searchTerm.length > 2) {
      return [
        ...predefinedItems,
        { id: "custom", label: searchTerm, icon: Search, questions: [] },
      ];
    }

    return predefinedItems;
  }, [searchTerm]);

  const handleFocus = () => setInputFocused(true);
  const handleBlur = (e: React.FocusEvent<HTMLDivElement>) => {
    // Only close if focus moves outside the container
    if (!containerRef.current?.contains(e.relatedTarget as Node)) {
      setInputFocused(false);
    }
  };

  return (
    <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          {/* Left column: Text and Search */}
          <div className="sm:text-center lg:text-left motion-preset-slide-right">
            <div
              className={`transition-all duration-1000 ${
                isVisible
                  ? "motion-preset-slide-up opacity-100"
                  : "opacity-0 translate-y-10"
              }`}
            >
              {/* Badge */}
              <div className="inline-flex items-center space-x-2 bg-teal-100 border border-teal-200/80 rounded-full px-4 py-2 mb-6 md:mb-8">
                <Sparkles className="w-4 h-4 text-teal-600" />
                <span className="text-teal-800 font-semibold text-sm">
                  {badgeText}
                </span>
              </div>

              <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                {title}
              </h1>

              <p className="text-lg sm:text-xl text-slate-600 md:mb-12 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-4">
                {subtitle}
              </p>
            </div>

            <div className="relative sm:hidden items-center justify-center motion-preset-slide-up">
              <img
                src={heroImageUrl}
                alt={heroImageAlt}
                className="w-auto h-[300px] object-contain transition-transform duration-500 scale-[1.30] hover:scale-[1.35] drop-shadow-2xl"
                loading="lazy"
                width={580}
                height={300}
              />
            </div>

            {/* Enhanced Search Bar */}
            <div
              className={`transition-all duration-1000 delay-300 ${
                isVisible
                  ? "motion-preset-slide-up opacity-100"
                  : "opacity-0 translate-y-10"
              }`}
            >
              <div
                ref={containerRef}
                tabIndex={-1}
                onFocus={handleFocus}
                onBlur={handleBlur}
                className="relative w-full max-w-xl mx-auto z-10"
              >
                <Command
                  filter={(value, search) => {
                    if (!search) return 1;
                    const searchValue = search.toLowerCase().trim();
                    const itemValue = value.toLowerCase().trim();
                    return itemValue.includes(searchValue) ? 1 : 0;
                  }}
                  className="rounded-lg border shadow-md"
                >
                  <CommandInput
                    placeholder={searchPlaceholder}
                    value={searchTerm}
                    onValueChange={setSearchTerm}
                    className="h-16"
                  />
                  {(inputFocused || searchTerm.trim().length > 0) && (
                    <CommandList className="max-h-[300px] overflow-y-auto">
                      <CommandEmpty>Geen suggesties gevonden</CommandEmpty>
                      <CommandGroup heading="Populaire diensten">
                        {filteredItems.map((item) => (
                          <CommandItem
                            key={item.id || item.label}
                            value={item.label}
                            onSelect={() => {
                              if (item.id === "custom") {
                                navigate(
                                  `/plaats-een-klus/custom?title=${encodeURIComponent(
                                    item.label
                                  )}`
                                );
                              } else {
                                navigate(`/plaats-een-klus/${item.id}`);
                              }
                              setSearchTerm("");
                            }}
                            className="cursor-pointer py-3 px-4 hover:bg-secondary"
                          >
                            {item.icon && (
                              <item.icon className="mr-2 h-4 w-4 text-primary" />
                            )}
                            <span className="font-semibold">{item.label}</span>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  )}
                </Command>
                {/* Try to keep the search button below the Command input */}
                {/* <button
                  type="button"
                  onClick={() => {
                    if (searchTerm.trim()) {
                      const predefinedItems = questionnaire.filter((item) =>
                        item.label
                          .toLowerCase()
                          .includes(searchTerm.toLowerCase())
                      );
                      const exactMatch = predefinedItems.find(
                        (item) =>
                          item.label.toLowerCase() === searchTerm.toLowerCase()
                      );
                      if (exactMatch) {
                        navigate(`/plaats-een-klus/${exactMatch.id}`);
                      } else if (searchTerm.trim().length > 2) {
                        navigate(
                          `/plaats-een-klus/custom?title=${encodeURIComponent(
                            searchTerm
                          )}`
                        );
                      } else {
                        document
                          .getElementById("diensten")
                          ?.scrollIntoView({ behavior: "smooth" });
                      }
                    } else {
                      document
                        .getElementById("diensten")
                        ?.scrollIntoView({ behavior: "smooth" });
                    }
                    setSearchTerm("");
                  }}
                  className="w-full mt-4 bg-gradient-to-r from-teal-500 to-teal-600 text-white px-6 py-4 font-bold text-lg hover:from-teal-600 hover:to-teal-700 transition-all duration-300 flex items-center justify-center space-x-3 hover:scale-105 shadow-lg hover:shadow-xl rounded-xl"
                >
                  <Search className="w-6 h-6 mr-2" />
                  <span>{searchButtonText}</span>
                </button> */}
              </div>

              <div className="flex flex-wrap items-center justify-center lg:justify-start mt-8 gap-x-6 gap-y-4 sm:gap-x-8">
                {infoPoints.map((point, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 text-slate-600"
                  >
                    <div
                      className={`w-2 h-2 rounded-full animate-pulse ${
                        ["bg-green-400", "bg-teal-400", "bg-blue-400"][
                          index % 3
                        ]
                      }`}
                    ></div>
                    <span className="text-sm font-medium">{point}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right column: iPhone Mockup */}
          <div className="relative hidden lg:flex items-center justify-center motion-preset-slide-up">
            <img
              src={heroImageUrl}
              alt={heroImageAlt}
              className="w-auto ml-16 h-[467px] object-contain transition-transform duration-500 scale-[1.30] hover:scale-[1.35] drop-shadow-2xl"
              loading="lazy"
              width={580}
              height={470}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
