-- Migration: Create boekhouding aanvragen table
-- Description: Creates the table for storing accounting outsourcing requests from craftsmen

-- Create the accounting_requests table
CREATE TABLE IF NOT EXISTS "accounting_requests" (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  company_name TEXT NOT NULL,
  chamber_of_commerce_number TEXT NOT NULL,
  annual_revenue TEXT NOT NULL,
  accounting_status TEXT NOT NULL,
  invoice_count TEXT NOT NULL,
  comments TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'contacted', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_accounting_requests_user_id ON accounting_requests(user_id);

-- Create index on status for admin filtering
CREATE INDEX IF NOT EXISTS idx_accounting_requests_status ON accounting_requests(status);

-- Create index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_accounting_requests_created_at ON accounting_requests(created_at);

-- Enable Row Level Security
ALTER TABLE accounting_requests ENABLE ROW LEVEL SECURITY;

-- Create policy for users to see only their own requests
CREATE POLICY "Users can view their own accounting requests" ON accounting_requests
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy for users to insert their own requests
CREATE POLICY "Users can insert their own accounting requests" ON accounting_requests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy for users to update their own requests
CREATE POLICY "Users can update their own accounting requests" ON accounting_requests
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy for admins to view all requests
CREATE POLICY "Admins can view all accounting requests" ON accounting_requests
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.user_type = 'admin'
    )
  );

-- Create policy for admins to update all requests
CREATE POLICY "Admins can update all accounting requests" ON accounting_requests
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.user_type = 'admin'
    )
  );

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_accounting_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_accounting_requests_updated_at
  BEFORE UPDATE ON accounting_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_accounting_requests_updated_at();
