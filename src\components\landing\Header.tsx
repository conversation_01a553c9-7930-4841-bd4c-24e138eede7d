/**
 * @description This component renders the main navigation header for the Klusgebied platform with a sticky design and responsive layout. It is now fully reusable, accepting props for logo, brand name, navigation items, and call-to-action buttons. The header adapts between desktop and mobile layouts, now including dropdown menus for services and maintenance contracts on desktop, while maintaining accessibility and modern design principles. Key variables include props for customization, state for scroll and mobile menu behavior, and state for managing dropdown visibility.
 */
import React, { useState, useEffect } from "react";
import { Menu, X, ChevronDown } from "lucide-react";
import { useNavigate } from "react-router-dom";

const serviceCategories = {
  Populair: [
    { label: "Loodgieter", href: "/diensten/loodgieter" },
    { label: "Elektricien", href: "/diensten/elektricien" },
    { label: "Schilder", href: "/diensten/schilder" },
    { label: "Timmerman", href: "/diensten/timmerman" },
    { label: "<PERSON><PERSON><PERSON><PERSON>", href: "/diensten/klusjesman" },
    { label: "Dak<PERSON>ker", href: "/diensten/dakdekker" },
  ],
  "Bouw & Renovatie": [
    { label: "Aannemer Service", href: "/diensten/aannemer-service" },
    { label: "Badkamer Renovatie", href: "/diensten/badkamer-renovatie" },
    { label: "Bouwbedrijf Service", href: "/diensten/bouwbedrijf-service" },
    { label: "Dakbedekking", href: "/diensten/dakbedekking" },
    { label: "Dakkapel Plaatsing", href: "/diensten/dakkapel-plaatsing" },
    { label: "Kozijn Specialist", href: "/diensten/kozijn-specialist" },
    { label: "Traprenovatie", href: "/diensten/traprenovatie" },
  ],
  "Installatie & Techniek": [
    { label: "CV Installateur", href: "/diensten/cv-installateur" },
    { label: "CV Ketel Service", href: "/diensten/cv-ketel-service" },
    { label: "Airco Installateur", href: "/diensten/airco-installateur" },
    { label: "Beveiligingsmonteur", href: "/diensten/beveiligingsmonteur" },
    { label: "Beveiligingscamera", href: "/diensten/beveiligingscamera" },
    { label: "Smart Home Specialist", href: "/diensten/smart-home-specialist" },
    {
      label: "Verlichting Specialist",
      href: "/diensten/verlichting-specialist",
    },
    { label: "Telefoon & Internet", href: "/diensten/telefoon-internet" },
    { label: "Garagedeur Monteur", href: "/diensten/garagedeur-monteur" },
    { label: "Ventilatie Service", href: "/diensten/ventilatie-service" },
    { label: "Monteur", href: "/diensten/monteur" },
  ],
  "Specialistische Diensten": [
    { label: "Lekkage Opsporen", href: "/diensten/lekkage-opsporen" },
    {
      label: "Waterleiding Vervangen",
      href: "/diensten/waterleiding-vervangen",
    },
    { label: "Isolatiemonteur", href: "/diensten/isolatiemonteur" },
    { label: "Isolatie Service", href: "/diensten/isolatie-service" },
    { label: "Tegelvloer Specialist", href: "/diensten/tegelvloer-specialist" },
    {
      label: "Parketvloer Specialist",
      href: "/diensten/parketvloer-specialist",
    },
    { label: "IKEA Montage Service", href: "/diensten/ikea-montage-service" },
  ],
  "Rondom Huis": [
    { label: "Tuinman", href: "/diensten/tuinman" },
    { label: "Tuinbestrating", href: "/diensten/tuinbestrating" },
    { label: "Stoffeerder", href: "/diensten/stoffeerder" },
    { label: "Interieurontwerper", href: "/diensten/interieurontwerper" },
  ],
};

const maintenanceContracts = [
  { label: "CV Ketel Onderhoud", href: "/onderhoudscontract/cv-ketel" },
  { label: "Dakgoot Reiniging", href: "/onderhoudscontract/dakgoot" },
  { label: "Klusjesman Abonnement", href: "/onderhoudscontract/klusjesman" },
  { label: "Huis Inspectie", href: "/onderhoudscontract/huis-inspectie" },
];

const Header = ({
  logoSrc = "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png",
  logoAlt = "Klusgebied Logo - Betrouwbare vakmannen platform",
  brandName = "Klusgebied",
  tagline = "Klussenmarkt van Nederland",
  navItems = [{ label: "Klusgebied App", href: "/app" }],
  vakmanItems = [
    { label: "Overzicht voor Vakmensen", href: "/vakman" },
    { label: "Investeringsmogelijkheden", href: "/investeren" },
    { label: "Boekhouding", href: "/boekhouding" },
  ],
  ctaButton = { label: "Plaats een klus", href: "/plaats-een-klus" },
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleNavClick = (href) => {
    if (href.startsWith("/")) {
      navigate(href);
    } else if (href.startsWith("#")) {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
    setIsMenuOpen(false);
    setOpenDropdown(null);
    setMobileOpen(null);
  };

  const Dropdown = ({
    items,
    isCategorized = false,
  }: {
    items:
      | Record<string, Array<{ label: string; href: string }>>
      | Array<{ label: string; href: string }>;
    isCategorized?: boolean;
  }) => (
    <div
      className={`absolute top-full mt-2 bg-white rounded-lg shadow-xl p-6 z-20 motion-preset-slide-down-fade ${
        isCategorized
          ? "w-auto max-w-7xl left-1/2 -translate-x-1/2"
          : "w-64 left-1/2 -translate-x-1/2"
      }`}
    >
      {isCategorized ? (
        <div className="flex flex-wrap gap-x-8">
          {Object.entries(items).map(([category, services]) => (
            <div key={category} className="flex-shrink-0 space-y-3">
              <h3 className="font-bold text-sm text-slate-500 uppercase tracking-wider px-3">
                {category}
              </h3>
              <div className="flex flex-col">
                {services.map((item) => (
                  <button
                    key={item.label}
                    onClick={() => handleNavClick(item.href)}
                    className="w-full text-left text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 py-2 px-3 rounded-md hover:bg-slate-50 focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : Array.isArray(items) ? (
        <div className="grid grid-cols-1 gap-y-2">
          {items.map((item) => (
            <button
              key={item.label}
              onClick={() => handleNavClick(item.href)}
              className="text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 text-left py-2 px-3 rounded-md hover:bg-slate-50 focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
            >
              {item.label}
            </button>
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );

  // Zoom scale for public pages (should match PublicLayout)
  const ZOOM_SCALE = 0.8;
  const widthPercent = `${100 / ZOOM_SCALE}%`;
  const marginLeftPercent = `${-(100 / ZOOM_SCALE - 100) / 2}%`;
  const zoomStyle = {
    transform: `scale(${ZOOM_SCALE})`,
    transformOrigin: "top center",
    width: widthPercent,
    marginLeft: marginLeftPercent,
  };

  return (
    <>
      <header
        style={zoomStyle}
        className={`fixed sm:w-full top-0 z-50 transition-all duration-500 ${
          isScrolled
            ? "bg-white shadow-xl border-b border-slate-200/50"
            : "bg-white"
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16 lg:h-20">
            {/* Logo */}
            <div
              className="flex items-center group cursor-pointer"
              onClick={() => navigate("/")}
            >
              <img
                src={logoSrc}
                alt={logoAlt}
                className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 motion-preset-slide-right group-hover:scale-110 transition-transform duration-300"
                width="48"
                height="48"
                loading="lazy"
              />
              <div className="ml-2 sm:ml-3">
                <span className="block text-xl sm:text-2xl lg:text-3xl font-bold text-slate-800 group-hover:text-teal-600 transition-colors duration-300">
                  {brandName}
                </span>
                {tagline && (
                  <p className="text-xs text-slate-500 mt-0 sm:mt-1">
                    {tagline}
                  </p>
                )}
              </div>
            </div>

            {/* Desktop Navigation & CTA */}
            <div className="hidden lg:flex items-center space-x-2">
              {/* Diensten Dropdown */}
              <div className="static">
                <div className="flex items-center rounded-md transition-colors duration-200">
                  <button
                    onClick={() => handleNavClick("/diensten")}
                    className="text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 hover:scale-105 px-3 py-2 rounded-l-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                  >
                    Diensten
                  </button>
                  <div className="h-4 w-px bg-slate-200"></div>
                  <button
                    onClick={() =>
                      setOpenDropdown(
                        openDropdown === "diensten" ? null : "diensten"
                      )
                    }
                    className="p-2 text-slate-700 hover:text-teal-600 rounded-r-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                    aria-haspopup="true"
                    aria-expanded={openDropdown === "diensten"}
                  >
                    <span className="sr-only">Open diensten menu</span>
                    <ChevronDown
                      size={16}
                      className={`transition-transform duration-200 ${
                        openDropdown === "diensten" ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                </div>
                {openDropdown === "diensten" && (
                  <Dropdown items={serviceCategories} isCategorized={true} />
                )}
              </div>

              {/* Onderhoudscontracten Dropdown */}
              <div className="relative">
                <div className="flex items-center rounded-md transition-colors duration-200">
                  <button
                    onClick={() => handleNavClick("/onderhoudscontracten")}
                    className="text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 hover:scale-105 px-3 py-2 rounded-l-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                  >
                    Onderhoudscontracten
                  </button>
                  <div className="h-4 w-px bg-slate-200"></div>
                  <button
                    onClick={() =>
                      setOpenDropdown(
                        openDropdown === "contracten" ? null : "contracten"
                      )
                    }
                    className="p-2 text-slate-700 hover:text-teal-600 rounded-r-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                    aria-haspopup="true"
                    aria-expanded={openDropdown === "contracten"}
                  >
                    <span className="sr-only">
                      Open onderhoudscontracten menu
                    </span>
                    <ChevronDown
                      size={16}
                      className={`transition-transform duration-200 ${
                        openDropdown === "contracten" ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                </div>
                {openDropdown === "contracten" && (
                  <Dropdown items={maintenanceContracts} />
                )}
              </div>

              {navItems.map((item) => (
                <button
                  key={item.label}
                  onClick={() => handleNavClick(item.href)}
                  className="text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 hover:scale-105 px-3 py-2 rounded-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                >
                  {item.label}
                </button>
              ))}

              {/* Voor Vakmensen Dropdown */}
              <div className="relative">
                <div className="flex items-center rounded-md transition-colors duration-200">
                  <button
                    onClick={() => handleNavClick("/vakman")}
                    className="text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 hover:scale-105 px-3 py-2 rounded-l-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                  >
                    Voor Vakmensen
                  </button>
                  <div className="h-4 w-px bg-slate-200"></div>
                  <button
                    onClick={() =>
                      setOpenDropdown(
                        openDropdown === "vakman" ? null : "vakman"
                      )
                    }
                    className="p-2 text-slate-700 hover:text-teal-600 rounded-r-md focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:outline-none"
                    aria-haspopup="true"
                    aria-expanded={openDropdown === "vakman"}
                  >
                    <span className="sr-only">Open voor vakmensen menu</span>
                    <ChevronDown
                      size={16}
                      className={`transition-transform duration-200 ${
                        openDropdown === "vakman" ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                </div>
                {openDropdown === "vakman" && <Dropdown items={vakmanItems} />}
              </div>

              <button
                onClick={() => handleNavClick(ctaButton.href)}
                className="bg-teal-500 text-white px-4 py-2 sm:px-6 sm:py-2.5 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-teal-500/20 hover:-translate-y-0.5 inline-block"
              >
                {ctaButton.label}
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-3 text-slate-700 hover:text-teal-600 transition-all duration-300 hover:scale-110 hover:bg-teal-50 rounded-lg"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden py-4 border-t border-slate-200/50 motion-preset-slide-down bg-white/95 backdrop-blur-lg rounded-b-2xl shadow-xl">
              <nav className="flex flex-col space-y-1 px-4">
                <button
                  onClick={() => handleNavClick("/diensten")}
                  className="w-full text-left text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 py-3 px-4 rounded-lg hover:bg-teal-50"
                >
                  Diensten
                </button>

                {/* Onderhoudscontracten mobile */}
                <div>
                  <button
                    onClick={() =>
                      setMobileOpen(
                        mobileOpen === "contracten" ? null : "contracten"
                      )
                    }
                    className="w-full flex justify-between items-center text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 text-left py-3 px-4 rounded-lg hover:bg-teal-50"
                  >
                    <span>Onderhoudscontracten</span>
                    <ChevronDown
                      size={16}
                      className={`transition-transform duration-200 ${
                        mobileOpen === "contracten" ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                  {mobileOpen === "contracten" && (
                    <div className="pl-4 mt-1 space-y-1 motion-preset-fade-down">
                      {maintenanceContracts.map((item) => (
                        <button
                          key={item.label}
                          onClick={() => handleNavClick(item.href)}
                          className="w-full text-left text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 py-2 px-3 rounded-md hover:bg-slate-100"
                        >
                          {item.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {navItems.map((item) => (
                  <button
                    key={item.label}
                    onClick={() => handleNavClick(item.href)}
                    className={`text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 text-left py-3 px-4 rounded-lg hover:bg-teal-50`}
                  >
                    {item.label}
                  </button>
                ))}

                {/* Voor Vakmensen mobile */}
                <div>
                  <button
                    onClick={() =>
                      setMobileOpen(mobileOpen === "vakman" ? null : "vakman")
                    }
                    className="w-full flex justify-between items-center text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 text-left py-3 px-4 rounded-lg hover:bg-teal-50"
                  >
                    <span>Voor Vakmensen</span>
                    <ChevronDown
                      size={16}
                      className={`transition-transform duration-200 ${
                        mobileOpen === "vakman" ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                  {mobileOpen === "vakman" && (
                    <div className="pl-4 mt-1 space-y-1 motion-preset-fade-down">
                      {vakmanItems.map((item) => (
                        <button
                          key={item.label}
                          onClick={() => handleNavClick(item.href)}
                          className="w-full text-left text-slate-700 hover:text-teal-600 font-medium transition-all duration-300 py-2 px-3 rounded-md hover:bg-slate-100"
                        >
                          {item.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                <div className="pt-4">
                  <button
                    onClick={() => handleNavClick(ctaButton.href)}
                    className="bg-teal-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg block text-center w-full"
                  >
                    {ctaButton.label}
                  </button>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
};

export default Header;
