export interface PortfolioProject {
  id: string;
  title: string;
  description: string | null;
  photos: { photo_url: string }[];
  budget: number | null;
  created_at: string;
}

export interface Vakman {
  id: string;
  company_name: string;
  email: string;
  user_type: string;
  profile_photo_url: string | null;
  averageRating: number | null;
  totalReviews: number;
  services?: string[] | null;
  first_name?: string | null;
  last_name?: string | null;
  phone_number?: string | null;
  kvk_number?: string | null;
  btw_number?: string | null;
  street_address?: string | null;
  city?: string | null;
  postal_code?: string | null;
  status?: string | null;
  diplomas?: any[] | null;
  portfolioProjectCount?: number;
  qualityScore?: number;
}
