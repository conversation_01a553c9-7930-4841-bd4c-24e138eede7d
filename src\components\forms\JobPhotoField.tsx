import { UseFormReturn } from "react-hook-form";
import { Image, XCircle } from "lucide-react";
import { useState, useEffect } from "react";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";

interface PhotoFieldProps {
  form: UseFormReturn<any>;
  existingPhotos?: string[];
  onRemoveExistingPhoto?: (url: string) => void;
}

export const JobPhotoField = ({
  form,
  existingPhotos = [],
  onRemoveExistingPhoto,
}: PhotoFieldProps) => {
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const { toast } = useToast();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    const existingFiles = form.getValues("photos") || [];
    const totalFiles =
      existingFiles.length + files.length + existingPhotos.length;

    if (totalFiles > 4) {
      toast({
        variant: "destructive",
        title: "Te veel foto's",
        description: "Je kunt maximaal 4 foto's uploaden",
      });
      e.target.value = "";
      return;
    }

    const newFiles = Array.from(files);
    const allFiles = [...existingFiles, ...newFiles];
    form.setValue("photos", allFiles);

    const newPreviewUrls = newFiles.map((file) => URL.createObjectURL(file));
    setPreviewUrls((prev) => [...prev, ...newPreviewUrls]);
  };

  const handleRemovePhoto = (index: number) => {
    const currentFiles = form.getValues("photos") || [];
    const updatedFiles = currentFiles.filter((_, i) => i !== index);
    form.setValue("photos", updatedFiles);

    URL.revokeObjectURL(previewUrls[index]);
    setPreviewUrls((prev) => prev.filter((_, i) => i !== index));
  };

  useEffect(() => {
    return () => {
      previewUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  }, []);

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-3 pb-2 border-b">
        <div className="bg-blue-100 p-2 rounded-full">
          <Image className="w-5 h-5 text-primary" />
        </div>
        <h2 className="text-lg font-semibold text-accent">Foto's</h2>
      </div>

      <FormField
        control={form.control}
        name="photos"
        render={() => (
          <FormItem>
            <FormLabel className="text-accent">
              Upload foto's (maximaal 4)
            </FormLabel>
            <FormControl>
              <div className="space-y-4">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleFileChange}
                  className="bg-white border-gray-200 focus:border-primary cursor-pointer flex items-center justify-center file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-white file:text-black hover:file:bg-gray-100 file:transition-colors text-center"
                />

                {existingPhotos.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Bestaande foto's:
                    </p>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                      {existingPhotos.map((url, index) => (
                        <div
                          key={`existing-${index}`}
                          className="relative aspect-square rounded-lg overflow-hidden border border-gray-200 group bg-gray-50"
                        >
                          <img
                            src={url}
                            alt={`Bestaande foto ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                          {onRemoveExistingPhoto && (
                            <Button
                              type="button"
                              variant="destructive"
                              size="icon"
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => onRemoveExistingPhoto(url)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {previewUrls.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Nieuwe foto's:
                    </p>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                      {previewUrls.map((url, index) => (
                        <div
                          key={`preview-${index}`}
                          className="relative aspect-square rounded-lg overflow-hidden border border-gray-200 group bg-gray-50"
                        >
                          <img
                            src={url}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleRemovePhoto(index)}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
