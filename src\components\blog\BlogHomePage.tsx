/**
 * Blog homepage component with dynamic content fetched from Supabase database.
 * Features a compelling hero section and displays blog articles in a responsive grid layout.
 */
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  Calendar,
  Clock,
  ArrowRight,
  Search,
  MapPin,
  Filter,
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

interface Author {
  id: number;
  name: string;
  avatar_url?: string;
  bio?: string;
  linkedin_url?: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  category: string;
  author_id: number;
  cover_image_url?: string;
  tags: string[];
  city?: string;
  region?: string;
  seo_title?: string;
  seo_description?: string;
  published: boolean;
  published_at: string;
  created_at: string;
  updated_at: string;
  author: Author;
}

export const BlogHomePage: React.FC = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("");

  const categories = [
    "SEO",
    "Marketing",
    "Technologie",
    "Zakelijk",
    "Webdesign",
    "E-commerce",
    "Sociale Media",
    "Content Strategie",
  ];

  const regions = [
    "Noord-Holland",
    "Zuid-Holland",
    "Utrecht",
    "Noord-Brabant",
    "Gelderland",
    "Overijssel",
    "Limburg",
    "Friesland",
    "Groningen",
    "Drenthe",
    "Flevoland",
    "Zeeland",
  ];

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from("posts" as any)
        .select(
          `
          *,
          author:authors(name, avatar_url, bio, linkedin_url)
        ` as any
        )
        .eq("published", true)
        .order("published_at", { ascending: false });

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching posts:", error);
        return;
      }

      setPosts((data as unknown as BlogPost[]) || []);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      !searchTerm ||
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesCategory =
      !selectedCategory || post.category === selectedCategory;
    const matchesRegion = !selectedRegion || post.region === selectedRegion;

    return matchesSearch && matchesCategory && matchesRegion;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("nl-NL", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.replace(/<[^>]*>/g, "").split(/\s+/).length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min leestijd`;
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-teal-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Klus Blog
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
            Jouw definitieve bron voor bruikbare strategieën in SEO, technologie
            en bedrijfsgroei. Expert inzichten voor de moderne professional.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
            <div className="relative flex-1 w-full">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                size={20}
              />
              <Input
                type="text"
                placeholder="Zoek artikelen..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white border-gray-300 text-gray-900 placeholder-gray-500"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="bg-white border-b border-gray-200 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Filter size={20} className="text-gray-500" />
              <span className="font-medium text-gray-700">Filters:</span>
            </div>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Alle categorieën</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={selectedRegion}
              onChange={(e) => setSelectedRegion(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Alle regio's</option>
              {regions.map((region) => (
                <option key={region} value={region}>
                  {region}
                </option>
              ))}
            </select>

            {(selectedCategory || selectedRegion || searchTerm) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedCategory("");
                  setSelectedRegion("");
                  setSearchTerm("");
                }}
                className="text-sm"
              >
                Filters wissen
              </Button>
            )}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="overflow-hidden">
                  <div className="h-48 bg-gray-200 animate-pulse"></div>
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-6 bg-gray-200 rounded animate-pulse mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredPosts.length === 0 ? (
            <div className="text-center py-16">
              <h3 className="text-2xl font-semibold text-gray-700 mb-4">
                Geen artikelen gevonden
              </h3>
              <p className="text-gray-500 mb-8">
                Probeer je zoekopdracht aan te passen of de filters te wijzigen.
              </p>
              <Button
                onClick={() => {
                  setSelectedCategory("");
                  setSelectedRegion("");
                  setSearchTerm("");
                }}
              >
                Alle artikelen bekijken
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post) => (
                <Card
                  key={post.id}
                  className="overflow-hidden hover:shadow-lg transition-shadow duration-300"
                >
                  {post.cover_image_url && (
                    <div className="h-48 overflow-hidden">
                      <img
                        src={post.cover_image_url}
                        alt={post.title}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}

                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="secondary" className="text-xs">
                        {post.category}
                      </Badge>
                      {post.city && (
                        <div className="flex items-center text-xs text-gray-500">
                          <MapPin size={12} className="mr-1" />
                          {post.city}
                        </div>
                      )}
                    </div>

                    <h3 className="text-xl font-semibold mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                      <Link to={`/weblogs/${post.slug}`}>{post.title}</Link>
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.summary}
                    </p>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        {formatDate(post.published_at)}
                      </div>
                      <div className="flex items-center">
                        <Clock size={14} className="mr-1" />
                        {getReadingTime(post.content)}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {post.author.avatar_url && (
                          <img
                            src={post.author.avatar_url}
                            alt={post.author.name}
                            className="w-8 h-8 rounded-full mr-2"
                          />
                        )}
                        <span className="text-sm text-gray-600">
                          {post.author.name}
                        </span>
                      </div>

                      <Link
                        to={`/weblogs/${post.slug}`}
                        className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                      >
                        Lees meer
                        <ArrowRight size={14} className="ml-1" />
                      </Link>
                    </div>

                    {post.tags.length > 0 && (
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="flex flex-wrap gap-1">
                          {post.tags.slice(0, 3).map((tag, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                          {post.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{post.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {filteredPosts.length > 0 && (
            <div className="text-center mt-12">
              <p className="text-gray-600">
                {filteredPosts.length} artikel
                {filteredPosts.length !== 1 ? "en" : ""} gevonden
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default BlogHomePage;
