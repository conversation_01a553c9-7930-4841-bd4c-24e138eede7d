import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Questionnaire } from "./types";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { FileUploadButton } from "./FileUploadButton";
import { CreateProfileForm, ProfileFormData } from "./CreateProfileForm";
import { serviceCategories } from "@/components/profile/ProfileServices";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { AnimatePresence, motion } from "framer-motion";

// Types
interface QuestionSelectorProps {
  questions: Questionnaire[];
  onValueChange: (
    value: string[],
    files: File[],
    profile?: ProfileFormData,
    services?: string[]
  ) => void;
  isLoading: boolean;
  variant?: "new" | "old";
  hasCustomQuestion?: boolean;
  isCustomJob?: boolean;
}

interface Answer {
  value: string;
  id: number;
}

// Add StepObj type
type StepObj =
  | { type: "base"; question: Questionnaire; idx: number }
  | { type: "custom" | "services" | "profile"; idx: number };

// Subcomponents
const QuestionOption = ({
  option,
  isSelected,
  onClick,
}: {
  option: string;
  isSelected: boolean;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md border border-gray-200 dark:border-gray-700 transition-all duration-200",
      isSelected
        ? "border-primary text-white bg-primary"
        : "hover:bg-secondary hover:border-primary/30 hover:text-primary dark:hover:bg-gray-800"
    )}
    aria-selected={isSelected}
  >
    <span className="text-sm">{option}</span>
  </button>
);

const QuestionItem = ({
  question,
  options,
  id,
  selectedValue,
  onSelect,
}: {
  question: string;
  options?: string[];
  id: number;
  selectedValue?: string;
  onSelect: (value: string) => void;
}) => (
  <div className="space-y-4">
    <p className="text-[23px] mb-3 text-gray-700 dark:text-gray-300">
      {question}
    </p>
    <div className="space-y-3">
      {options?.map((option, idx) => (
        <QuestionOption
          key={idx}
          option={option}
          isSelected={option === selectedValue}
          onClick={() => onSelect(option)}
        />
      ))}
    </div>
  </div>
);

const ServicesSelector = ({
  selectedServices,
  onServicesChange,
}: {
  selectedServices: string[];
  onServicesChange: (services: string[]) => void;
}) => {
  const handleServiceToggle = (service: string) => {
    onServicesChange(
      selectedServices.includes(service)
        ? selectedServices.filter((s) => s !== service)
        : [...selectedServices, service]
    );
  };

  return (
    <div className="space-y-4">
      <p className="text-[23px] mb-3 text-gray-700 dark:text-gray-300">
        Welke diensten heeft u nodig voor deze klus?
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {serviceCategories.map((category) => (
          <Popover key={category.name}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start gap-2 h-auto py-3"
              >
                <div className="flex flex-col items-start">
                  <span className="font-medium">{category.name}</span>
                  <span className="text-sm text-muted-foreground">
                    {selectedServices.filter((s) =>
                      category.services.includes(s)
                    ).length || "Geen"}{" "}
                    geselecteerd
                  </span>
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 bg-white">
              <ScrollArea className="pr-4 h-[250px]">
                <div className="space-y-2">
                  {category.services.map((service) => (
                    <div key={service} className="flex items-center space-x-2">
                      <Checkbox
                        id={service}
                        checked={selectedServices.includes(service)}
                        onCheckedChange={() => handleServiceToggle(service)}
                      />
                      <label
                        htmlFor={service}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {service}
                      </label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </PopoverContent>
          </Popover>
        ))}
      </div>

      {selectedServices.length > 0 && (
        <div className="mt-4 p-4 bg-muted rounded-lg">
          <h3 className="font-medium mb-2">Geselecteerde diensten:</h3>
          <div className="flex flex-wrap gap-2">
            {selectedServices.map((service) => (
              <div
                key={service}
                className="bg-background px-2 py-1 rounded-md text-sm flex items-center gap-1"
              >
                {service}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => handleServiceToggle(service)}
                >
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export function QuestionSelector({
  questions,
  onValueChange,
  isLoading,
  variant = "old",
  hasCustomQuestion = false,
  isCustomJob = false,
}: QuestionSelectorProps) {
  const scrollRef = useRef<HTMLDivElement>(null);

  const [fileList, setFileList] = useState<File[]>([]);
  const [revealedStepCount, setRevealedStepCount] = useState(1);
  const [showError, setShowError] = useState(false);
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const stepRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [stepOpacities, setStepOpacities] = useState<number[]>([]);

  const baseQuestions = hasCustomQuestion
    ? questions.slice(0, questions.length - 1)
    : questions;
  const originQuestionCount = baseQuestions.length;

  // Build the step list
  const stepList: StepObj[] = [
    ...baseQuestions.map(
      (q, i) => ({ type: "base", question: q, idx: i + 1 } as const)
    ),
    { type: "custom", idx: originQuestionCount + 1 } as const,
    { type: "services", idx: originQuestionCount + 2 } as const,
    ...(variant === "new"
      ? [{ type: "profile", idx: originQuestionCount + 3 } as const]
      : []),
  ];

  // Only render revealed steps
  const visibleSteps: StepObj[] = stepList.slice(0, revealedStepCount);

  // Helper: is step completed?
  const isStepCompleted = (stepObj: StepObj) => {
    if (stepObj.type === "base") {
      return !!answers.find((a) => a.id === stepObj.question.id)?.value;
    } else if (stepObj.type === "custom") {
      return !!answers.find((a) => a.id === originQuestionCount + 1)?.value;
    } else if (stepObj.type === "services") {
      // You can add more logic here if needed
      return true;
    } else if (stepObj.type === "profile") {
      return true;
    }
    return false;
  };

  // Next button: only reveal next step if current is completed
  const handleNext = () => {
    const currentIdx = visibleSteps.length - 1;
    const currentStepObj = visibleSteps[currentIdx];
    if (!isStepCompleted(currentStepObj)) {
      setShowError(true);
      return;
    }
    setShowError(false);
    if (revealedStepCount < stepList.length) {
      setRevealedStepCount(revealedStepCount + 1);
      setTimeout(() => {
        const el = stepRefs.current[revealedStepCount];
        if (el) {
          el.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }, 50);
    } else {
      // Final submit
      onValueChange(
        answers.map((a) => a.value),
        fileList,
        undefined,
        selectedServices
      );
    }
  };

  // Previous button: remove last step
  const handlePrev = () => {
    if (revealedStepCount > 1) {
      setRevealedStepCount(revealedStepCount - 1);
      setTimeout(() => {
        const el = stepRefs.current[revealedStepCount - 2];
        if (el) {
          el.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }, 50);
    }
  };

  // On scroll, update opacity for each step except the last
  useEffect(() => {
    const onScroll = () => {
      const newOpacities = visibleSteps.map((_, idx) => {
        if (idx === visibleSteps.length - 1) return 1;
        const el = stepRefs.current[idx];
        if (!el) return 0.4;
        const rect = el.getBoundingClientRect();
        const visibleHeight = Math.max(
          0,
          Math.min(rect.bottom, window.innerHeight) - Math.max(rect.top, 0)
        );
        const ratio = visibleHeight / rect.height;
        return ratio >= 1 ? 1 : 0.4;
      });
      setStepOpacities(newOpacities);
    };
    window.addEventListener("scroll", onScroll, { passive: true });
    onScroll(); // initial
    return () => window.removeEventListener("scroll", onScroll);
  }, [visibleSteps.length]);

  const handleProfileSubmit = useCallback(
    (value: ProfileFormData) => {
      setShowError(false);
      onValueChange(
        answers.map((a) => a.value),
        fileList,
        value,
        selectedServices
      );
    },
    [answers, fileList, onValueChange, selectedServices]
  );

  const handleAnswerSelect = useCallback((id: number, value: string) => {
    setAnswers((prev) => {
      const filtered = prev.filter((item) => item.id !== id);
      return [...filtered, { id, value }];
    });
    setShowError(false);
  }, []);

  return (
    <div
      className="flex flex-col gap-10 w-full sm:px-4 relative"
      style={{
        paddingBottom: `${Math.ceil((window.innerHeight - 550) / 2)}px`,
        minHeight: 600,
      }}
    >
      <div className="flex flex-col gap-6" style={{ minHeight: 400 }}>
        <AnimatePresence initial={false}>
          {visibleSteps.map((stepObj, idx) => {
            // Only n-1th step (previous) gets opacity logic
            let opacity = 1;
            if (idx === visibleSteps.length - 2) {
              opacity = stepOpacities[idx] ?? 0.4;
            }
            let content = null;
            if (stepObj.type === "base") {
              const baseStep = stepObj as {
                type: "base";
                question: Questionnaire;
                idx: number;
              };
              content = (
                <QuestionItem
                  {...baseStep.question}
                  selectedValue={
                    answers.find((a) => a.id === baseStep.question.id)?.value
                  }
                  onSelect={(value) =>
                    handleAnswerSelect(baseStep.question.id, value)
                  }
                />
              );
            } else if (stepObj.type === "custom") {
              content = (
                <div className="space-y-2">
                  <Label
                    htmlFor="description"
                    className="text-[23px] text-gray-700 dark:text-gray-300 font-normal"
                  >
                    {questions[originQuestionCount]?.question || "Beschrijving"}
                  </Label>
                  <Textarea
                    id="description"
                    placeholder={
                      questions[originQuestionCount]?.placeholder ||
                      "Beschrijf de klus in detail..."
                    }
                    className="min-h-[250px] bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus-visible:ring-0 focus:border-primary resize-none text-foreground placeholder:text-muted-foreground/70"
                    value={
                      answers.find(
                        (item) => item.id === originQuestionCount + 1
                      )?.value ?? ""
                    }
                    onChange={(e) =>
                      handleAnswerSelect(
                        originQuestionCount + 1,
                        e.target.value
                      )
                    }
                  />
                </div>
              );
            } else if (stepObj.type === "services") {
              content = (
                <div className="space-y-8">
                  {isCustomJob && (
                    <ServicesSelector
                      selectedServices={selectedServices}
                      onServicesChange={setSelectedServices}
                    />
                  )}
                  <FileUploadButton
                    fileList={fileList}
                    setFileList={setFileList}
                  />
                </div>
              );
            } else if (stepObj.type === "profile") {
              content = (
                <CreateProfileForm
                  onBack={handlePrev}
                  onSubmit={handleProfileSubmit}
                  isLoading={isLoading}
                />
              );
            }
            return (
              <motion.div
                key={
                  stepObj.type === "base"
                    ? `step-card-${stepObj.idx}`
                    : stepObj.type
                }
                id={`step-card-${stepObj.idx}`}
                ref={(el) => (stepRefs.current[idx] = el)}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity, y: 0 }}
                exit={{ opacity: 0, y: -30 }}
                transition={{ duration: 0.35, ease: "easeInOut" }}
                className="rounded-xl shadow-sm bg-white dark:bg-slate-800 transition-all"
                style={{ pointerEvents: "auto" }}
              >
                {content}
              </motion.div>
            );
          })}
        </AnimatePresence>
        {showError && (
          <p role="alert" className="text-red-700">
            Selecteer een item.
          </p>
        )}
        <div ref={scrollRef} />
      </div>

      {revealedStepCount !== originQuestionCount + 3 && (
        <div className="flex flex-row gap-4">
          <Button variant="outline" onClick={handlePrev} disabled={isLoading}>
            Terug
          </Button>
          <Button
            onClick={handleNext}
            disabled={isLoading}
            className="text-white"
          >
            {revealedStepCount === stepList.length ? "Indienen" : "Volgende"}
          </Button>
        </div>
      )}
    </div>
  );
}
