/**
 * @description This component renders a comprehensive and SEO-optimized detail page for general mechanic/technician services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for various repair and installation tasks. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import PricingSection from "@/components/landing/PricingSection";
import {
  ArrowLeft,
  <PERSON>tings,
  Clock,
  ArrowRight,
  ThumbsUp,
  Edit3,
  MessageSquare,
  User<PERSON>heck,
  CheckCircle,
  MapPin,
  Wrench,
} from "lucide-react";

const Service_MonteurPage = () => {
  usePageTitle(
    "Monteur Nodig? | Klusgebied - Apparaat Reparaties & Installaties"
  );
  const navigate = useNavigate();

  const services = [
    {
      icon: Wrench,
      title: "Witgoed Reparatie",
      description:
        "Reparatie van wasmachines, drogers, vaatwassers en koelkasten.",
      points: [
        "Reparatie van alle merken witgoed.",
        "Eerlijk advies over reparatie vs. vervanging.",
        "Snelle service om ongemak te minimaliseren.",
        "Garantie op de uitgevoerde reparatie.",
      ],
    },
    {
      icon: Settings,
      title: "Meubelmontage",
      description: "Vakkundige montage van kasten, bedden en andere meubels.",
      points: [
        "Montage van meubels van alle merken (incl. IKEA).",
        "Snel, vakkundig en zonder stress voor u.",
        "Bespaart u tijd en frustratie.",
        "Zorgt voor een stevige en correcte montage.",
      ],
    },
    {
      icon: ThumbsUp,
      title: "Apparatuur Installatie",
      description:
        "Aansluiten van nieuwe keukenapparatuur of andere elektrische apparaten.",
      points: [
        "Veilige en correcte installatie van uw nieuwe apparatuur.",
        "Aansluiten van ovens, kookplaten, etc.",
        "We controleren de werking voordat we vertrekken.",
        "Advies over het gebruik van het apparaat.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Settings className="w-8 h-8 text-white" />,
      title: "Alle Merken",
      description:
        "Onze monteurs hebben ervaring met alle bekende merken witgoed en apparatuur.",
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Snelle Service",
      description:
        "Vaak kunnen we dezelfde dag nog een monteur sturen voor uw reparatie.",
    },
    {
      icon: <ThumbsUp className="w-8 h-8 text-white" />,
      title: "Garantie op Reparatie",
      description:
        "U krijgt standaard garantie op de uitgevoerde reparatie en onderdelen.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een monteur per uur?",
      answer:
        "Het uurtarief voor een monteur ligt gemiddeld tussen de €40 en €65, exclusief materiaalkosten. Voorrijkosten kunnen ook van toepassing zijn.",
    },
    {
      question: "Is reparatie goedkoper dan een nieuw apparaat kopen?",
      answer:
        "In veel gevallen is reparatie een duurzame en voordelige keuze. Onze monteur geeft altijd een eerlijk advies over de kosten en de levensduur van het apparaat.",
    },
    {
      question: "Repareren jullie ook oudere apparaten?",
      answer:
        "Ja, zolang onderdelen beschikbaar zijn, repareren onze monteurs ook oudere modellen. Zij kunnen u adviseren of reparatie nog zinvol is.",
    },
    {
      question: "Hoe snel kan een monteur er zijn?",
      answer:
        "Voor urgente reparaties streven we ernaar om binnen 24 uur een monteur bij u langs te sturen, afhankelijk van de planning en uw locatie.",
    },
  ];

  const reviews = [
    {
      name: "Karin de Jong",
      location: "Den Haag",
      rating: 5,
      quote:
        "Mijn wasmachine was binnen no-time gerepareerd. De monteur was vriendelijk en zeer deskundig.",
      highlighted: true,
    },
    {
      name: "Tom Willems",
      location: "Arnhem",
      rating: 5,
      quote:
        "Geen gedoe meer met die IKEA-handleidingen. De monteur heeft mijn nieuwe kast perfect in elkaar gezet.",
      highlighted: false,
    },
    {
      name: "Familie Peters",
      location: "Zwolle",
      rating: 4,
      quote:
        "De nieuwe vaatwasser is netjes geïnstalleerd en de oude is meegenomen. Goede service!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1573496130407-57329f01f769?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1517048676732-d65bc937f952?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1682531046921-4a37f93b85de?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtZWNoYW5pYyUyMHRvb2xzJTJDJTIwcmVwYWlyJTIwc2VydmljZSUyQyUyMHRlY2huaWNpYW4lMjBhdCUyMHdvcmt8ZW58MHx8fHwxNzUxNzQwNzc1fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1676630656246-3047520adfdf?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwYXBwbGlhbmNlJTIwcmVwYWlyJTJDJTIwaW5zdGFsbGF0aW9uJTIwc2VydmljZSUyQyUyMHByb2Zlc3Npb25hbCUyMHRlY2huaWNpYW58ZW58MHx8fHwxNzUxNzQwNzc1fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description: "Beschrijf uw reparatie- of montagetaak.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Monteurs reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde monteurs.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start",
      description: "Vergelijk profielen en kies de beste monteur voor uw klus.",
      microcopy: "Vergelijk profielen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Monteurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "monteur",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Monteur Nodig? | Klusgebied - Apparaat Reparaties & Installaties
        </title>
        <meta
          name="description"
          content="Vind een vakkundige en betrouwbare monteur voor elke technische klus, van witgoed reparatie tot meubelmontage. Plaats je klus gratis op Klusgebied."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-purple-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-purple-100 border border-purple-200/80 rounded-full px-4 py-2 mb-6">
                    <Settings className="w-5 h-5 text-purple-600" />
                    <span className="text-purple-800 font-semibold text-sm">
                      Monteur Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Monteur Nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-indigo-500 mt-2">
                      Reparatie & Installatie
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor reparatie, installatie en montage. Vind een vakkundige
                    en betrouwbare monteur voor elke technische klus.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-purple-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-purple-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:-translate-y-1"
                    >
                      Vind jouw monteur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele monteur aan het werk met gereedschap"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte monteur voor jouw klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-purple-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Monteur Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van een kapotte wasmachine tot het monteren van een nieuwe kast,
                onze monteurs helpen u graag.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-purple-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-purple-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <PricingSection
          serviceName="monteur"
          themeColor="purple"
          priceItems={[
            {
              label: "Uurtarief:",
              value: "€40–€65",
              unit: "per uur (excl. materiaal)",
            },
            {
              label: "Witgoed reparatie:",
              value: "Vanaf €75",
              unit: "(incl. voorrijkosten)",
            },
            { label: "Meubelmontage:", value: "Op offertebasis", unit: "" },
          ]}
        />

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een monteur via Klusgebied en wees verzekerd van een
                snelle en vakkundige oplossing.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-purple-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-purple-600 to-indigo-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Een apparaat defect of iets te monteren?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en vind snel een vakkundige monteur die het
              probleem voor u oplost.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-purple-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je montage- of reparatieklus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-purple-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-purple-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_MonteurPage;
