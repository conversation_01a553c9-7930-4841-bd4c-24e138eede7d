import { useState } from "react";
import {
  Phone,
  FileText,
  MapPin,
  Calendar,
  CheckCircle,
  Info,
  Home,
  Building,
  Hammer,
  Wrench,
  Shield,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";

import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const ConstructionLanding = () => {
  const [contactModalOpen, setContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 5,
      shortText:
        "Onze aanbouw is perfect uitgevoerd. Binnen planning, goede communicatie en super resultaat. Absolute aanrader...",
      fullText:
        "Onze aanbouw is perfect uitgevoerd. Binnen planning, goede communicatie en super resultaat. Absolute aanrader voor iedereen die een betrouwbaar bouwbedrijf zoekt. De kwaliteit van het werk was uitstekend en alles werd netjes opgeleverd.",
      verified: true,
    },
    {
      id: 2,
      name: "Khalid",
      rating: 5,
      shortText:
        "Geen omkijken gehad naar aannemers of onderaannemers. Alles liep zoals afgesproken. Top service...",
      fullText:
        "Geen omkijken gehad naar aannemers of onderaannemers. Alles liep zoals afgesproken. Top service en professionele uitvoering. Het bouwbedrijf heeft alle verwachtingen overtroffen.",
      verified: true,
    },
    {
      id: 3,
      name: "Ruben",
      rating: 5,
      shortText:
        "Volledige renovatie met strak resultaat. Goede planning en uitvoering. Absolute aanrader voor bouwprojecten...",
      fullText:
        "Volledige renovatie met strak resultaat. Goede planning en uitvoering. Absolute aanrader voor bouwprojecten. Het team was zeer professioneel en heeft alles volgens planning opgeleverd.",
      verified: true,
    },
    {
      id: 4,
      name: "Marco",
      rating: 5,
      shortText:
        "Prima bouwbedrijf. Stond stipt op tijd voor de deur en was erg professioneel. Alles rustig uitgelegd en vakkundig...",
      fullText:
        "Prima bouwbedrijf. Stond stipt op tijd voor de deur en was erg professioneel. Alles rustig uitgelegd en vakkundig uitgevoerd. Zeer tevreden met het eindresultaat en de service.",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={contactModalOpen}
        setIsOpen={setContactModalOpen}
        jobType="bouwbedrijf-inschakelen"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-xl font-bold tracking-wide">Klusgebied</h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md text-base tracking-wide"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md text-base tracking-wide"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-lg mb-2 tracking-wide leading-relaxed">
            Verbouwing, uitbouw of nieuwbouwproject gepland?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Bouwbedrijf inschakelen – Van fundering tot oplevering professioneel
            geregeld
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-lg tracking-wide leading-relaxed">
            Zoek je een betrouwbaar bouwbedrijf voor jouw verbouwing, uitbouw of
            nieuwbouwproject? Klusgebied koppelt je aan ervaren bouwspecialisten
            voor elk type project.
          </p>
          <button
            onClick={() => setContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base tracking-wide"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base tracking-wide leading-relaxed">
                Actief in Amsterdam, Almere en Utrecht
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base tracking-wide leading-relaxed">
                Vaak dezelfde dag beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Building className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base tracking-wide leading-relaxed">
                Volledige projectbegeleiding
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-rezwan-1216544.jpg"
            className="w-full h-full object-cover"
            alt="Bouwbedrijf inschakelen"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-base tracking-wide leading-relaxed">
              Onze bouwspecialisten hebben ervaring met alle bekende merken en
              materialen.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-karolina-grabowska-5240607.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-jvdm-1454804.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 tracking-wide leading-relaxed">
                Bouwproject zonder stress en gedoe?
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Een bouwproject kan overweldigend zijn. Van vergunningen tot
                coördinatie van verschillende vakspecialisten - er komt veel bij
                kijken. Zonder de juiste begeleiding loop je het risico op
                vertragingen en kostenoverschrijdingen.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Met een professioneel bouwbedrijf heb je één aanspreekpunt voor
                het gehele project. Wij regelen alles van A tot Z: van ontwerp
                en vergunningen tot de eindoplevering.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Een goed georganiseerd bouwproject zorgt voor minder stress,
                betere kwaliteit en oplevering binnen tijd en budget. Zo kun je
                genieten van het resultaat zonder zorgen.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Bouwbedrijf Services
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-base tracking-wide leading-relaxed">
            Wij lossen alle bouwuitdagingen op, van kleine verbouwingen tot
            complete nieuwbouwprojecten. Onze diensten zijn beschikbaar in de
            Randstad en daarbuiten.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Building
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl font-bold mb-4 tracking-wide leading-relaxed">
                Aanbouw en uitbouw
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Uitbreiding van woonkamer of keuken, inclusief fundering, muren,
                kozijnen en afwerking. Volledig sleutelklaar opgeleverd.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Home
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl font-bold mb-4 tracking-wide leading-relaxed">
                Complete woningrenovatie
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Van oude woning een modern thuis maken. Volledige afbouw,
                elektra, loodgieterij en meer. Alles uit één hand.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Hammer
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl font-bold mb-4 tracking-wide leading-relaxed">
                Nieuwbouw en casco-opbouw
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Vanaf fundering tot wind- en waterdicht casco, inclusief
                tekenwerk en vergunningen. Professioneel uitgevoerd.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Wrench
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl font-bold mb-4 tracking-wide leading-relaxed">
                Verbouw en transformaties
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Garage ombouwen naar kantoor, zolder tot slaapkamer of woning
                splitsen in meerdere units. Creatieve oplossingen.
              </p>
            </div>

            {/* Service Card 5 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Shield
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-xl font-bold mb-4 tracking-wide leading-relaxed">
                Projectbegeleiding
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Volledige begeleiding van begin tot eind. Eén aanspreekpunt voor
                alle disciplines en garantie op kwaliteit en planning.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-heyho-6238609.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Nieuwbouw casco woning resultaat"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Een complete nieuwbouw casco woning met fundering, muren en
                dakconstructie professioneel opgeleverd binnen de afgesproken
                tijd.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-asphotograpy-224924.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Uitbouw woonkamer resultaat"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Een moderne uitbouw van 3 meter met openslaande deuren, volledig
                sleutelklaar opgeleverd binnen 4 weken.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-olga-neptuna-1090543-2078774.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Woningrenovatie resultaat"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Complete renovatie van een jaren '30 woning met nieuwe vloeren,
                elektra en badkamer volgens moderne standaarden.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 tracking-wide leading-relaxed">
                Professionele bouwbegeleiding – Van start tot oplevering
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Wij staan voor je klaar tijdens het gehele bouwproces.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Of het nu gaat om een kleine verbouwing of groot
                nieuwbouwproject, we begeleiden je van A tot Z met één
                aanspreekpunt.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Neem contact met ons op en we kunnen vaak binnen enkele dagen
                starten met de planning. Zo heb je snel duidelijkheid over je
                project.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Onze eigen gecertificeerde bouwspecialisten werken met een
                persoonlijke en professionele aanpak. Geen lange wachttijden,
                gewoon kwaliteit en betrouwbaarheid.
              </p>
              <p className="mb-6 text-base tracking-wide leading-relaxed">
                Heb je vragen over je bouwproject? Neem contact met ons op.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-heyho-7614405.jpg"
                  className="w-[530px] h-[353px] object-cover rounded-lg"
                  alt="Bouwbegeleiding service"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-infinity-lifespaces-1420423121-30580528.jpg"
                  className="w-[530px] h-[339px] object-cover rounded-lg"
                  alt="Over Klusgebied bouwbedrijven"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je bouwproject altijd in
                goede handen is. Of het nu gaat om verbouwen, uitbreiden of
                nieuwbouw, wij verbinden je met lokaal gecertificeerde
                bouwspecialisten die het vakkundig en betrouwbaar oplossen.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Heb je een bouwproject in gedachten of wil je een afspraak
                inplannen? Wij maken het eenvoudig en zorgen dat je snel wordt
                geholpen door onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-pixabay-164572.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-infinity-lifespaces-1420423121-30580527.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base tracking-wide leading-relaxed">
                    Toegewijde en deskundige bouwspecialisten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base tracking-wide leading-relaxed">
                    Afspraak gegarandeerd binnen 3 dagen
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base tracking-wide leading-relaxed">
                    Snelle en vakkundige projectbegeleiding; elke vraag is
                    welkom
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base tracking-wide leading-relaxed">
                    Uitstekend beoordeeld door klanten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base tracking-wide leading-relaxed">
                    Mogelijkheid om snel een afspraak in te plannen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide leading-relaxed">
                  Kleine verbouwing
                </h3>
                <p className="text-base tracking-wide leading-relaxed">
                  Bouwbedrijf inschakelen
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Muren verplaatsen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Kleine aanbouw
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Projectbegeleiding
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Vergunningen regelen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Garantie op werk
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €5.000,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide leading-relaxed">
                  Woningrenovatie
                </h3>
                <p className="text-base tracking-wide leading-relaxed">
                  Bouwbedrijf inschakelen
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Badkamers renoveren
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Vloeren vervangen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Keuken installeren
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Elektra en loodgieterij
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Volledige projectcoördinatie
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €20.000,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide leading-relaxed">
                  Nieuwbouw casco
                </h3>
                <p className="text-base tracking-wide leading-relaxed">
                  Bouwbedrijf inschakelen
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Fundering aanleggen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Muren en dakconstructie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Wind- en waterdicht
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Tekenwerk en vergunningen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Volledige bouwbegeleiding
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center text-base tracking-wide leading-relaxed">
                      Afbouw mogelijk
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold tracking-wide">
                      €100.000,-
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Not Individual Teams Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 tracking-wide leading-relaxed">
              Waarom geen los vakteam, maar één bouwbedrijf?
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto mb-8"></div>
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-red-800 mb-3 tracking-wide leading-relaxed">
                  ❌ Losse vakteams
                </h3>
                <ul className="text-left space-y-2 text-red-700">
                  <li className="text-base tracking-wide leading-relaxed">
                    • Verschillende planningen coördineren
                  </li>
                  <li className="text-base tracking-wide leading-relaxed">
                    • Onduidelijke aansprakelijkheid
                  </li>
                  <li className="text-base tracking-wide leading-relaxed">
                    • Risico op miscommunicatie
                  </li>
                  <li className="text-base tracking-wide leading-relaxed">
                    • Langere doorlooptijd
                  </li>
                </ul>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-green-800 mb-3 tracking-wide leading-relaxed">
                  ✅ Eén bouwbedrijf
                </h3>
                <ul className="text-left space-y-2 text-green-700">
                  <li className="text-base tracking-wide leading-relaxed">
                    • Eén centrale planning
                  </li>
                  <li className="text-base tracking-wide leading-relaxed">
                    • Alle garanties en aansprakelijkheid geborgd
                  </li>
                  <li className="text-base tracking-wide leading-relaxed">
                    • Snellere oplevering door samenwerking
                  </li>
                  <li className="text-base tracking-wide leading-relaxed">
                    • Volledig verzekerd en volgens bouwbesluit
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Heb ik zelf ook een architect nodig?"
              answer="Niet per se. Veel bouwbedrijven werken samen met een vaste architect of tekenbureau. Wij kunnen je adviseren over de beste aanpak voor jouw project en regelen indien nodig de juiste contacten."
            />
            <FAQItem
              question="Regelen jullie ook vergunningen?"
              answer="Ja, indien nodig begeleiden wij het gehele vergunningsproces. Onze bouwbedrijven hebben ervaring met alle benodigde procedures en zorgen dat alles volgens de regels verloopt."
            />
            <FAQItem
              question="Hoe lang duurt een gemiddelde uitbouw?"
              answer="Gemiddeld 4 tot 6 weken, afhankelijk van formaat en complexiteit. Bij de offerte krijg je een duidelijke planning met realistische doorlooptijden en mijlpalen."
            />
            <FAQItem
              question="Moet ik tijdens de bouw thuis blijven?"
              answer="Niet nodig, maar wij houden je dagelijks op de hoogte via bouwrapportages of check-ins. Je bent altijd op de hoogte van de voortgang en kunt op elk moment contact opnemen."
            />
            <FAQItem
              question="Is er garantie op het werk?"
              answer="Ja. Alle bouwbedrijven werken met garantie op constructie, afwerking en oplevering. Je krijgt duidelijke garantievoorwaarden bij de offerte en volledige zekerheid op kwaliteit."
            />
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor jouw bouwproject
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 text-base tracking-wide leading-relaxed">
                Onze bouwspecialisten zijn beschikbaar en kunnen vaak binnen
                enkele dagen starten met de planning. Klik op de onderstaande
                knop om contact op te nemen.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/handyman/pexels-infinity-lifespaces-1420423121-30580530.jpg"
                  className="w-full h-full object-cover"
                  alt="Bouwspecialist"
                />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-sm italic tracking-wide leading-relaxed">
            Jeroen van der Berg – bouwspecialist in Almere, Amsterdam en
            Utrecht, geselecteerd door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl font-bold tracking-wide">Klusgebied</h3>
              </div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4 tracking-wide leading-relaxed">
                Contact
              </h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium text-base tracking-wide leading-relaxed">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="text-base tracking-wide leading-relaxed">
                    Slotermeerlaan 58
                  </p>
                </li>
                <li>
                  <p className="text-base tracking-wide leading-relaxed">
                    1064 HC Amsterdam
                  </p>
                </li>
                <li>
                  <p className="text-base tracking-wide leading-relaxed">
                    KVK: 93475101
                  </p>
                </li>
                <li>
                  <p className="text-base tracking-wide leading-relaxed">
                    <EMAIL>
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p className="tracking-wide leading-relaxed">
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1] tracking-wide">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ConstructionLanding;
