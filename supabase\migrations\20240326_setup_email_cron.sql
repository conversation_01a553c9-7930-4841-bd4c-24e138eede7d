-- Enable de benodigde extensies als ze nog niet enabled zijn
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Schedule de email functie om elke dag om 9:00 te draaien
SELECT cron.schedule(
  'send-marketing-emails-daily',
  '0 9 * * *',
  $$
  SELECT
    net.http_post(
      url:='https://bbyifnpcqefabwexvxdc.supabase.co/functions/v1/send-marketing-emails',
      headers:='{"Content-Type": "application/json", "Authorization": "Bearer YOUR_ANON_KEY"}'::jsonb
    ) as request_id;
  $$
);