import { useCallback, useState } from "react";
import { X, Upload, Trash2, Eye } from "lucide-react";
import { useDropzone } from "react-dropzone";
import { Dialog, DialogContent } from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface AdminPhotoUploadProps {
  photos: string[];
  onPhotosChange: (photos: string[]) => void;
  maxPhotos?: number;
  className?: string;
}

export function AdminPhotoUpload({
  photos,
  onPhotosChange,
  maxPhotos = 10,
  className,
}: AdminPhotoUploadProps) {
  const { toast } = useToast();

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (photos.length + acceptedFiles.length > maxPhotos) {
        toast({
          variant: "destructive",
          title: "Te veel foto's",
          description: `Maximaal ${maxPhotos} foto's toegestaan`,
        });
        return;
      }

      const newPhotos = await Promise.all(
        acceptedFiles.map((file) => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.readAsDataURL(file);
          });
        })
      );

      onPhotosChange([...photos, ...newPhotos]);
    },
    [photos, onPhotosChange, maxPhotos]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    multiple: true,
  });

  const removePhoto = (index: number) => {
    const newPhotos = photos.filter((_, i) => i !== index);
    onPhotosChange(newPhotos);
  };

  return (
    <>
      <div className={cn("space-y-4", className)}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 overflow-y-auto max-h-[40vh]">
          {photos?.map((photo, index) => (
            <div key={index} className="relative group aspect-square">
              <img
                src={photo}
                alt={`Photo ${index + 1}`}
                className="w-full h-full object-cover rounded-lg transition-all duration-200 group-hover:brightness-[0.85]"
              />
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded-lg">
                <div className="absolute inset-x-0 bottom-0 p-2 bg-gradient-to-t from-black/80 to-transparent">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="secondary"
                      size="icon"
                      className="h-8 w-8 rounded-full bg-white/50 hover:bg-white/60 transition-all"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setPreviewUrl(photo);
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="icon"
                      className="h-8 w-8 rounded-full bg-red-500/50 hover:bg-red-500/60 transition-all"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        removePhoto(index);
                      }}
                    >
                      <Trash2 className="h-4 w-4 text-black/70" />
                    </Button>
                  </div>
                </div>
                <div className="absolute top-2 left-2">
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-black/40 text-white backdrop-blur-sm">
                    {index + 1}/{photos.length}
                  </span>
                </div>
              </div>
            </div>
          ))}

          {photos.length < maxPhotos && (
            <div
              {...getRootProps()}
              className={cn(
                "border-2 border-dashed rounded-lg aspect-square flex flex-col items-center justify-center gap-2 transition-colors cursor-pointer",
                isDragActive
                  ? "border-primary bg-primary/5"
                  : "border-gray-200 hover:border-primary"
              )}
            >
              <input {...getInputProps()} />
              <Upload className="h-8 w-8 text-gray-400" />
              <p className="text-sm text-gray-500 text-center">
                {isDragActive
                  ? "Afbeeldingen hier neerzetten"
                  : "Versleep afbeeldingen of klik om ze te selecteren"}
              </p>
              <p className="text-xs text-gray-400">
                {photos.length} / {maxPhotos} foto's
              </p>
            </div>
          )}
        </div>
      </div>
      <Dialog open={!!previewUrl} onOpenChange={() => setPreviewUrl(null)}>
        <DialogContent className="max-w-4xl p-0 overflow-hidden">
          <div className="relative">
            {previewUrl && (
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-auto max-h-[80vh] object-contain"
              />
            )}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 h-8 w-8 rounded-full backdrop-blur-sm bg-black/20 hover:bg-black/40 transition-all"
              onClick={() => setPreviewUrl(null)}
            >
              <X className="h-4 w-4 text-white" />
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
