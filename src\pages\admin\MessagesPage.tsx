import { Search, ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import { AdminMessageCard } from "@/components/admin/messages/AdminMessageCard";
import { useJobs } from "@/contexts/JobsContext";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import CreateJobModal from "@/components/admin/jobs/CreateJobModal";
import { JobSkeleton } from "./JobSkeleton";

const MessagesPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredJobs, setFilteredJobs] = useState<any[]>([]);

  const navigate = useNavigate();
  const { jobs, isLoading } = useJobs();

  useEffect(() => {
    setFilteredJobs(
      jobs?.filter(
        (job) =>
          job.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.profiles?.first_name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          job.profiles?.last_name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          job.messages?.some((msg) =>
            msg.content?.toLowerCase().includes(searchQuery.toLowerCase())
          )
      )
    );
  }, [jobs, searchQuery]);

  const removeJob = (jobId: string) => {
    setFilteredJobs((filteredJobs) =>
      filteredJobs.filter((job) => job?.id !== jobId)
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-6 sm:px-0 py-6 space-y-6">
      <div className="flex sm:flex-row flex-col justify-between sm:items-center gap-2">
        <div className="flex items-center gap-4">
          <Button
            className="-ml-3"
            variant="ghost"
            size="icon"
            onClick={() => navigate("/beheerder")}
            aria-label="Return to admin page"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-xl font-semibold">Berichten</h1>
        </div>
        <CreateJobModal />
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Zoek op titel, naam of berichtinhoud..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {isLoading && !jobs?.length ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, index) => (
            <JobSkeleton key={index} />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredJobs?.map((job) => (
            <AdminMessageCard key={job.id} job={job} removeJob={removeJob} />
          ))}
        </div>
      )}
    </div>
  );
};

export default MessagesPage;
