import { useState, useEffect } from "react";

import { JobDetailInfo } from "./JobDetailInfo";
import { VakmanProfileDialog } from "./VakmanProfileDialog";
import { VakmanInfo } from "./detail/VakmanInfo";
import { supabase } from "@/integrations/supabase/client";
import { ReviewSection } from "./detail/ReviewSection";

interface JobContentProps {
  id: string;
  currentJob: any;
  currentStatus: string;
  isOwner: boolean;
  isDirect: boolean;
}

export const JobContent = ({
  id,
  currentJob,
  currentStatus,
  isOwner,
  isDirect,
}: JobContentProps) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [hasReviewed, setHasReviewed] = useState(false);

  const [acceptedVakman, setAcceptedVakman] = useState<string | null>(null);
  const [vakmanProfile, setVakmanProfile] = useState<any>(null);
  const [showVakmanProfile, setShowVakmanProfile] = useState(false);
  const [jobStatus, setJobStatus] = useState(currentStatus);

  useEffect(() => {
    if (!currentJob?.hired_craftman_id) return;

    checkReviewStatus(currentJob.hired_craftman_id);
  }, [currentJob]);

  const checkReviewStatus = async (craftmanId: string): Promise<void> => {
    try {
      setAcceptedVakman(craftmanId);

      const [profileResponse, reviewResponse] = await Promise.all([
        supabase.from("profiles").select("*").eq("id", craftmanId).single(),
        supabase
          .from("vakman_reviews")
          .select(
            `
            *,
            reviewer:reviewer_id (
              first_name,
              last_name
            )
          `
          )
          .eq("job_id", id)
          .eq("vakman_id", craftmanId)
          .maybeSingle(),
      ]);

      if (profileResponse.data) {
        setVakmanProfile(profileResponse.data);
      }
      setHasReviewed(!!reviewResponse.data);
    } catch (error) {
      console.error("Error checking review status:", error);
      throw error;
    }
  };

  const handleComplete = async (craftmanId: string): Promise<void> => {
    try {
      await Promise.all([
        checkReviewStatus(craftmanId),
        supabase.from("jobs").update({ status: "completed" }).eq("id", id),
      ]);

      setJobStatus("completed");
    } catch (error) {
      console.error("Error completing job:", error);
      setJobStatus("in_progress");
    }
  };

  return (
    <div className="space-y-6">
      <JobDetailInfo
        id={id}
        title={currentJob.title}
        description={currentJob.description}
        location={currentJob.location}
        date={currentJob.date}
        status={jobStatus}
        house_number={currentJob.house_number}
        house_number_addition={currentJob.house_number_addition}
        photos={currentJob.photos || []}
        isOwner={isOwner}
        isDirect={isDirect}
        handleComplete={handleComplete}
      />

      {jobStatus === "completed" && vakmanProfile && (
        <VakmanInfo
          vakmanProfile={vakmanProfile}
          setShowVakmanProfile={setShowVakmanProfile}
        />
      )}

      {jobStatus === "completed" && isOwner && (
        <ReviewSection
          isOwner={isOwner}
          acceptedVakman={acceptedVakman}
          hasReviewed={hasReviewed}
          showReviewForm={showReviewForm}
          setShowReviewForm={setShowReviewForm}
          jobId={id}
          checkReviewStatus={checkReviewStatus}
        />
      )}

      {acceptedVakman && (
        <VakmanProfileDialog
          isOpen={showVakmanProfile}
          onClose={() => setShowVakmanProfile(false)}
          vakmanId={acceptedVakman}
          isAccepted={true}
        />
      )}
    </div>
  );
};
