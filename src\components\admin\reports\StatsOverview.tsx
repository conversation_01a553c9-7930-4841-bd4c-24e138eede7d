import { User, Briefcase, Star } from "lucide-react";

import { Card } from "@/components/ui/card";

interface StatsOverviewProps {
  vakmanStats: any[];
}

export const StatsOverview = ({ vakmanStats }: StatsOverviewProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <User className="h-5 w-5 text-primary" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Totaal Vakmannen</p>
            <p className="text-xl font-bold">{vakmanStats?.length || 0}</p>
          </div>
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-green-100 rounded-lg">
            <Briefcase className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">
              Totaal Voltooide Klussen
            </p>
            <p className="text-xl font-bold">
              {vakmanStats?.reduce(
                (sum, vakman) => sum + vakman.completed_jobs,
                0
              ) || 0}
            </p>
          </div>
        </div>
      </Card>

      <Card className="p-4 sm:col-span-2 lg:col-span-1">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-yellow-100 rounded-lg">
            <Star className="h-5 w-5 text-yellow-600" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">
              Gemiddelde Beoordeling
            </p>
            <p className="text-xl font-bold">
              {vakmanStats?.length
                ? (
                    vakmanStats.reduce(
                      (sum, vakman) => sum + vakman.average_rating,
                      0
                    ) / vakmanStats.length
                  ).toFixed(1)
                : "0.0"}
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};
