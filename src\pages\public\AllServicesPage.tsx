/**
 * @description This component renders a comprehensive and SEO-optimized page showcasing all available services on the Klusgebied platform. It mirrors the structure and quality of the homepage, featuring a dynamic hero section, a trust bar, the full services grid, a 'How It Works' section, a testimonials slider, and a final call-to-action. The page is designed for clarity, user engagement, and conversion, with a clean layout, responsive design, and world-class animations. Key variables include the various imported components that structure the page for a stunning and cohesive user experience.
 */
import React from "react";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import TrustBar from "@/components/landing/TrustBar";
import ServicesGrid from "@/components/landing/ServicesGrid";
import HowItWorks from "@/components/landing/HowItWorks";
import TestimonialsSlider from "@/components/landing/TestimonialsSlider";
import FinalCTA from "@/components/landing/FinalCTA";
import usePageTitle from "@/hooks/usePageTitle";
import { Wrench } from "lucide-react";

const AllServicesPage = () => {
  usePageTitle(
    "Alle Diensten | Klusgebied - Vind de Juiste Vakman voor Elke Klus"
  );

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Alle Diensten | Klusgebied - Vind de Juiste Vakman voor Elke Klus
        </title>
        <meta
          name="description"
          content="Een compleet overzicht van alle diensten die Klusgebied aanbiedt. Van loodgieter tot tuinman, vind de perfecte, geverifieerde professional voor jouw klus."
        />
        <link rel="canonical" href="https://www.klusgebied.nl/#/diensten" />
      </Helmet>

      <main>
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 lg:pt-40 lg:pb-28 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1672152567751-71ea0298e821?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxidXNpbmVzcyUyQyUyMG1vZGVybnxlbnwwfHx8fDE3NTIxODU5NDR8MA&ixlib=rb-4.1.0&w=1920&h=1080&q=80&fit=crop"
              alt="Overzicht van diverse professionele diensten"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-slate-900/60"></div>
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="motion-preset-fade-in-down">
              <div className="inline-flex items-center justify-center bg-white/10 backdrop-blur-sm p-4 rounded-2xl mb-6">
                <Wrench className="h-12 w-12 text-teal-300" />
              </div>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight">
                Alle Diensten Onder Één Dak
              </h1>
              <p className="mt-6 max-w-3xl mx-auto text-lg sm:text-xl text-slate-200">
                Vind de juiste vakman voor elke klus. Bekijk ons complete aanbod
                van professionele diensten, van loodgieter tot tuinman en alles
                daartussenin.
              </p>
            </div>
          </div>
        </section>

        <TrustBar />

        {/* Services Grid Section */}
        <section id="diensten" className="py-16 lg:py-24 bg-slate-50">
          <ServicesGrid showTitle={true} showCTA={true} />
        </section>

        <HowItWorks />

        {/* Testimonials Section */}
        <TestimonialsSlider />

        {/* Final CTA Section */}
        <FinalCTA />
      </main>
      <Footer />
    </div>
  );
};

export default AllServicesPage;
