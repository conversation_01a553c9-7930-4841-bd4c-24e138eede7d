import { useState } from "react";
import { Upload, User } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface ProfilePhotoUploadProps {
  profile: any;
  onSuccess: () => void;
}

export const ProfilePhotoUpload = ({
  profile,
  onSuccess,
}: ProfilePhotoUploadProps) => {
  const [uploading, setUploading] = useState(false);
  const { toast } = useToast();

  const handlePhotoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      setUploading(true);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error("Je moet een afbeelding selecteren.");
      }

      const file = event.target.files[0];
      const fileExt = file.name.split(".").pop();
      const filePath = `${profile.id}/${Math.random()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from("profile_photos")
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const {
        data: { publicUrl },
      } = supabase.storage.from("profile_photos").getPublicUrl(filePath);

      const { error: updateError } = await supabase
        .from("profiles")
        .update({ profile_photo_url: publicUrl })
        .eq("id", profile.id);

      if (updateError) {
        throw updateError;
      }

      toast({
        title: "Profielfoto geüpload",
        description: "Je profielfoto is succesvol bijgewerkt.",
      });

      onSuccess();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fout bij uploaden",
        description: error.message,
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4 animate-fade-in">
      <Avatar className="h-32 w-32 border-4 border-white shadow-lg">
        <AvatarImage src={profile.profile_photo_url} />
        <AvatarFallback className="bg-primary">
          <User className="h-16 w-16 text-white" />
        </AvatarFallback>
      </Avatar>
      <div className="flex items-center gap-2">
        <Input
          type="file"
          accept="image/*"
          onChange={handlePhotoUpload}
          disabled={uploading}
          className="hidden"
          id="photo-upload"
        />
        <Button
          type="button"
          variant="outline"
          disabled={uploading}
          onClick={() => document.getElementById("photo-upload")?.click()}
          className="hover:bg-primary hover:text-white transition-colors"
        >
          <Upload className="h-4 w-4 mr-2" />
          {uploading ? "Uploaden..." : "Profielfoto uploaden"}
        </Button>
      </div>
    </div>
  );
};
