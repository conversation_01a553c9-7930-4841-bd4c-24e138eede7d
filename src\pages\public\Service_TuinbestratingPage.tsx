/**
 * @description This component renders a comprehensive and SEO-optimized detail page for garden paving services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout, mirroring the structure of other top-tier service pages for consistency. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for garden paving.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Wrench,
  ShieldCheck,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Grip,
  Sun,
  Award,
  Layers,
  TreePine,
} from "lucide-react";

const Service_TuinbestratingPage = () => {
  usePageTitle(
    "Tuinbestrating Nodig? | Klusgebied - Terras, Oprit & Sierbestrating"
  );
  const navigate = useNavigate();

  const tuinbestratingServices = [
    {
      icon: Grip,
      title: "Terras & Oprit Aanleggen",
      description:
        "Creëer een prachtig en functioneel terras of een duurzame oprit.",
      points: [
        "Keuze uit diverse materialen: tegels, klinkers, natuursteen.",
        "Solide ondergrond voor een langdurig strak resultaat.",
        "Inclusief afwatering en kantopsluiting.",
        "Perfect voor zowel moderne als klassieke tuinen.",
      ],
    },
    {
      icon: Layers,
      title: "Sierbestrating & Patronen",
      description:
        "Unieke patronen en decoratieve bestrating voor een sfeervolle tuin.",
      points: [
        "Creatieve legverbanden (visgraat, wildverband, etc.).",
        "Combinaties van verschillende materialen en kleuren.",
        "Aanleg van tuinpaden, cirkels en andere vormen.",
        "Advies over de beste stijl voor uw tuin.",
      ],
    },
    {
      icon: Sun,
      title: "Keramische & Natuursteen Tegels",
      description: "Luxe, duurzame en onderhoudsvriendelijke tegelsoorten.",
      points: [
        "Vakkundig leggen van grootformaat keramische tegels.",
        "Verwerking van natuursteen met oog voor detail.",
        "Krasvaste en kleurvaste oplossingen.",
        "Inclusief advies over de juiste voeg- en lijmsoorten.",
      ],
    },
    {
      icon: Wrench,
      title: "Reparatie & Onderhoud",
      description: "Herstel van verzakkingen en reiniging van uw bestrating.",
      points: [
        "Herstraten van verzakte opritten en terrassen.",
        "Vervangen van kapotte tegels of stenen.",
        "Professionele reiniging om algen en mos te verwijderen.",
        "Onkruidvrij maken en opnieuw invegen van voegen.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Sun className="w-8 h-8 text-white" />,
      title: "Verhoogt Woningwaarde",
      description:
        "Een prachtig aangelegde tuin en oprit verhogen de waarde en uitstraling van uw huis.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Duurzaam & Onderhoudsarm",
      description:
        "Wij gebruiken kwaliteitsmaterialen en zorgen voor een solide basis die jarenlang meegaat.",
    },
    {
      icon: <Award className="w-8 h-8 text-white" />,
      title: "Vakmanschap & Garantie",
      description:
        "Onze stratenmakers zijn ervaren vakmensen. U krijgt garantie op het geleverde werk.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost tuinbestrating per m²?",
      answer:
        "De kosten variëren sterk, van €50 tot €150 per m², inclusief materiaal en arbeid. De prijs is afhankelijk van de gekozen tegel of steen, de ondergrond en de complexiteit van het legverband.",
    },
    {
      question: "Welk type tegel is het beste voor mijn terras?",
      answer:
        "Keramische tegels zijn populair vanwege hun duurzaamheid en onderhoudsgemak. Betontegels zijn voordeliger en verkrijgbaar in vele stijlen. Natuursteen geeft een luxe, unieke uitstraling.",
    },
    {
      question: "Hoe voorkom ik onkruid tussen de tegels?",
      answer:
        "Een goede, waterdoorlatende voeg en een solide ondergrond zijn essentieel. Wij gebruiken speciale voegmiddelen die onkruidgroei tegengaan en adviseren over periodiek onderhoud.",
    },
    {
      question: "Kan ik bestrating op een bestaand terras leggen?",
      answer:
        "Dit is soms mogelijk, maar meestal is het beter om de oude laag te verwijderen en een nieuwe, stabiele ondergrond van zand en eventueel puin aan te brengen voor het beste en meest duurzame resultaat.",
    },
    {
      question: "Hoe lang duurt het aanleggen van een terras?",
      answer:
        "Een gemiddeld terras van 30m² kan vaak binnen 2 tot 4 dagen worden aangelegd, afhankelijk van de voorbereiding van de ondergrond en het gekozen materiaal.",
    },
    {
      question: "Krijg ik garantie op de bestrating?",
      answer:
        "Ja, op alle werkzaamheden die via Klusgebied worden uitgevoerd, krijgt u garantie. Dit dekt bijvoorbeeld verzakkingen die niet door externe factoren zijn veroorzaakt.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Boer",
      location: "Amstelveen",
      rating: 5,
      quote:
        "Onze oprit is prachtig en superstrak gelegd. De stratenmakers werkten hard en waren erg professioneel. Een enorme verbetering!",
      highlighted: false,
    },
    {
      name: "Linda Gerritsen",
      location: "Haarlem",
      rating: 5,
      quote:
        "Het keramische terras is nog mooier geworden dan we hadden durven dromen. Echt vakmanschap. Klusgebied heeft ons perfect geholpen.",
      highlighted: true,
    },
    {
      name: "Mark van den Heuvel",
      location: "Breda",
      rating: 4,
      quote:
        "Goed werk geleverd. De communicatie was duidelijk en de prijs was eerlijk. Ons tuinpad ligt er weer netjes bij.",
      highlighted: false,
    },
    {
      name: "Chantal Willems",
      location: "Apeldoorn",
      rating: 5,
      quote:
        "Sierbestrating in de achtertuin laten aanleggen. De vakman dacht creatief mee en het resultaat is uniek en sfeervol.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1626228672398-940ff5fc15dd?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYXZlZCUyMGRyaXZld2F5JTJDJTIwbGFuZHNjYXBpbmclMkMlMjBvdXRkb29yJTIwc3BhY2V8ZW58MHx8fHwxNzUxNzQyNDcwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1724607809324-ffec13685c86?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjB0ZXJyYWNlJTJDJTIwb3V0ZG9vciUyMGZ1cm5pdHVyZSUyQyUyMGdhcmRlbiUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0NzB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1684050532687-a54a3b18bfac?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxkZWNvcmF0aXZlJTIwcGF2aW5nJTJDJTIwZ2FyZGVuJTIwcGF0aCUyQyUyMHVuaXF1ZSUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0Njl8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1697803586572-aaa47de7587d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjZXJhbWljJTIwdGlsZXMlMkMlMjBvdXRkb29yJTIwZmxvb3JpbmclMkMlMjBsdXh1cnklMjBwYXRpb3xlbnwwfHx8fDE3NTE3NDI0NzB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je wensen voor de bestrating. Voeg foto's en afmetingen toe.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van ervaren stratenmakers.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste vakman voor jouw tuin. Plan en start!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Stratenmakers in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "stratenmaker",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Tuinbestrating Nodig? | Klusgebied - Terras, Oprit & Sierbestrating
        </title>
        <meta
          name="description"
          content="Vind een vakkundige stratenmaker voor uw terras, oprit of sierbestrating. Plaats uw klus gratis op Klusgebied en ontvang offertes van geverifieerde professionals."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Tuinbestrating",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.9",
              reviewCount: "189",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "50",
                maxPrice: "150",
                valueAddedTaxIncluded: true,
                unitText: "SQUARE_METER",
                description:
                  "Prijs per vierkante meter, inclusief arbeid en standaard materiaal.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-green-100 border border-green-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <TreePine className="w-5 h-5 text-green-600" />
                    <span className="text-green-800 font-semibold text-sm">
                      Tuin & Buitenwerk
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Tuinbestrating nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-teal-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Vakkundig & Stijlvol
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Laat uw terras, oprit of tuinpad vakkundig aanleggen door
                    een ervaren stratenmaker. Vind snel een geverifieerde
                    professional bij u in de buurt.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/tuin-bestrating")
                      }
                      className="group inline-flex items-center justify-center bg-green-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw stratenmaker
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 189 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1724607809324-ffec13685c86?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjB0ZXJyYWNlJTJDJTIwb3V0ZG9vciUyMGZ1cm5pdHVyZSUyQyUyMGdhcmRlbiUyMGRlc2lnbnxlbnwwfHx8fDE3NTE3NDI0NzB8MA&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Modern aangelegd terras met stijlvolle tuinmeubels"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte stratenmaker voor jouw
                klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-green-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Bestratingsdiensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Voor een duurzaam en prachtig resultaat dat perfect past bij uw
                huis en tuin.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {tuinbestratingServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-green-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-green-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost tuinbestrating via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor ervaren
                stratenmakers. Je ontvangt altijd eerst een duidelijke offerte
                voordat de klus start.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Prijs per m²:{" "}
                    <strong className="text-slate-900">€50–€150</strong> (incl.
                    arbeid & materiaal)
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Afvoeren oude bestrating:{" "}
                    <strong className="text-slate-900">€10–€20</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Voorrijkosten: Vaak inbegrepen in de offerte</span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/tuin-bestrating")}
                className="bg-green-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een stratenmaker vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-green-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-green-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-green-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-green-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-green-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Betrouwbare stratenmakers die staan voor kwaliteit en een
                prachtig eindresultaat.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-green-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(`/stad/${city.toLowerCase().replace(/\s+/g, "-")}`)
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-green-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-green-500 to-teal-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw tuin te transformeren?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en ontvang snel reacties van de beste stratenmakers
              bij u in de buurt.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/tuin-bestrating")}
              className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je bestratingsklus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/tuin-bestrating")}
          className="w-full group inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_TuinbestratingPage;
