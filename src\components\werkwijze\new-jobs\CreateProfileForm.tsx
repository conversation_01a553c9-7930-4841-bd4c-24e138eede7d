import { useCallback } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { CreateProfileFormProps } from "./types";

const profileSchema = z.object({
  email: z.string().email("Vul een geldig e-mailadres in"),
  postal_code: z.string().min(1, "Voer een postcode in"),
  house_number: z.string().min(1, "Vul een huisnummer in"),
  first_name: z.string().min(1, "Voer een voornaam in"),
  last_name: z.string().min(1, "Vul een achternaam in"),
  house_number_addition: z.string().optional(),
  city: z.string().min(1, "Vul een plaats in"),
  password: z
    .string()
    .min(6, "Wachtwoord moet minimaal 6 karakters bevatten")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z\d])[^\s]{6,}$/,
      "Wachtwoord moet bestaan uit kleine letters, hoofdletters, cijfers en symbolen"
    ),
});

export type ProfileFormData = z.infer<typeof profileSchema>;

export function CreateProfileForm({
  onBack,
  onSubmit,
  isLoading,
}: CreateProfileFormProps) {
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      email: "",
      house_number: "",
      postal_code: "",
      password: "",
    },
  });

  const handleSubmit = useCallback(
    (value: ProfileFormData) => {
      onSubmit(value);
    },
    [onSubmit]
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-10">
        <div className="flex flex-col gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>E-mail</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-3 gap-2">
            <FormField
              control={form.control}
              name="postal_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Postcode</FormLabel>
                  <FormControl>
                    <Input placeholder="1234 AB" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="house_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Huisnummer</FormLabel>
                  <FormControl>
                    <Input placeholder="12" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="house_number_addition"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-muted-foreground">
                    Toevoeging (optioneel)
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="A" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Plaats</FormLabel>
                <FormControl>
                  <Input placeholder="Amsterdam" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-2 gap-2">
            <FormField
              control={form.control}
              name="first_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Voornaam</FormLabel>
                  <FormControl>
                    <Input placeholder="John" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="last_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Achternaam</FormLabel>
                  <FormControl>
                    <Input placeholder="Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Wachtwoord</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Minimaal 8 karakters"
                    {...field}
                    type="password"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex flex-row gap-4">
          <Button
            variant="outline"
            disabled={isLoading}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onBack();
            }}
          >
            Terug
          </Button>
          <Button type="submit" disabled={isLoading}>
            Indienen
          </Button>
        </div>
      </form>
    </Form>
  );
}
