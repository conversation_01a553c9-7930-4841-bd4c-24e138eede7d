import { JobMetadata } from "./info/JobMetadata";
import { JobDetailResponse } from "./JobDetailResponse";

interface JobDetailInfoProps {
  id: string;
  title: string;
  description: string;
  location: string;
  date: string;
  status: string;
  house_number: string;
  house_number_addition?: string;
  photos: string[];
  isOwner: boolean;
  isDirect: boolean;
  handleComplete: (hired_craftman_id: string) => Promise<void>;
}

export const JobDetailInfo = ({
  id,
  title,
  description,
  location,
  date,
  status,
  house_number,
  house_number_addition,
  photos = [],
  isOwner,
  isDirect,
  handleComplete,
}: JobDetailInfoProps) => {
  return (
    <div className="flex sm:flex-row flex-col-reverse justify-between sm:gap-0 gap-6">
      <div className="space-y-8 animate-fade-in">
        <div className="space-y-4">
          <h1 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors">
            {title}
          </h1>
          <JobMetadata
            jobId={id}
            location={location}
            house_number={house_number}
            house_number_addition={house_number_addition}
            date={date}
            isOwner={isOwner}
            status={status}
            isDirect={isDirect}
          />
        </div>
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Beschrijving</h2>
          <p className="text-gray-600 whitespace-pre-wrap leading-relaxed">
            {description}
          </p>
        </div>
        {photos && photos.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-800">Foto's</h2>
            <div className="grid grid-cols-2 gap-4">
              {photos.map((photo, index) => (
                <div
                  key={index}
                  className="relative group overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                >
                  <img
                    src={photo}
                    alt={`Foto ${index + 1} van de klus`}
                    className="w-full h-48 object-cover transform transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {status !== "completed" && (
        <JobDetailResponse
          jobId={id}
          status={status}
          isOwner={isOwner}
          handleComplete={handleComplete}
        />
      )}
    </div>
  );
};
