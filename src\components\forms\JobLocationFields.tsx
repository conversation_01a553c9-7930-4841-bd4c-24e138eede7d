import { UseFormReturn } from "react-hook-form";
import { MapPin } from "lucide-react";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface LocationFieldsProps {
  form: UseFormReturn<any>;
}

export const JobLocationFields = ({ form }: LocationFieldsProps) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 pb-2 border-b">
        <div className="bg-blue-100 p-2 rounded-full">
          <MapPin className="w-5 h-5 text-primary" />
        </div>
        <h2 className="text-base sm:text-lg font-semibold text-accent">
          Locatie
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <FormField
          control={form.control}
          name="postal_code"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-accent text-sm sm:text-base">
                Postcode <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="1234 AB"
                  {...field}
                  className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus:border-primary text-foreground placeholder:text-muted-foreground/70"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-2 sm:gap-4">
          <FormField
            control={form.control}
            name="house_number"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-accent text-sm sm:text-base">
                  Huisnummer <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="123"
                    {...field}
                    className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus:border-primary text-foreground placeholder:text-muted-foreground/70"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="house_number_addition"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-accent text-sm sm:text-base">
                  Toevoeging
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="A"
                    {...field}
                    className="bg-white dark:bg-slate-800 border-gray-200 dark:border-slate-700 focus:border-primary text-foreground placeholder:text-muted-foreground/70"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};
