import { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { useAtomValue } from "jotai";

import { useJobResponse } from "./hooks/useJobResponse";
import { Button } from "@/components/ui/button";
import { JobResponses } from "./JobResponses";
import { SessionContext } from "../auth/SessionProvider";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { currentJobInfoAtom } from "@/states/job";
import { isProfileCompletedAtom } from "@/states/profile";

interface JobDetailResponseProps {
  jobId: string;
  isOwner: boolean;
  status: string;
  handleComplete: (hired_craftman_id: string) => Promise<void>;
}

export const JobDetailResponse = ({
  jobId,
  isOwner,
  status,
  handleComplete,
}: JobDetailResponseProps) => {
  const navigate = useNavigate();
  const { userProfile } = useContext(SessionContext);
  const { toast } = useToast();
  const currentJobInfo = useAtomValue(currentJobInfoAtom);
  const isProfileCompleted = useAtomValue(isProfileCompletedAtom);

  const { hasResponded, handleJobResponse } = useJobResponse(
    jobId,
    status,
    currentJobInfo,
    userProfile
  );

  const [isLoading, setIsLoading] = useState(false);

  const handleResponse = async () => {
    setIsLoading(true);

    try {
      // Check job details and user balance
      const { data: job, error: jobError } = await supabase
        .from("jobs")
        .select("response_cost, title")
        .eq("id", jobId)
        .single();

      if (jobError) throw new Error("Failed to fetch job details");

      // Validate user balance
      if (job && userProfile.balance < job.response_cost) {
        toast({
          variant: "destructive",
          title: "Onvoldoende saldo",
          description: `Je hebt niet genoeg saldo om op deze klus te reageren (€${job.response_cost.toFixed(
            2
          )} nodig). Je wordt doorgestuurd naar de saldo pagina.`,
        });
        navigate("/evenwicht");
        return;
      }

      // Validate user status
      if (userProfile?.status !== "active") {
        toast({
          title: "Kan niet reageren",
          description:
            "De status van uw profiel wordt beoordeeld. Vraag ernaar bij de beheerder.",
        });
        return;
      }

      // Submit response
      await handleJobResponse(
        "Ik heb interesse in deze klus en zou graag meer informatie willen ontvangen."
      );

      navigate("/banen");
      sendEmailForResponse();
    } catch (error) {
      console.error("Error in handleResponse:", error);
      toast({
        variant: "destructive",
        title: "Fout",
        description:
          "Er is een probleem opgetreden bij het verwerken van je reactie.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendEmailForResponse = async () => {
    try {
      if (
        !currentJobInfo?.profiles?.email ||
        !currentJobInfo?.title ||
        !userProfile?.company_name
      ) {
        throw new Error("Missing required information for email");
      }

      const emailContent = {
        to: [currentJobInfo.profiles.email],
        subject: "Nieuw antwoord ontvangen",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <p>Beste ${currentJobInfo.profiles.first_name || "klant"},</p>
            <p>Er is een nieuw antwoord ontvangen op uw klus 
              <a href="https://klusgebied.nl/banen/${currentJobInfo.id}" 
                 style="color: #007bff; text-decoration: none;">
                <strong>${currentJobInfo.title}</strong>
              </a> 
              van ${userProfile.company_name}.
            </p>
            <p>Ga naar de website om het antwoord te bekijken.</p>
            <p>Met vriendelijke groet,</p>
            <p>De Klusser</p>
          </div>
        `.trim(),
      };

      const { error } = await supabase.functions.invoke("send-email", {
        body: emailContent,
      });

      if (error) {
        throw new Error(`Failed to send email: ${error.message}`);
      }
    } catch (error) {
      console.error("Error sending response notification:", error);
      throw error;
    }
  };

  // Als je de eigenaar bent, laat dan alle reacties zien
  if (isOwner) {
    return (
      <JobResponses
        jobId={jobId}
        status={status}
        isOwner={isOwner}
        handleComplete={handleComplete}
      />
    );
  }

  // Als je een vakman bent en al hebt gereageerd, laat dan je reactie zien
  if (hasResponded) {
    return <JobResponses jobId={jobId} status={status} isOwner={false} />;
  }

  // Anders, toon de reageer knop voor vakmannen
  return (
    <div className="mt-4">
      <Button
        onClick={handleResponse}
        disabled={isLoading || !isProfileCompleted}
        className="w-full text-white"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Bezig met reageren...
          </>
        ) : (
          "Reageer op deze klus"
        )}
      </Button>
    </div>
  );
};
