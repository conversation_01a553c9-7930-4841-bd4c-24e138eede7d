import { useEffect, useState } from "react";
import { Moon, Sun } from "lucide-react";

import { Button } from "@/components/ui/button";

export const DarkModeToggle = () => {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    // Get saved preference if it exists
    const savedPreference = localStorage.getItem("darkMode");

    // Always default to light mode (false) if no preference is saved
    const shouldBeDark = savedPreference === "true" ? true : false;

    setIsDark(shouldBeDark);

    // Apply the theme
    if (shouldBeDark) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, []);

  const toggleDarkMode = () => {
    const newDarkMode = !isDark;
    setIsDark(newDarkMode);
    localStorage.setItem("darkMode", String(newDarkMode));

    if (newDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleDarkMode}
      className="h-9 w-9 hover:bg-muted transition-colors duration-200"
      title={isDark ? "Licht modus" : "Donkere modus"}
    >
      {isDark ? (
        <Sun className="h-4 w-4 text-yellow-500 transition-transform duration-200 rotate-0 hover:rotate-90" />
      ) : (
        <Moon className="h-4 w-4 text-slate-700 dark:text-slate-400 transition-transform duration-200" />
      )}
      <span className="sr-only">
        {isDark ? "Schakel licht modus in" : "Schakel donkere modus in"}
      </span>
    </Button>
  );
};
