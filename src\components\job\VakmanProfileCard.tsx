import { useEffect, useMemo, useState } from "react";
import { User, MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAtomValue } from "jotai";

import { Card } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { AcceptVakmanButton } from "./AcceptVakmanButton";
import { supabase } from "@/integrations/supabase/client";
import { VakmanProfileDialog } from "./VakmanProfileDialog";
import { Button } from "@/components/ui/button";
import { currentJobInfoAtom } from "@/states/job";

interface VakmanProfileCardProps {
  profile: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string | null;
    profile_photo_url: string | null;
    company_name: string;
  };
  jobId: string;
  showAcceptButton?: boolean;
  status?: string;
  isOwner: boolean;
  isAccepted: boolean;
  handleComplete: (hired_craftman_id: string) => Promise<void>;
}

export const VakmanProfileCard = ({
  profile,
  jobId,
  showAcceptButton = false,
  status,
  isOwner,
  isAccepted,
  handleComplete,
}: VakmanProfileCardProps) => {
  const navigate = useNavigate();
  const currentJobInfo = useAtomValue(currentJobInfoAtom);

  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [slug, setSlug] = useState<string>();
  const [isChatOpen, setIsChatOpen] = useState(false);

  const shouldShowHireButton = useMemo(() => {
    if (!currentJobInfo?.first_craftsman_accepted_at) {
      return true;
    }

    const firstAcceptedDate = new Date(
      currentJobInfo.first_craftsman_accepted_at
    );
    const now = new Date();
    const hoursDifference =
      (now.getTime() - firstAcceptedDate.getTime()) / (1000 * 60 * 60);

    return hoursDifference >= 48;
  }, [currentJobInfo?.first_craftsman_accepted_at]);

  useEffect(() => {
    fetchChatData();
  }, [profile, jobId]);

  const fetchChatData = async () => {
    try {
      const { data, error } = await supabase
        .from("chats")
        .select("slug, status")
        .eq("job_id", jobId)
        .eq("craftman_id", profile?.id)
        .single();
      if (error) throw error;

      setSlug(data?.slug);
      setIsChatOpen(data?.status === "accepted" ? true : false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleChatClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    if (!slug) return;

    navigate(`/gesprekken?chatId=${slug}`);
  };

  const handleHire = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        handleComplete(profile.id),
        supabase
          .from("jobs")
          .update({ hired_craftman_id: profile.id })
          .eq("id", jobId)
          .throwOnError(),
      ]);
    } catch (error) {
      console.error("Failed to hire craftsman:", error);
      // Consider adding error handling/notification here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Card
        className="p-4 space-y-4 cursor-pointer hover:bg-accent/5 transition-colors"
        onClick={() => setIsProfileDialogOpen(true)}
      >
        <div className="flex items-center gap-4">
          <Avatar className="h-12 w-12">
            {profile.profile_photo_url ? (
              <AvatarImage
                src={profile.profile_photo_url}
                alt={`${profile.first_name} ${profile.last_name}`}
                className="object-cover"
              />
            ) : (
              <AvatarFallback className="bg-primary">
                <User className="h-6 w-6 text-white" />
              </AvatarFallback>
            )}
          </Avatar>
          <div>
            <h3 className="font-medium">{profile.company_name}</h3>
            <p className="text-sm text-muted-foreground">
              {profile.first_name} {profile.last_name}
            </p>
          </div>
        </div>

        <div
          className="flex flex-col gap-2"
          onClick={(e) => e.stopPropagation()}
        >
          {showAcceptButton && (
            <AcceptVakmanButton jobId={jobId} vakmanId={profile.id} />
          )}
          {status !== "open" &&
            status !== "completed" &&
            isChatOpen &&
            (isOwner ? (
              <div className="flex gap-2">
                {shouldShowHireButton ? (
                  <>
                    <Button
                      className="w-full flex items-center justify-center gap-2"
                      onClick={handleHire}
                      disabled={isLoading}
                    >
                      Aangenomen
                    </Button>
                    <Button
                      onClick={handleChatClick}
                      className="h-10 w-10 flex items-center justify-center gap-2"
                      aria-label="Open Chat"
                    >
                      <MessageCircle className="h-5 w-5" />
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={handleChatClick}
                    className="flex w-full items-center justify-center gap-2"
                  >
                    Chat openen <MessageCircle className="h-5 w-5" />
                  </Button>
                )}
              </div>
            ) : (
              <Button
                onClick={handleChatClick}
                className="w-full flex items-center justify-center gap-2"
              >
                <MessageCircle className="h-5 w-5" />
                Open Chat
              </Button>
            ))}
        </div>
      </Card>

      <VakmanProfileDialog
        isOpen={isProfileDialogOpen}
        onClose={() => setIsProfileDialogOpen(false)}
        vakmanId={profile.id}
        isAccepted={isAccepted}
      />
    </>
  );
};
