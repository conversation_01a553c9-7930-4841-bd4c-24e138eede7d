import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import { supabase } from "@/integrations/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { BackToDashboard } from "./BackToDashboard";
import { ProfileAlert } from "./profile/sections/ProfileAlert";
import { ProfileContent } from "./profile/sections/ProfileContent";
import { ROUTE_PATHS } from "@/config/routes";

export const ProfileSection = () => {
	const [profile, setProfile] = useState<any>(null);
	const [loading, setLoading] = useState(true);
	const [isEditing, setIsEditing] = useState(false);
	const { toast } = useToast();
	const navigate = useNavigate();

	const isProfileComplete =
		profile &&
		profile.first_name &&
		profile.last_name &&
		profile.street_address &&
		profile.house_number;

	useEffect(() => {
		const fetchProfile = async () => {
			try {
				// First check if we have a valid session
				const { data: sessionData, error: sessionError } =
					await supabase.auth.getSession();

				if (sessionError) {
					console.error("Session error:", sessionError);
					// Handle session error specifically
					if (sessionError.message.includes("session_not_found")) {
						await handleSessionExpired();
						return;
					}
					throw sessionError;
				}

				if (!sessionData.session) {
					navigate(ROUTE_PATHS.AUTH);
					return;
				}

				// Get current user
				const {
					data: { user },
					error: userError,
				} = await supabase.auth.getUser();

				if (userError) {
					console.error("User error:", userError);
					if (userError.message.includes("session_not_found")) {
						await handleSessionExpired();
						return;
					}
					throw userError;
				}

				if (!user) {
					navigate(ROUTE_PATHS.AUTH);
					return;
				}

				const { data: profileData, error: profileError } = await supabase
					.from("profiles")
					.select("*")
					.eq("id", user.id)
					.maybeSingle();

				if (profileError) {
					console.error("Error fetching profile:", profileError);
					throw profileError;
				}

				if (!profileData) {
					const { error: createError } = await supabase
						.from("profiles")
						.insert([
							{
								id: user.id,
								email: user.email,
							},
						]);

					if (createError) {
						console.error("Error creating profile:", createError);
						throw createError;
					}

					const { data: newProfile, error: fetchError } = await supabase
						.from("profiles")
						.select("*")
						.eq("id", user.id)
						.maybeSingle();

					if (fetchError) {
						console.error("Error fetching new profile:", fetchError);
						throw fetchError;
					}

					setProfile(newProfile);
				} else {
					setProfile(profileData);
				}
			} catch (error: any) {
				console.error("Error in fetchProfile:", error);
				toast({
					variant: "destructive",
					title: "Fout bij laden profiel",
					description:
						"Er is een probleem opgetreden bij het laden van je profiel. Log opnieuw in.",
				});
				navigate(ROUTE_PATHS.AUTH);
			} finally {
				setLoading(false);
			}
		};

		const handleSessionExpired = async () => {
			await supabase.auth.signOut();
			toast({
				variant: "destructive",
				title: "Sessie verlopen",
				description: "Je sessie is verlopen. Log opnieuw in.",
			});
			navigate(ROUTE_PATHS.AUTH);
		};

		fetchProfile();
	}, [toast, navigate]);

	const handleEditSuccess = async () => {
		setIsEditing(false);
		try {
			const {
				data: { user },
				error: userError,
			} = await supabase.auth.getUser();

			if (userError) {
				console.error("User error in handleEditSuccess:", userError);
				if (userError.message.includes("session_not_found")) {
					await supabase.auth.signOut();
					toast({
						variant: "destructive",
						title: "Sessie verlopen",
						description: "Je sessie is verlopen. Log opnieuw in.",
					});
					navigate(ROUTE_PATHS.AUTH);
					return;
				}
				throw userError;
			}

			if (user) {
				const { data: profileData, error: profileError } = await supabase
					.from("profiles")
					.select("*")
					.eq("id", user.id)
					.maybeSingle();

				if (profileError) {
					console.error("Error fetching updated profile:", profileError);
					throw profileError;
				}

				if (profileData) {
					setProfile(profileData);

					if (
						profileData?.first_name &&
						profileData?.last_name &&
						profileData?.street_address &&
						profileData?.house_number
					) {
						toast({
							title: "Profiel bijgewerkt",
							description:
								"Je profielgegevens zijn succesvol bijgewerkt. Je wordt nu doorgestuurd naar het dashboard.",
						});
						navigate(ROUTE_PATHS.HOME);
					} else {
						toast({
							title: "Profiel bijgewerkt",
							description: "Je profielgegevens zijn succesvol bijgewerkt.",
						});
					}
				}
			}
		} catch (error) {
			console.error("Error in handleEditSuccess:", error);
			toast({
				variant: "destructive",
				title: "Fout bij bijwerken",
				description:
					"Er is een fout opgetreden bij het bijwerken van je profiel.",
			});
		}
	};

	const handleLogout = async () => {
		await supabase.auth.signOut();
		navigate(ROUTE_PATHS.AUTH);
	};

	if (loading) {
		return (
			<div className="p-6 max-w-4xl mx-auto">
				<BackToDashboard />
				<div className="space-y-6">
					<Skeleton className="h-[400px] w-full" />
				</div>
			</div>
		);
	}

	return (
		<div className="p-6 space-y-6 max-w-4xl mx-auto">
			<BackToDashboard />
			<ProfileAlert isProfileComplete={isProfileComplete} />
			<ProfileContent
				profile={profile}
				isEditing={isEditing}
				onEditSuccess={handleEditSuccess}
				onEditCancel={() => setIsEditing(false)}
				onLogout={handleLogout}
				onEdit={() => setIsEditing(true)}
			/>
		</div>
	);
};
