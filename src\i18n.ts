// src/i18n.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpApi from 'i18next-http-backend';

i18n
  // "HttpApi" will load translations from a server
  // (e.g., from your public/locales folder)
  .use(HttpApi)
  // "LanguageDetector" will detect user language
  .use(LanguageDetector)
  // "initReactI18next" passes i18n down to react-i18next
  .use(initReactI18next)
  .init({
    // Languages we support
    supportedLngs: ['en', 'nl'],
    
    // The default language
    fallbackLng: 'nl',
    
    // For a real app, you might want to disable debug in production
    debug: true, 

    // Options for language detection
    detection: {
      order: ['path', 'cookie', 'htmlTag', 'localStorage', 'subdomain'],
      caches: ['cookie'],
    },
    
    // Where to find the translation files
    backend: {
      loadPath: '/locales/{{lng}}/translation.json',
    },

    react: {
      // Use React's Suspense for loading translations
      useSuspense: true,
    }
  });

export default i18n;