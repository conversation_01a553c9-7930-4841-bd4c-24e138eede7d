/**
 * @description This component renders a comprehensive and SEO-optimized detail page for handyman services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for handymen. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  <PERSON>L<PERSON><PERSON>,
  <PERSON><PERSON>,
  Shield<PERSON>heck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageS<PERSON>re,
  <PERSON>r<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
  Package,
  Paintbrush2,
  Lamp,
} from "lucide-react";

const Service_KlusjesmanPage = () => {
  usePageTitle("Klusjesman Nodig? | Klusgebied - Alle Klussen Onder Één Dak");
  const navigate = useNavigate();

  const handymanServices = [
    {
      icon: Wrench,
      title: "Kleine Reparaties",
      description:
        "Van een lekkende kraan tot een klemmende deur, wij lossen het op.",
      points: [
        "Reparatie van lekkende kranen en sifons.",
        "Vastzetten van losse trapleuningen en deurklinken.",
        "Repareren van klemmende deuren en ramen.",
        "Vervangen van kapotte schakelaars en stopcontacten.",
      ],
    },
    {
      icon: Package,
      title: "Meubels Monteren",
      description: "Vakkundige montage van kasten, bedden en andere meubels.",
      points: [
        "Ervaring met meubels van IKEA, Leen Bakker, etc.",
        "Snelle en stevige montage van complexe kasten.",
        "Ophangen van planken, spiegels en schilderijen.",
        "Zorgt voor een perfect afgesteld eindresultaat.",
      ],
    },
    {
      icon: Lamp,
      title: "Lampen Ophangen",
      description: "Veilig en recht ophangen van alle soorten verlichting.",
      points: [
        "Installatie van hanglampen, plafondlampen en spots.",
        "Aansluiten op het lichtnet en monteren van dimmers.",
        "Advies over de juiste hoogte en positionering.",
        "Nette afwerking zonder zichtbare kabels.",
      ],
    },
    {
      icon: Paintbrush2,
      title: "Klein Schilder- & Kitwerk",
      description: "Bijwerken van schilderwerk en vernieuwen van kitranden.",
      points: [
        "Een muur of deur een frisse nieuwe kleur geven.",
        "Bijwerken van beschadigd schilderwerk.",
        "Vernieuwen van oude kitranden in badkamer en keuken.",
        "Zorgt voor een nette en verzorgde uitstraling.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Allround Vakman",
      description: "Onze klusjesmannen zijn van alle markten thuis.",
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Snel Ter Plaatse",
      description: "Meestal binnen 24 uur beschikbaar voor dringende klusjes.",
    },
    {
      icon: <Wrench className="w-8 h-8 text-white" />,
      title: "Geen Klus Te Klein",
      description:
        "Ook voor de kleinste reparaties en klusjes kunt u bij ons terecht.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een klusjesman per uur?",
      answer:
        "Het uurtarief van een klusjesman ligt gemiddeld tussen de €35 en €55, afhankelijk van de klus. Voor veelvoorkomende klussen wordt vaak een vaste prijs afgesproken.",
    },
    {
      question: "Welke klussen kan een klusjesman allemaal doen?",
      answer:
        "Een klusjesman is een allrounder. Denk aan kleine reparaties, meubelmontage, schilderijen ophangen, lampen installeren en klein loodgieters- of elektrawerk. Voor grote, specialistische klussen is het beter een vakspecialist in te huren.",
    },
    {
      question: "Brengen klusjesmannen hun eigen gereedschap mee?",
      answer:
        "Ja, onze klusjesmannen komen volledig uitgerust met professioneel gereedschap om de meest voorkomende klussen direct uit te voeren.",
    },
    {
      question: "Kan ik een klusjesman voor een paar uurtjes inhuren?",
      answer:
        "Jazeker. Veel klusjesmannen werken met een minimumaantal uren (vaak 2 uur), maar zijn perfect voor het bundelen en oplossen van meerdere kleine klusjes in één bezoek.",
    },
  ];

  const reviews = [
    {
      name: "Sanne de Boer",
      location: "Den Haag",
      rating: 5,
      quote:
        "Eindelijk iemand gevonden die al die kleine klusjes in huis heeft opgelost. De klusjesman was super handig, vriendelijk en snel. Heel blij mee!",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Groningen",
      rating: 5,
      quote:
        "De PAX-kast van IKEA stond binnen no-time perfect in elkaar. Zonder de hulp van Klusgebied was me dit nooit gelukt. Top service!",
      highlighted: false,
    },
    {
      name: "Familie de Leeuw",
      location: "Breda",
      rating: 5,
      quote:
        "Alle lampen hangen recht en werken perfect. De klusjesman dacht goed mee over de beste plek. Zeker een aanrader.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1585406666850-82f7532fdae3?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxoYW5keW1hbiUyQyUyMHJlcGFpciUyMHdvcmslMkMlMjB0b29sc3xlbnwwfHx8fDE3NTE3NDAzOTN8MA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1516975080664-ed2fc6a32937?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581580059884-4701fefd22cc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwaW1wcm92ZW1lbnQlMkMlMjBoYW5keW1hbiUyMHNlcnZpY2VzJTJDJTIwcHJvZmVzc2lvbmFsJTIwd29ya2VyfGVufDB8fHx8MTc1MTc0MDM5M3ww&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je klusjes. Bundel ze eventueel voor een efficiënte aanpak.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en prijsvoorstellen van handige klusjesmannen.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste klusjesman voor jouw klussen. Plan en start!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = React.useState(0);

  React.useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Klusjesmannen in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "klusjesman",
    color: "green",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Klusjesman Nodig? | Klusgebied - Alle Klussen Onder Één Dak
        </title>
        <meta
          name="description"
          content="Vind snel een betrouwbare klusjesman voor alle kleine en grote klussen in en om het huis. Plaats je klus gratis en ontvang offertes van geverifieerde vakmannen in jouw regio."
        />
        {/* ... other meta tags and structured data ... */}
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-green-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-green-100 border border-green-200/80 rounded-full px-4 py-2 mb-6">
                    <Wrench className="w-5 h-5 text-green-600" />
                    <span className="text-green-800 font-semibold text-sm">
                      Klusjesman Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Klusjesman nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-teal-500 mt-2">
                      Snel & Betrouwbaar
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor alle klussen in en om het huis. Van klein tot groot,
                    onze allround vakmannen lossen het op.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/klusjesman")}
                      className="group inline-flex items-center justify-center bg-green-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw klusjesman
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.8/5</span>
                    <span>gebaseerd op 412 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele klusjesman aan het werk"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte klusjesman voor jouw klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-green-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Klusjesman Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Geen klus te klein of te gek. Lees meer over wat onze
                klusjesmannen voor u kunnen betekenen.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {handymanServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-green-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-green-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een klusjesman via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor erkende
                vakmannen. Je ontvangt altijd eerst een prijsvoorstel voordat de
                klus start.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Uurtarief:{" "}
                    <strong className="text-slate-900">€35–€55</strong> (incl.
                    BTW)
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Minimale afname: Vaak 2 uur</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vaste prijs: Mogelijk voor duidelijk omschreven klussen
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/klusjesman")}
                className="bg-green-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een klusjesman vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-green-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-green-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-green-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-green-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-green-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Betrouwbare klusjesmannen die voor elke klus een oplossing
                hebben.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-green-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-green-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-green-500 to-teal-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Heeft u een klus?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Van klein tot groot, onze klusjesmannen staan voor u klaar.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/klusjesman")}
              className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/klusjesman")}
          className="w-full group inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_KlusjesmanPage;
