import { useEffect, useState } from "react";
import { Plus } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { AddProjectForm } from "../portfolio/AddProjectForm";
import { ProjectCard } from "../portfolio/ProjectCard";
import { Project } from "../portfolio/types";

interface ProfilePortfolioProps {
  userId: string;
}

export const ProfilePortfolio = ({ userId }: ProfilePortfolioProps) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from("portfolio_projects")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching projects:", error);
        toast({
          variant: "destructive",
          title: "Fout bij ophalen projecten",
          description:
            "Er is een fout opgetreden bij het ophalen van je portfolio projecten.",
        });
      } else {
        const projectsWithPhotos = data.map((project) => ({
          ...project,
          photos: Array.isArray(project.photos)
            ? project.photos.map((photo: any) => ({
                id: photo.id?.toString() || "",
                photo_url: photo.photo_url || "",
              }))
            : [],
        }));
        setProjects(projectsWithPhotos);
      }
    } catch (error) {
      console.error("Error in fetchProjects:", error);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [userId]);

  const handleUpdateProject = async (
    projectId: string,
    updatedData: Partial<Project>
  ) => {
    try {
      const { error } = await supabase
        .from("portfolio_projects")
        .update({
          title: updatedData.title,
          description: updatedData.description,
          budget: updatedData.budget,
          photos: updatedData.photos,
        })
        .eq("id", projectId);

      if (error) throw error;

      toast({
        title: "Project bijgewerkt",
        description: "Je project is succesvol bijgewerkt.",
      });

      fetchProjects();
    } catch (error) {
      console.error("Error updating project:", error);
      toast({
        variant: "destructive",
        title: "Fout bij bijwerken project",
        description:
          "Er is een fout opgetreden bij het bijwerken van je project.",
      });
    }
  };

  const handleDeleteProject = (projectId: string) => {
    setProjects(projects.filter((project) => project.id !== projectId));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-lg text-accent">Portfolio</h3>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Project Toevoegen
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Nieuw Project Toevoegen</DialogTitle>
            </DialogHeader>
            <AddProjectForm
              onSuccess={() => {
                setIsDialogOpen(false);
                fetchProjects();
              }}
            />
          </DialogContent>
        </Dialog>
      </div>

      {projects.length === 0 ? (
        <div className="text-center py-8 border rounded-lg bg-muted/10">
          <p className="text-muted-foreground">
            Je hebt nog geen projecten in je portfolio.
            <br />
            Voeg je eerste project toe om je werk te laten zien aan potentiële
            klanten.
          </p>
          <Button
            onClick={() => setIsDialogOpen(true)}
            variant="outline"
            className="mt-4"
          >
            <Plus className="h-4 w-4 mr-2" />
            Project Toevoegen
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {projects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onUpdate={handleUpdateProject}
              onDelete={handleDeleteProject}
            />
          ))}
        </div>
      )}
    </div>
  );
};
