/**
 * @description This component renders a dedicated section for maintenance contracts on the Klusgebied platform. It showcases four key contract types with visually appealing cards, icons, and clear calls-to-action. The component is designed to be fully responsive, engaging, and guides users to dedicated pages for each contract. Key variables include the contractsData array which holds the information for each card, and the navigate function for routing.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Heater,
  Home,
  Wrench,
  Search,
  ArrowRight,
  ShieldCheck,
} from "lucide-react";

const MaintenanceContracts = () => {
  const navigate = useNavigate();

  const contractsData = [
    {
      title: "CV Ketel Onderhoud",
      description:
        "Voorkom storingen en zorg voor een veilige, efficiënte CV-ketel met ons jaarlijkse onderhoudscontract.",
      icon: Heater,
      path: "/onderhoudscontract/cv-ketel",
      features: [
        "Jaarlijkse inspectie",
        "24/7 storingsdienst",
        "Efficiëntie-check",
      ],
      bgColor: "from-orange-50 to-amber-50",
      iconColor: "text-orange-500",
      buttonColor: "bg-orange-500 hover:bg-orange-600",
    },
    {
      title: "Dakgoot Reiniging",
      description:
        "Voorkom lekkages en waterschade door uw dakgoten periodiek professioneel te laten reinigen.",
      icon: Home,
      path: "/onderhoudscontract/dakgoot",
      features: [
        "2x per jaar reiniging",
        "Inspectie op schade",
        "Voorkom verstopping",
      ],
      bgColor: "from-blue-50 to-sky-50",
      iconColor: "text-blue-500",
      buttonColor: "bg-blue-500 hover:bg-blue-600",
    },
    {
      title: "Klusjesman Abonnement",
      description:
        "Een vast contactpersoon voor alle kleine klussen in en om het huis. Gemak en zekerheid voor een vast bedrag.",
      icon: Wrench,
      path: "/onderhoudscontract/klusjesman",
      features: [
        "Vaste klusjesman",
        "Voorrang bij planning",
        "Kleine reparaties",
      ],
      bgColor: "from-green-50 to-emerald-50",
      iconColor: "text-green-500",
      buttonColor: "bg-green-500 hover:bg-green-600",
    },
    {
      title: "Huis Inspectie",
      description:
        "Periodieke inspectie van uw woning op cruciale punten om onverwachte problemen en kosten te voorkomen.",
      icon: Search,
      path: "/onderhoudscontract/huis-inspectie",
      features: [
        "Jaarlijkse inspectie",
        "Bouwkundig rapport",
        "Advies op maat",
      ],
      bgColor: "from-purple-50 to-violet-50",
      iconColor: "text-purple-500",
      buttonColor: "bg-purple-500 hover:bg-purple-600",
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-16">
        <h2 className="text-3xl lg:text-5xl font-bold text-slate-800 mb-6">
          Zorgeloos Wonen met Onze Onderhoudscontracten
        </h2>
        <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
          Kies voor zekerheid en gemak. Met onze onderhoudscontracten bent u
          verzekerd van een perfect onderhouden woning, het hele jaar door.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {contractsData.map((contract, index) => (
          <div
            key={contract.title}
            className={`group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-slate-100 motion-preset-slide-up motion-delay-${
              index * 100
            }`}
          >
            <div
              className={`absolute top-0 left-0 w-full h-2 ${
                contract.buttonColor.split(" ")[0]
              }`}
            ></div>
            <div className="p-8 flex flex-col h-full">
              <div className="mb-6">
                <div
                  className={`w-16 h-16 rounded-xl flex items-center justify-center bg-gradient-to-br ${contract.bgColor} mb-4`}
                >
                  <contract.icon className={`w-8 h-8 ${contract.iconColor}`} />
                </div>
                <h3 className="text-2xl font-bold text-slate-800">
                  {contract.title}
                </h3>
              </div>
              <p className="text-slate-600 mb-6 flex-grow">
                {contract.description}
              </p>
              <ul className="space-y-3 mb-8 text-slate-700">
                {contract.features.map((feature) => (
                  <li key={feature} className="flex items-center">
                    <ShieldCheck className="w-5 h-5 text-teal-500 mr-3 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <button
                onClick={() => navigate(contract.path)}
                className={`mt-auto w-full inline-flex items-center justify-center text-white px-6 py-3 rounded-xl font-bold text-lg ${contract.buttonColor} transition-all duration-300 shadow-md hover:shadow-lg hover:-translate-y-0.5`}
              >
                <span>Bekijk contract</span>
                <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MaintenanceContracts;
