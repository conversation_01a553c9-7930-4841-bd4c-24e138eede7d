/**
 * @description This component renders a comprehensive and SEO-optimized detail page for general contractor services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for contractors. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  HardHat,
  ClipboardList,
  Users,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  <PERSON>r<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
} from "lucide-react";

const Service_AannemerServicePage = () => {
  usePageTitle("Aannemer Nodig? | Klusgebied - Voor Grote Verbouwingen");
  const navigate = useNavigate();

  const services = [
    {
      icon: HardHat,
      title: "Totaalverbouwing",
      description:
        "Volledige coördinatie van uw verbouwing, van fundering tot dak.",
      points: [
        "Eén aanspreekpunt voor het hele project.",
        "Begeleiding van ontwerp tot oplevering.",
        "Strakke planning en budgetbeheer.",
        "Coördinatie van alle onderaannemers.",
      ],
    },
    {
      icon: ClipboardList,
      title: "Aanbouw & Opbouw",
      description: "Realisatie van een aanbouw, serre of een extra verdieping.",
      points: [
        "Creëer meer leefruimte en waarde.",
        "Inclusief vergunningsaanvraag en constructieberekeningen.",
        "Naadloze aansluiting op uw bestaande woning.",
        "Hoogwaardige materialen en afwerking.",
      ],
    },
    {
      icon: Users,
      title: "Woningrenovatie",
      description: "Het moderniseren en renoveren van uw bestaande woning.",
      points: [
        "Vernieuwen van keukens, badkamers en andere ruimtes.",
        "Verduurzamen en energiezuinig maken van uw woning.",
        "Herindelen van ruimtes voor een betere flow.",
        "Respect voor authentieke details bij renovatie.",
      ],
    },
    {
      icon: CheckCircle,
      title: "Projectmanagement",
      description:
        "Professioneel beheer van planning, budget en onderaannemers.",
      points: [
        "Zorgt voor een soepel en efficiënt bouwproces.",
        "Voorkomt vertragingen en onverwachte kosten.",
        "Kwaliteitscontrole gedurende het hele project.",
        "Duidelijke communicatie en rapportage.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Users className="w-8 h-8 text-white" />,
      title: "Eén Aanspreekpunt",
      description:
        "De aannemer is uw centrale contactpersoon en coördineert alle werkzaamheden.",
    },
    {
      icon: <ClipboardList className="w-8 h-8 text-white" />,
      title: "Duidelijke Planning",
      description:
        "Een helder overzicht van de werkzaamheden, doorlooptijden en kosten.",
    },
    {
      icon: <HardHat className="w-8 h-8 text-white" />,
      title: "Kwaliteitsgarantie",
      description:
        "De aannemer is verantwoordelijk voor de kwaliteit van het geleverde werk.",
    },
  ];

  const faqs = [
    {
      question: "Wat doet een aannemer precies?",
      answer:
        "Een aannemer is verantwoordelijk voor de coördinatie en uitvoering van een bouwproject. Hij of zij regelt de planning, het personeel (onderaannemers), de materialen en bewaakt het budget en de kwaliteit.",
    },
    {
      question: "Wanneer heb ik een aannemer nodig?",
      answer:
        "Voor grotere projecten zoals een aanbouw, een complete renovatie of nieuwbouw is het verstandig een aannemer in te schakelen. Deze neemt u veel werk en zorgen uit handen.",
    },
    {
      question: "Hoe kies ik de juiste aannemer?",
      answer:
        "Kijk naar ervaring, referenties en keurmerken. Vraag meerdere offertes aan en zorg voor een duidelijke overeenkomst (aannemingsovereenkomst) waarin alle afspraken zijn vastgelegd.",
    },
    {
      question: "Wat is het verschil tussen een aannemer en een bouwbedrijf?",
      answer:
        "Een aannemer is vaak een persoon of klein bedrijf dat projecten leidt en onderaannemers inhuurt. Een bouwbedrijf heeft vaak zelf personeel in dienst voor verschillende disciplines (timmermannen, metselaars etc.).",
    },
  ];

  const reviews = [
    {
      name: "Familie de Boer",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Onze aanbouw is prachtig geworden. De aannemer heeft alles perfect gecoördineerd, van de fundering tot de afwerking. Een zorgeloze verbouwing!",
      highlighted: true,
    },
    {
      name: "Stichting Woonzorg",
      location: "Utrecht",
      rating: 5,
      quote:
        "Voor de renovatie van onze locatie was een strakke planning cruciaal. De aannemer heeft dit uitstekend gemanaged en binnen budget opgeleverd.",
      highlighted: false,
    },
    {
      name: "Erik & Chantal",
      location: "Haarlem",
      rating: 5,
      quote:
        "We hadden een complexe verbouwing voor ogen. De aannemer dacht creatief mee en kwam met slimme oplossingen. Het resultaat is fantastisch.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1666137270524-5131ac07314d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjb25zdHJ1Y3Rpb24lMjBzaXRlJTJDJTIwY29udHJhY3RvciUyQyUyMGJ1aWxkaW5nJTIwcGxhbnN8ZW58MHx8fHwxNzUxNzQyNDA5fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1725462294312-d10464f66c2b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxob21lJTIwcmVub3ZhdGlvbiUyQyUyMGNvbnN0cnVjdGlvbiUyMHdvcmtlciUyQyUyMHByb2plY3QlMjBtYW5hZ2VtZW50fGVufDB8fHx8MTc1MTc0MjQwOXww&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1645651964715-d200ce0939cc?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxiYWlsZGluZyUyMG1hdGVyaWFscyUyQyUyMGNvbnN0cnVjdGlvbiUyMHRvb2xzJTJDJTIwcmVub3ZhdGlvbiUyMHByb2plY3R8ZW58MHx8fHwxNzUxNzQyNDA5fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Beschrijf uw project",
      description:
        "Geef een gedetailleerde omschrijving van uw verbouw- of renovatieplannen.",
      microcopy: "Vrijblijvend en uitgebreid",
    },
    {
      icon: MessageSquare,
      title: "Ontvang voorstellen",
      description:
        "Geselecteerde aannemers nemen contact op om het project te bespreken en een offerte op te stellen.",
      microcopy: "Binnen 48 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start de bouw",
      description:
        "Vergelijk de aannemers en offertes en kies de beste partner voor uw project.",
      microcopy: "Kies op basis van expertise en vertrouwen",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Aannemers in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "aannemer",
    color: "orange",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Aannemer Nodig? | Klusgebied - Voor Grote Verbouwingen</title>
        <meta
          name="description"
          content="Vind een betrouwbare aannemer voor uw verbouwing, renovatie of aanbouw. Klusgebied verbindt u met gekwalificeerde aannemers voor een zorgeloos project."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-orange-100 border border-orange-200/80 rounded-full px-4 py-2 mb-6">
                    <HardHat className="w-5 h-5 text-orange-600" />
                    <span className="text-orange-800 font-semibold text-sm">
                      Aannemer Service
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Aannemer Nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-amber-500 mt-2">
                      Zorgeloos Verbouwen
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor een zorgeloze verbouwing, renovatie of aanbouw. Wij
                    coördineren uw project van A tot Z.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <button
                    onClick={() =>
                      navigate("/plaats-een-klus/aannemer-inschakelen")
                    }
                    className="group inline-flex items-center justify-center bg-orange-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
                  >
                    Vind een aannemer
                    <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                  </button>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Aannemer die bouwplannen bekijkt op een bouwplaats"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte aannemer voor uw project.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-orange-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Complete begeleiding voor elk bouwproject, groot of klein.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-orange-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-orange-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een aannemer?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten van een aannemer zijn een percentage van de totale
                bouwsom, meestal tussen 10% en 15%. Dit dekt de coördinatie,
                planning en het management van uw project.
              </p>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Vraag altijd een gedetailleerde offerte aan voor uw specifieke
                project.
              </div>
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/aannemer-inschakelen")
                }
                className="bg-orange-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een aannemer vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-orange-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-orange-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-orange-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-orange-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van een Aannemer
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor professionaliteit, overzicht en een eindresultaat van
                hoge kwaliteit.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-orange-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-orange-500 to-amber-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Grote verbouwplannen?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Laat u ontzorgen. Plaats uw project en ontvang offertes van
              gekwalificeerde aannemers.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/aannemer-inschakelen")}
              className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu uw project
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/aannemer-inschakelen")}
          className="w-full group inline-flex items-center justify-center bg-orange-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je project
        </button>
      </div>
    </div>
  );
};

export default Service_AannemerServicePage;
