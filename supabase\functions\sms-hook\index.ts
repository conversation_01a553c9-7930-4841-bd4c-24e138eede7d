import { Webhook } from "https://esm.sh/standardwebhooks@1.0.0";
import { corsHeaders } from "../_shared/cors.ts";

const sendTextMessage = async (
  messageBody: string,
  toNumber: string
): Promise<any> => {
  const workspaceId = Deno.env.get("MESSAGEBIRD_WORKSPACE_ID");
  const channelId = Deno.env.get("MESSAGEBIRD_CHANNEL_ID");
  const accessKey = Deno.env.get("MESSAGEBIRD_ACCESS_KEY");

  const messageBirdEndpoint = `https://api.bird.com/workspaces/${workspaceId}/channels/${channelId}/messages`;

  const result = await fetch(messageBirdEndpoint, {
    method: "POST",
    headers: {
      Authorization: `AccessKey ${accessKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      receiver: {
        contacts: [
          {
            identifierValue: toNumber,
          },
        ],
      },
      body: {
        type: "text",
        text: {
          text: messageBody,
        },
      },
    }),
  });

  const data = await result.json();

  if (!result.ok) {
    throw new Error(data.message || "Failed to send SMS");
  }

  return result;
};

const formatDutchPhoneNumber = (phoneNumber: string): string | null => {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, "");

  // Check if it's a Dutch mobile number
  if (cleaned.startsWith("31")) {
    cleaned = cleaned;
  } else if (cleaned.startsWith("0")) {
    cleaned = "31" + cleaned.substring(1);
  } else if (cleaned.length === 9) {
    cleaned = "31" + cleaned;
  }

  // Validate the final format
  const phoneRegex = /^31[6][0-9]{8}$/;
  if (!phoneRegex.test(cleaned)) {
    return null;
  }

  return "+" + cleaned;
};

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  const payload = await req.text();
  // Paste the earlier copied base64 secret here and remove the first "v1,whsec_" from the copied base64 secret
  const base64_secret = Deno.env.get("SEND_SMS_HOOK_SECRET");
  const headers = Object.fromEntries(req.headers);
  const wh = new Webhook(base64_secret);
  try {
    const { sms, user } = wh.verify(payload, headers);

    const messageBody = `Your code is ${sms.otp} for 2FA.`;
    const phoneNumber = formatDutchPhoneNumber(user.factors[0].phone);
    if (!phoneNumber) {
      return new Response(
        JSON.stringify({
          error: {
            http_code: 400,
            message: "Invalid phone number format.",
          },
        }),
        {
          status: 400,
          headers: {
            ...corsHeaders,
            "Content-Type": "application/json",
          },
        }
      );
    }

    await sendTextMessage(messageBody, phoneNumber);

    return new Response(JSON.stringify({ message: "SMS sent successfully." }), {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: {
          http_code: 500,
          message: `Failed to send sms: ${JSON.stringify(error)}`,
        },
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );
  }
});
