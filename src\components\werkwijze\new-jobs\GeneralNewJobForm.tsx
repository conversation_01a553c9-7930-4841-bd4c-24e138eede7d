import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useMemo, useState } from "react";

import { questionnaire } from "./questionnaire";
import { QuestionSelector } from "./QuestionSelector";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { generateFullDescription } from "@/lib/generate-full-description";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { BackToDashboard } from "@/components/BackToDashboard";

export const CUSTOM_QUESTION = [
  {
    id: 1,
    question: "Wat moet er precies gebeuren?",
    options: [
      "Iets laten installeren of monteren",
      "Iets laten repareren of vervangen",
      "Iets laten bouwen of uitbreiden",
      "Iets laten afwerken of netjes maken",
    ],
  },
  {
    id: 2,
    question: "In welke ruimte of plek speelt dit zich af?",
    options: [
      "Binnen in huis (woonkamer, slaapkamer, zolder, etc.)",
      "Keuken of badkamer",
      "Buitenruimte (tuin, gevel, dak, balkon)",
      "Anders / meerdere ruimtes",
    ],
  },
  {
    id: 3,
    question: "Is het nieuw werk of een reparatie?",
    options: [
      "Volledig nieuw werk",
      "Reparatie van bestaande situatie",
      "Gedeeltelijke vervanging",
      "Weet ik niet zeker",
    ],
  },
  {
    id: 4,
    question: "Gaat het om een binnen- of buitenklus?",
    options: [
      "Binnenklus",
      "Buitenklus",
      "Zowel binnen als buiten",
      "Geen idee / niet van toepassing",
    ],
  },
  {
    id: 5,
    question: "Is er al iets voorbereid of moet alles vanaf nul?",
    options: [
      "Alles moet vanaf nul gebeuren",
      "Er is al iets gedaan, het moet afgemaakt worden",
      "Alleen afwerking nodig",
      "Weet ik niet / moet vakman beoordelen",
    ],
  },
];

export function GeneralNewJobForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { userProfile } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clientProfile, setClientProfile] = useState<any>();

  useEffect(() => {
    const email = searchParams.get("email");
    if (!email || userProfile?.user_type !== "admin") return;

    fetchSpecificProfile(email);
  }, [searchParams, userProfile]);

  // Get job details based on id or custom title
  const jobDetails = useMemo(() => {
    if (id === "custom") {
      const customTitle = searchParams.get("title");
      return {
        label: customTitle || "Nieuwe klus",
        questions: CUSTOM_QUESTION,
        hasCustomQuestion: false,
        services: [],
      };
    }

    return questionnaire.find((q) => q.id === id);
  }, [id, searchParams]);

  const handleFileUpload = async (
    files: File[],
    jobId: string
  ): Promise<string[]> => {
    try {
      const uploadPromises = files.map(async (file) => {
        const fileExt = file.name.split(".").pop()?.toLowerCase() || "";
        const fileName = `${jobId}/${crypto.randomUUID()}.${fileExt}`;

        const { error: uploadError } = await supabase.storage
          .from("job-photos")
          .upload(fileName, file, {
            cacheControl: "3600",
            upsert: false,
          });

        if (uploadError) {
          throw new Error(
            `Failed to upload ${file.name}: ${uploadError.message}`
          );
        }

        const {
          data: { publicUrl },
        } = supabase.storage.from("job-photos").getPublicUrl(fileName);

        return publicUrl;
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error("File upload failed:", error);
      throw new Error("Failed to upload one or more files");
    }
  };

  const handleSubmit = useCallback(
    async ({
      answers,
      photos,
      services = [],
    }: {
      photos: File[];
      answers: string[];
      services?: string[];
    }) => {
      try {
        setIsSubmitting(true);

        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError || !session) {
          throw new Error("Je moet ingelogd zijn om een klus toe te voegen");
        }

        // Upload photos first if there are any
        let photoUrls: string[] = [];
        if (photos && photos.length > 0) {
          const tempJobId = crypto.randomUUID(); // Generate a temporary ID for the photo folder
          photoUrls = await handleFileUpload(photos, tempJobId);
        }

        const ownerProfile =
          userProfile?.user_type === "admin" ? clientProfile : userProfile;

        // Create the job with the photo URLs
        const { data: jobData, error: jobError } = await supabase
          .from("jobs")
          .insert({
            title: jobDetails.label,
            description: generateFullDescription(
              id,
              answers,
              answers[
                jobDetails?.hasCustomQuestion
                  ? jobDetails.questions.length - 1
                  : jobDetails.questions.length
              ]
            ),
            postal_code: ownerProfile.postal_code,
            house_number: ownerProfile.house_number,
            house_number_addition: ownerProfile.house_number_addition,
            user_id: ownerProfile.id,
            photos: photoUrls,
            services: services.length > 0 ? services : jobDetails.services,
          })
          .select()
          .single();

        if (jobError) {
          console.error("Error creating job:", jobError);
          throw jobError;
        }

        toast({
          title: "Klus toegevoegd",
          description: "Je klus is succesvol toegevoegd.",
        });

        navigate(`/kies-uw-vakman?id=${jobData.id}`);

        // Send notification email
        const { error: emailError } = await supabase.functions.invoke(
          "send-email",
          {
            body: {
              to: ["<EMAIL>"],
              subject: "Nieuwe klusopdracht geplaatst",
              html: `
            <div style="font-family: Arial, sans-serif;">
              <h2>Nieuwe klusopdracht</h2>
              <p><strong>Titel:</strong> ${jobDetails.label}</p>
              <p><strong>Klant:</strong> ${ownerProfile.first_name} ${ownerProfile.last_name}</p>
              <p><strong>Locatie:</strong> ${ownerProfile.postal_code}</p>
              <p><strong>Email:</strong> ${ownerProfile.email}</p>
              <p><a href="${location.origin}/admin/messages">Bekijk in admin panel</a></p>
            </div>
          `,
            },
          }
        );

        if (emailError) {
          console.error("Failed to send notification email:", emailError);
        }
      } catch (error) {
        console.error("Error creating job:", error);
        toast({
          variant: "destructive",
          title: "Er is iets misgegaan",
          description:
            "De klus kon niet worden toegevoegd. Probeer het opnieuw.",
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [id, jobDetails?.label, navigate, userProfile, clientProfile]
  );

  const handleSelectChange = useCallback(
    (data: string[], files: File[], profile?: any, services?: string[]) => {
      handleSubmit({
        photos: files,
        answers: data,
        services: services || [],
      });
    },
    [handleSubmit]
  );

  const fetchSpecificProfile = async (email: string) => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, postal_code, house_number, house_number_addition")
        .eq("email", email)
        .single();
      if (error) throw error;

      setClientProfile(data);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="relative">
      <div className="absolute sm:top-8 sm:left-[16%] top-4 left-6">
        <BackToDashboard />
      </div>
      <div className="bg-gradient-to-b from-background via-muted/30 to-background min-h-96 sm:items-center items-start justify-center flex flex-col py-10 max-w-xl sm:mx-auto mx-6">
        <p className="sm:text-4xl text-3xl py-20 font-bold">
          {jobDetails.label}
        </p>
        <QuestionSelector
          questions={jobDetails.questions}
          onValueChange={handleSelectChange}
          isLoading={isSubmitting}
          hasCustomQuestion={jobDetails?.hasCustomQuestion}
          isCustomJob={id === "custom"}
        />
      </div>
    </div>
  );
}
