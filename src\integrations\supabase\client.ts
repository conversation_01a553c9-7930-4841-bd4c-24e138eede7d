import { createClient } from "@supabase/supabase-js";

import type { Database } from "./types";

export const SUPABASE_URL = "https://bbyifnpcqefabwexvxdc.supabase.co";
const SUPABASE_ANON_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJieWlmbnBjcWVmYWJ3ZXh2eGRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzY4MjUsImV4cCI6MjA1MDU1MjgyNX0.ehAIdEzui34zPwiyvBdhWbGgPKQDHULnGiM8PInp57I";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  }
);
