/**
 * @description This component renders a comprehensive footer with multiple columns of links, company information, and social media integration. It is now fully reusable, accepting props for all content including logo, brand name, contact details, link sections, and social media URLs. The component uses react-router-dom for seamless single-page application navigation and maintains a responsive, world-class design. Key variables include props for customization and the handleLinkClick function for routing.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";

const Footer = ({
  logoSrc = "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png",
  brandName = "Klusgebied",
  description = "Het vertrouwde platform waar klanten en vakmannen elkaar vinden. Van kleine reparaties tot grote renovaties - wij brengen vraag en aanbod samen.",
  contactInfo = [
    { icon: Mail, text: "<EMAIL>" },
    { icon: Phone, text: "085 - 130 5000" },
    { icon: MapPin, text: "Amsterdam, Nederland" },
  ],
  footerSections = {
    Klusgebied: [
      { label: "Over ons", path: "/over-ons" },
      { label: "Hoe het werkt", path: "/#hoe-het-werkt" },
      { label: "Veelgestelde vragen", path: "/faq" },
      { label: "Blog", path: "/weblogs" },
      { label: "Pers & Media", path: "/pers-en-media" },
      { label: "Voor Bedrijven", path: "/bedrijven" },
    ],
    Diensten: [
      { label: "Loodgieter", path: "/diensten/loodgieter" },
      { label: "Elektricien", path: "/diensten/elektricien" },
      { label: "Schilder", path: "/diensten/schilder" },
      { label: "Timmerman", path: "/diensten/timmerman" },
      { label: "Alle diensten", path: "/diensten" },
    ],
    "Populaire Steden": [
      { label: "Amsterdam", path: "/stad/amsterdam" },
      { label: "Rotterdam", path: "/stad/rotterdam" },
      { label: "Den Haag", path: "/stad/den-haag" },
      { label: "Utrecht", path: "/stad/utrecht" },
      { label: "Alle locaties", path: "/#locaties" },
    ],
    "Voor Vakmannen": [
      { label: "Word vakman", path: "/vakman" },
      { label: "Vakman inloggen", path: "/login" },
      { label: "Hulp & Support", path: "/support" },
    ],
    "Contact & Hulp": [
      { label: "Contact", path: "/contact" },
      { label: "Klantenservice", path: "/klantenservice" },
      { label: "Garantie", path: "/garantie" },
      { label: "Geschillen", path: "/geschillen" },
      { label: "Feedback", path: "/feedback" },
    ],
  },
  socialLinks = [
    { icon: Twitter, url: "https://x.com/heybossAI", label: "Twitter" },
    {
      icon: Linkedin,
      url: "https://www.linkedin.com/company/heyboss-xyz/",
      label: "LinkedIn",
    },
    { icon: Instagram, url: "#", label: "Instagram" },
    { icon: Facebook, url: "#", label: "Facebook" },
  ],
  appDownloadLinks = {
    apple: "https://apps.apple.com/nl/app/klusgebied/id6747739000",
    google:
      "https://play.google.com/store/apps/details?id=nl.klusgebied.android",
  },
  newsletterPlaceholder = "Je e-mailadres",
  newsletterButtonText = "Aanmelden",
  copyrightText = "© 2025 Klusgebied. Alle rechten voorbehouden.",
  legalLinks = [
    {
      label: "Privacy Policy",
      href: "https://legal.heyboss.tech/67845a5e6e6bf5ecd4a3ae47/",
    },
    {
      label: "Algemene Voorwaarden",
      href: "https://legal.heyboss.tech/67845cfe76f9675292514b80/",
    },
    { label: "Cookies", href: "#" },
  ],
}) => {
  const navigate = useNavigate();

  const handleLinkClick = (path) => {
    if (!path || path === "#") return;

    if (path.startsWith("/#")) {
      navigate("/");
      setTimeout(() => {
        const elementId = path.substring(2);
        const element = document.getElementById(elementId);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }, 100);
    } else {
      navigate(path);
    }
  };

  return (
    <footer className="bg-slate-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-7 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div
              className="flex items-center mb-6 cursor-pointer"
              onClick={() => navigate("/")}
            >
              <img
                src={logoSrc}
                alt={brandName}
                className="w-10 h-10"
                loading="lazy"
                width="40"
                height="40"
              />
              <span className="ml-3 text-xl md:text-2xl font-bold">
                {brandName}
              </span>
            </div>
            <p className="text-slate-300 mb-6 leading-relaxed text-base">
              {description}
            </p>

            {/* Contact Info */}
            <div className="space-y-4">
              {contactInfo.map((item, index) => {
                const Icon = item.icon;
                return (
                  <div
                    key={index}
                    className="flex items-center text-slate-300 text-base"
                  >
                    <Icon className="w-5 h-5 mr-3 text-teal-400" />
                    <span>{item.text}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerSections).map(([sectionTitle, links]) => (
            <div key={sectionTitle}>
              <h3 className="font-bold text-base md:text-lg mb-5 text-white">
                {sectionTitle}
              </h3>
              <ul className="space-y-4">
                {links.map((link) => (
                  <li key={link.label}>
                    <button
                      onClick={() => handleLinkClick(link.path)}
                      className="text-left text-base text-slate-300 hover:text-teal-400 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:text-slate-300"
                      disabled={!link.path || link.path === "#"}
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Social Media, App Downloads & Newsletter */}
        <div className="border-t border-slate-800 pt-10 mt-10 md:mt-16">
          <div className="flex flex-col lg:flex-row justify-between items-center lg:items-start gap-10">
            {/* Social Media */}
            <div className="text-center lg:text-left">
              <h3 className="font-bold text-base md:text-lg mb-4">Volg ons</h3>
              <div className="flex space-x-4 justify-center lg:justify-start">
                {socialLinks.map((social) => {
                  const Icon = social.icon;
                  return (
                    <a
                      key={social.label}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-slate-800 p-3 rounded-full hover:bg-teal-500 transition-all duration-200 hover:scale-110"
                      aria-label={social.label}
                    >
                      <Icon className="w-5 h-5" />
                    </a>
                  );
                })}
              </div>
            </div>

            {/* App Downloads */}
            <div className="text-center lg:text-left">
              <h3 className="font-bold text-base md:text-lg mb-4">
                Download de App
              </h3>
              <div className="flex items-center flex-col sm:flex-row gap-4 justify-center">
                <a
                  href={appDownloadLinks.apple}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block hover:-translate-y-1 transition-transform duration-300"
                >
                  <img
                    src="/download-on-the-app-store.svg"
                    alt="Download on the App Store"
                    className="h-12 w-auto"
                    loading="lazy"
                    width={144}
                    height={56}
                  />
                </a>
                <a
                  href={appDownloadLinks.google}
                  className={`inline-block ${
                    appDownloadLinks.google === "#"
                      ? "cursor-not-allowed opacity-70"
                      : "hover:-translate-y-1 transition-transform duration-300"
                  }`}
                >
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                    alt="Get it on Google Play"
                    className="h-12 w-auto"
                    loading="lazy"
                    width={144}
                    height={56}
                  />
                </a>
              </div>
            </div>

            {/* Newsletter Signup */}
            <div className="w-full lg:w-auto text-center lg:text-left">
              <h3 className="font-bold text-base md:text-lg mb-4">
                Blijf op de hoogte
              </h3>
              <div className="flex max-w-md mx-auto lg:mx-0">
                <input
                  type="email"
                  placeholder={newsletterPlaceholder}
                  className="flex-1 w-full px-4 py-2 sm:py-3 bg-slate-800 border border-slate-600 rounded-l-lg focus:outline-none focus:border-teal-500 text-white placeholder-slate-400"
                />
                <button className="bg-teal-500 px-6 py-2 sm:py-3 rounded-r-lg font-semibold hover:bg-teal-600 transition-colors duration-200">
                  {newsletterButtonText}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-slate-800 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center text-base text-slate-300">
            <div className="mb-4 md:mb-0">{copyrightText}</div>
            <div className="flex space-x-6">
              {legalLinks.map((link) => (
                <a
                  key={link.label}
                  href={link.href}
                  target={link.href.startsWith("http") ? "_blank" : "_self"}
                  rel="noopener noreferrer"
                  className="hover:text-teal-400 transition-colors duration-200"
                >
                  {link.label}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
