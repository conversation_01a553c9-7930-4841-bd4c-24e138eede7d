/**
 * @description This component is responsible for injecting JSON-LD structured data into the head of the document for SEO purposes. It includes schemas for Organization, WebSite, LocalBusiness, Service, and FAQPage to help search engines understand the content of the Klusgebied platform. This component renders nothing to the DOM itself but uses react-helmet-async to manage the head content. Key variables include the structured data objects for each schema type.
 */
import React from 'react';
import { Helmet } from 'react-helmet-async';

const StructuredData = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Klusgebied.nl",
    "url": "https://klusgebied.nl",
    "logo": "https://klusgebied.nl/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+31-85-130-5000",
      "contactType": "Customer Service",
      "email": "<EMAIL>",
      "areaServed": "NL"
    },
    "sameAs": [
      "https://x.com/heybossAI",
      "https://www.linkedin.com/company/heyboss-xyz/"
    ]
  };

  const webSiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://klusgebied.nl",
    "name": "Klusgebied",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://klusgebied.nl/zoekresultaat?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Klusgebied",
    "image": "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png",
    "url": "https://www.klusgebied.nl/",
    "telephone": "+31-85-130-5000",
    "priceRange": "€€",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Keizersgracht 123",
      "addressLocality": "Amsterdam",
      "postalCode": "1015 CJ",
      "addressCountry": "NL"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "15000"
    }
  };

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "serviceType": "Platform voor vakmannen",
    "provider": {
      "@type": "LocalBusiness",
      "name": "Klusgebied"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Netherlands"
    },
    "description": "Vind snel en eenvoudig een geverifieerde vakman in jouw regio voor elke klus. Loodgieter, elektricien, schilder en meer. Plaats je klus gratis op Klusgebied.nl."
  };

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Hoe vind ik de juiste vakman voor mijn klus?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "U kunt eenvoudig de zoekbalk op onze homepage gebruiken of door onze dienstencategorieën bladeren. Plaats uw klus gratis en u ontvangt binnen 24 uur reacties van gekwalificeerde vakmannen."
        }
      },
      {
        "@type": "Question",
        "name": "Zijn de vakmannen op Klusgebied geverifieerd?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Ja, alle professionals op ons platform worden gescreend op basis van hun KVK-inschrijving, certificeringen en ervaring."
        }
      }
    ]
  };

  return (
    <Helmet>
      <script type="application/ld+json">{JSON.stringify(organizationSchema)}</script>
      <script type="application/ld+json">{JSON.stringify(webSiteSchema)}</script>
      <script type="application/ld+json">{JSON.stringify(localBusinessSchema)}</script>
      <script type="application/ld+json">{JSON.stringify(serviceSchema)}</script>
      <script type="application/ld+json">{JSON.stringify(faqSchema)}</script>
    </Helmet>
  );
};

export default StructuredData;
