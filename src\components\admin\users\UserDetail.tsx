import { useEffect, useState } from "react";
import { PencilIcon, StarIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import DiplomaManagement, { Diploma } from "./DiplomaManagement";

export interface UserInfo {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  user_type: string;
  phone_number: string;
  company_name?: string;
  kvk_number?: string;
  btw_number?: string;
  created_at: string;
  updated_at: string;
  balance: number;
  profile_photo_url?: string;
  services?: string[];
  diplomas?: Diploma[];
  [key: string]: any; // Allow for additional properties
}

interface PortfolioProject {
  id: string;
  title: string;
  description: string;
  photos: {
    id: string;
    photo_url: string;
  }[];
  created_at: string;
  user_id: string;
}

interface VakmanReview {
  id: string;
  rating: number;
  comment: string;
  created_at: string;
  vakman_id: string;
  reviewer: {
    first_name: string;
    last_name: string;
  };
  job: {
    title: string;
  };
}

interface UserDetailProps {
  userInfo: UserInfo;
}

const UserDetail = ({ userInfo }: UserDetailProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedInfo, setEditedInfo] = useState<UserInfo>(userInfo);
  const [isLoading, setIsLoading] = useState(false);
  const [portfolio, setPortfolio] = useState<PortfolioProject[]>([]);
  const [reviews, setReviews] = useState<VakmanReview[]>([]);
  const [averageRating, setAverageRating] = useState(0);
  const [isLoadingExtra, setIsLoadingExtra] = useState(false);

  // Function to refresh user data after diploma updates
  const refreshUserData = async () => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userInfo.id)
        .single();

      if (error) throw error;
      if (data) {
        setEditedInfo(data as unknown as UserInfo);
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
    }
  };

  useEffect(() => {
    const fetchExtraData = async () => {
      if (userInfo.user_type !== "vakman") return;

      setIsLoadingExtra(true);
      try {
        // Parallel fetch for better performance
        const [portfolioResult, reviewsResult] = await Promise.all([
          fetchPortfolioData(userInfo.id),
          fetchReviewsData(userInfo.id),
        ]);

        handlePortfolioData(portfolioResult);
        handleReviewsData(reviewsResult);
      } catch (error) {
        console.error("Error fetching extra data:", error);
      } finally {
        setIsLoadingExtra(false);
      }
    };

    const fetchPortfolioData = async (userId: string) => {
      const { data, error } = await supabase
        .from("portfolio_projects")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data;
    };

    const fetchReviewsData = async (userId: string) => {
      const { data, error } = await supabase
        .from("vakman_reviews")
        .select(
          `
          *,
          reviewer:profiles!vakman_reviews_reviewer_id_fkey (
            first_name,
            last_name
          ),
          job:jobs (
            title
          )
        `
        )
        .eq("vakman_id", userId);

      if (error) throw error;
      return data;
    };

    const handlePortfolioData = (portfolioData: any[]) => {
      const processedProjects = portfolioData.map((project) => ({
        ...project,
        photos: Array.isArray(project.photos)
          ? project.photos.map((photo) => ({
              id: String(photo.id || ""),
              photo_url: photo.photo_url || "",
            }))
          : [],
      }));
      setPortfolio(processedProjects);
    };

    const handleReviewsData = (reviewsData: any[]) => {
      if (!reviewsData?.length) return;

      setReviews(reviewsData);
      const averageRating =
        reviewsData.reduce((acc, review) => acc + review.rating, 0) /
        reviewsData.length;
      setAverageRating(averageRating || 0);
    };

    fetchExtraData();
  }, [userInfo.id, userInfo.user_type]);

  const handleChange = (
    field: keyof UserInfo,
    value: string | string[] | number
  ) => {
    setEditedInfo((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update({ ...editedInfo } as any)
        .eq("id", editedInfo?.id);
      if (error) throw error;

      setIsEditing(false);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const InfoItem = ({ label, value }: { label: string; value?: string }) => (
    <div className="text-sm">
      <p className="text-muted-foreground text-xs">{label}</p>
      <p className="font-medium truncate">{value || "Niet opgegeven"}</p>
    </div>
  );

  const Stat = ({ label, value }: { label: string; value: string }) => (
    <div className="bg-muted/50 rounded p-2">
      <p className="text-xs text-muted-foreground">{label}</p>
      <p className="text-sm font-semibold">{value}</p>
    </div>
  );

  const ProjectCard = ({ project }: { project: PortfolioProject }) => (
    <div className="bg-card rounded-lg border overflow-hidden">
      <div className="p-3">
        <div className="flex justify-between items-start mb-2">
          <div>
            <h4 className="font-medium">{project.title}</h4>
            <p className="text-sm text-muted-foreground line-clamp-2">
              {project.description}
            </p>
          </div>
          <p className="text-xs text-muted-foreground">
            {new Date(project.created_at).toLocaleDateString()}
          </p>
        </div>
        {project.photos.length > 0 && (
          <div className="grid grid-cols-3 sm:grid-cols-4 gap-1 mt-2">
            {project.photos.map((photo) => (
              <div
                key={photo.id}
                className="aspect-square relative overflow-hidden rounded"
              >
                <img
                  src={photo.photo_url}
                  alt={`Foto van ${project.title}`}
                  className="object-cover w-full h-full hover:scale-105 transition-transform"
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const ReviewCard = ({ review }: { review: VakmanReview }) => (
    <div className="border rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div>
          <p className="font-medium">
            {review.reviewer.first_name} {review.reviewer.last_name}
          </p>
          {review.job && (
            <p className="text-sm text-muted-foreground">{review.job.title}</p>
          )}
        </div>
        <div className="flex items-center gap-1">
          <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
          <span>{review.rating}</span>
        </div>
      </div>
      <p className="text-sm">{review.comment}</p>
      <p className="text-xs text-muted-foreground mt-2">
        {new Date(review.created_at).toLocaleDateString()}
      </p>
    </div>
  );

  // Update the display view
  if (!isEditing) {
    return (
      <div className="container max-w-4xl mx-auto px-4 py-6 space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold tracking-tight">
            Gebruiker Details
          </h2>
          <Button onClick={() => setIsEditing(true)} size="sm">
            <PencilIcon className="h-4 w-4 mr-2 sm:block hidden" />
            Bewerken
          </Button>
        </div>

        {/* User Card */}
        <div className="bg-card rounded-lg border shadow-sm p-4">
          <div className="flex flex-row items-start gap-4">
            {/* Profile Photo - Made smaller */}
            <div className="relative shrink-0">
              {editedInfo.profile_photo_url ? (
                <img
                  src={editedInfo.profile_photo_url}
                  alt={`${editedInfo.first_name} ${editedInfo.last_name}`}
                  className="w-24 h-24 object-cover rounded-full border-2 border-border"
                />
              ) : (
                <div className="w-24 h-24 bg-muted flex items-center justify-center rounded-full border-2 border-border">
                  <span className="text-2xl font-medium text-muted-foreground">
                    {editedInfo.first_name?.[0]}
                    {editedInfo.last_name?.[0]}
                  </span>
                </div>
              )}
            </div>

            {/* User Info - Better spacing */}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold truncate">
                {editedInfo.first_name} {editedInfo.last_name}
              </h3>
              <p className="text-sm text-muted-foreground truncate">
                {editedInfo.email}
              </p>
              {editedInfo.user_type === "vakman" && (
                <div className="mt-1 flex flex-wrap gap-1">
                  <Badge variant="secondary" className="text-xs">
                    {editedInfo.user_type}
                  </Badge>
                  {editedInfo.company_name && (
                    <Badge variant="outline" className="text-xs">
                      {editedInfo.company_name}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Info Sections Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Personal Info */}
          <Card className="md:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Persoonlijke Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <InfoItem label="Telefoon" value={editedInfo.phone_number} />
              <InfoItem label="Type" value={editedInfo.user_type} />
              <InfoItem
                label="Lid sinds"
                value={new Date(editedInfo.created_at).toLocaleDateString()}
              />
            </CardContent>
          </Card>

          {/* Business Info - Only for Vakman */}
          {editedInfo.user_type === "vakman" && (
            <Card className="md:col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Zakelijke Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <InfoItem
                  label="Bedrijfsnaam"
                  value={editedInfo.company_name}
                />
                <InfoItem label="KvK nummer" value={editedInfo.kvk_number} />
                <InfoItem label="BTW nummer" value={editedInfo.btw_number} />
              </CardContent>
            </Card>
          )}

          {/* Account Stats */}
          <Card className="md:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Statistieken</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                <Stat
                  label="Saldo"
                  value={`€${editedInfo.balance?.toFixed(2) || "0.00"}`}
                />
                {editedInfo.user_type === "vakman" && (
                  <>
                    <Stat
                      label="Portfolio"
                      value={portfolio.length.toString()}
                    />
                    <Stat label="Reviews" value={reviews.length.toString()} />
                    <Stat
                      label="Rating"
                      value={`${averageRating.toFixed(1)}/5`}
                    />
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Services Section */}
        {editedInfo.user_type === "vakman" && editedInfo.services && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Diensten</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-1">
                {editedInfo.services.map((service, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs py-0.5 px-2"
                  >
                    {service}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Portfolio Section */}
        {editedInfo.user_type === "vakman" && (
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Portfolio</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="p-2">
              {isLoadingExtra ? (
                <div className="space-y-2">
                  {[1, 2].map((i) => (
                    <div key={i} className="space-y-2 animate-pulse">
                      <div className="h-4 bg-muted rounded w-1/4" />
                      <div className="grid grid-cols-4 gap-1">
                        {[1, 2, 3, 4].map((j) => (
                          <div
                            key={j}
                            className="aspect-square bg-muted rounded"
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {portfolio.map((project) => (
                    <ProjectCard key={project.id} project={project} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Reviews Section */}
        {editedInfo.user_type === "vakman" && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Beoordelingen</CardTitle>
                {!isLoadingExtra && averageRating > 0 && (
                  <div className="flex items-center gap-1">
                    <StarIcon className="h-5 w-5 text-yellow-400 fill-current" />
                    <span className="font-medium">
                      {averageRating.toFixed(1)}
                    </span>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingExtra ? (
                <div className="space-y-4">
                  {[1, 2].map((i) => (
                    <div key={i} className="border rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <div className="space-y-2">
                          <div className="h-5 bg-muted rounded animate-pulse w-32" />
                          <div className="h-4 bg-muted rounded animate-pulse w-24" />
                        </div>
                        <div className="h-6 bg-muted rounded animate-pulse w-12" />
                      </div>
                      <div className="h-4 bg-muted rounded animate-pulse w-full" />
                      <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
                      <div className="h-3 bg-muted rounded animate-pulse w-24 mt-4" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <ReviewCard key={review.id} review={review} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Diplomas & Certifications Section */}
        {editedInfo.user_type === "vakman" && (
          <DiplomaManagement
            userInfo={editedInfo}
            onDiplomasUpdate={refreshUserData}
          />
        )}
      </div>
    );
  }

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        handleSubmit();
      }}
      className="container max-w-4xl mx-auto px-4 py-6 space-y-6 overflow-y-auto"
    >
      {/* Header with Actions */}
      <div className="flex sm:flex-row flex-col justify-between sm:items-center gap-4">
        <h2 className="text-xl font-bold tracking-tight">Bewerk Gebruiker</h2>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsEditing(false)}
            disabled={isLoading}
          >
            Annuleren
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 border-2 border-white/50 border-t-white animate-spin rounded-full" />
                Opslaan...
              </div>
            ) : (
              "Opslaan"
            )}
          </Button>
        </div>
      </div>

      {/* User Card with Photo */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-6 items-center sm:items-start">
            {/* Profile Photo */}
            <div className="relative group">
              {editedInfo.profile_photo_url ? (
                <img
                  src={editedInfo.profile_photo_url}
                  alt={`${editedInfo.first_name} ${editedInfo.last_name}`}
                  className="w-32 h-32 object-cover rounded-full border-2 border-border"
                />
              ) : (
                <div className="w-32 h-32 bg-muted flex items-center justify-center rounded-full border-2 border-border">
                  <span className="text-4xl font-medium text-muted-foreground">
                    {editedInfo.first_name?.[0]}
                    {editedInfo.last_name?.[0]}
                  </span>
                </div>
              )}
            </div>

            {/* Basic Info */}
            <div className="flex-1 space-y-4 w-full">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Input
                  label="Voornaam"
                  value={editedInfo.first_name}
                  onChange={(e) => handleChange("first_name", e.target.value)}
                  required
                />
                <Input
                  label="Achternaam"
                  value={editedInfo.last_name}
                  onChange={(e) => handleChange("last_name", e.target.value)}
                  required
                />
              </div>
              <Input
                label="Email"
                type="email"
                value={editedInfo.email}
                onChange={(e) => handleChange("email", e.target.value)}
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Info Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Personal Info */}
        <Card>
          <CardHeader>
            <CardTitle>Persoonlijke Informatie</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              label="Telefoon"
              value={editedInfo.phone_number}
              onChange={(e) => handleChange("phone_number", e.target.value)}
            />
            <Input label="Type" value={editedInfo.user_type} disabled />
          </CardContent>
        </Card>

        {/* Business Info - Only for Vakman */}
        {editedInfo.user_type === "vakman" && (
          <Card>
            <CardHeader>
              <CardTitle>Zakelijke Informatie</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                label="Bedrijfsnaam"
                value={editedInfo.company_name}
                onChange={(e) => handleChange("company_name", e.target.value)}
              />
              <Input
                label="KvK nummer"
                value={editedInfo.kvk_number}
                onChange={(e) => handleChange("kvk_number", e.target.value)}
              />
              <Input
                label="BTW nummer"
                value={editedInfo.btw_number}
                onChange={(e) => handleChange("btw_number", e.target.value)}
              />
            </CardContent>
          </Card>
        )}

        {/* Account Information */}
        <Card
          className={editedInfo.user_type === "vakman" ? "md:col-span-2" : ""}
        >
          <CardHeader>
            <CardTitle>Account Informatie</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">
                  Lid sinds
                </label>
                <Input
                  type="date"
                  value={editedInfo.created_at.split("T")[0]}
                  disabled
                />
              </div>
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">
                  Laatste activiteit
                </label>
                <Input
                  type="date"
                  value={editedInfo.updated_at.split("T")[0]}
                  disabled
                />
              </div>
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">
                  Saldo (€)
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={editedInfo.balance}
                  onChange={(e) =>
                    handleChange("balance", parseFloat(e.target.value))
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </form>
  );
};

export default UserDetail;
