export const StepsSection = () => {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24 max-w-4xl">
      <h2 className="text-2xl md:text-3xl font-display font-semibold text-center mb-12">
        Zo werkt het: in 4 eenvoudige stappen
      </h2>
      <div className="grid gap-8 md:gap-12 relative">
        {/* Connecting Line */}
        <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary via-primary to-transparent -translate-x-1/2" />

        {steps.map((step, index) => (
          <div
            key={step.title}
            className={`relative bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-8 lg:w-[85%] animate-fade-in ${
              index % 2 === 1 ? "lg:ml-auto" : ""
            }`}
            style={{ animationDelay: `${0.4 + index * 0.1}s` }}
          >
            <div className="flex lg:hidden items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-bold shrink-0">
                {index + 1}
              </div>
              <h3 className="text-xl md:text-2xl font-display font-semibold text-gray-900">
                {step.title}
              </h3>
            </div>
            <div className="absolute -left-5 top-8 w-10 h-10 bg-primary rounded-full hidden lg:flex items-center justify-center text-white font-bold">
              {index + 1}
            </div>
            <h3 className="hidden lg:block text-2xl font-display font-semibold mb-4 text-gray-900">
              {step.title}
            </h3>
            <p className="text-gray-600 leading-relaxed text-lg">
              {step.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

const steps = [
  {
    title: "Beschrijf je klus",
    description:
      "Vertel ons wat je nodig hebt. Hoe specifieker je bent, hoe beter vakmannen kunnen inschatten of ze je kunnen helpen. Voeg gerust foto's toe om de situatie duidelijk te maken.",
  },
  {
    title: "Ontvang reacties",
    description:
      "Binnen korte tijd ontvang je reacties van gekwalificeerde vakmannen uit jouw regio. Bekijk hun profielen, ervaring en beoordelingen om een goede keuze te maken.",
  },
  {
    title: "Kies je vakman",
    description:
      "Vergelijk de reacties en kies de vakman die het beste bij jouw klus past. Bespreek de details via ons berichtensysteem en maak duidelijke afspraken over de uitvoering.",
  },
  {
    title: "Klus geklaard",
    description:
      "Na afronding van de klus kun je een beoordeling achterlaten. Zo help je andere klanten bij het vinden van de juiste vakman en draag je bij aan onze betrouwbare community.",
  },
];
