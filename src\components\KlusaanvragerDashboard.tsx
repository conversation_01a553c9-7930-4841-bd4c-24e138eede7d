import { useNavigate } from "react-router-dom";
import { Plus, Briefcase, User, MessageSquare, Brush } from "lucide-react";

import { Card } from "./ui/card";
import { useUnreadMessages } from "@/hooks/useUnreadMessages";
import { Badge } from "./ui/badge";
import { DashboardAlert } from "./dashboard/DashboardAlert";
import { useAuth } from "./auth/hooks/useAuth";

interface KlusaanvragerDashboardProps {
  setCurrentView: (view: string) => void;
}

export const KlusaanvragerDashboard = ({
  setCurrentView,
}: KlusaanvragerDashboardProps) => {
  const { userProfile } = useAuth();
  const navigate = useNavigate();

  const {
    unreadMessageCount,
    unreadResponseCount,
    resetUnreadMessageCount,
    resetUnreadResponseCount,
  } = useUnreadMessages(userProfile.id || "");

  const handleCardClick = async (section: string) => {
    switch (section) {
      case "nieuwe-klus":
        setCurrentView("new-job");
        navigate("/banen/new");
        break;
      case "mijn-klussen":
        setCurrentView("my-jobs");
        navigate("/banen");
        resetUnreadResponseCount();
        break;
      case "profiel":
        setCurrentView("profile");
        navigate("/profiel");
        break;
      case "vakmannen":
        setCurrentView("vakmannen");
        navigate("/kies-uw-vakman");
        break;
    
      case "chats":
        setCurrentView("chats");
        navigate("/gesprekken");
        resetUnreadMessageCount();
        break;
      default:
        break;
    }
  };

  const dashboardItems = [
    {
      title: "Nieuwe Klus",
      description: "Plaats een nieuwe klusopdracht en vind de perfecte vakman",
      onClick: () => handleCardClick("nieuwe-klus"),
      isPrimary: true,
      icon: <Plus className="h-5 w-5 text-white" />,
      gradient: "from-primary to-primary-hover",
    },
    
      {
      title: "Mijn onderhoud",
      description: "Al je onderhoudscontracten overzichtelijk op één plek.",
      onClick: () => handleCardClick("onderhoud"),
      isPrimary: true,
      icon: < Brush className="h-5 w-5 text-white" />,
      gradient: "from-primary to-primary-hover",
    },
    {
      title: "Mijn Klussen",
      description: "Bekijk en beheer je geplaatste klussen en reacties",
      onClick: () => handleCardClick("mijn-klussen"),
      notificationCount: unreadResponseCount,
      icon: <Briefcase className="h-5 w-5 text-white" />,
      gradient: "from-emerald-500 to-emerald-600",
    },
    {
      title: "Chats",
      description: "Bekijk en beheer je gesprekken met vakmannen",
      onClick: () => handleCardClick("chats"),
      notificationCount: unreadMessageCount,
      icon: <MessageSquare className="h-5 w-5 text-white" />,
      gradient: "from-blue-500 to-blue-600",
    },
    {
      title: "Profiel",
      description: "Beheer je persoonlijke gegevens en voorkeuren",
      onClick: () => handleCardClick("profiel"),
      icon: <User className="h-5 w-5 text-white" />,
      gradient: "from-amber-500 to-amber-600",
    },
  ];

  return (
    <div className="min-h-[calc(100vh-85px)] py-12">
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="mb-8 animate-fade-in">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Welkom, {userProfile.first_name || "Klusaanvrager"}
          </h1>
          <p className="text-muted-foreground">Wat wil je vandaag doen?</p>
        </div>

        {/* <DashboardAlert profile={userProfile} /> */}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
          {dashboardItems.map((item, index) => (
            <Card
              key={index}
              className="group cursor-pointer transition-all hover:scale-105 duration-300 overflow-hidden bg-card dark:bg-gray-800/50 border-0 shadow-lg hover:shadow-xl"
              onClick={item.onClick}
            >
              <div className="relative p-6">
                <div
                  className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${item.gradient} rounded-full opacity-10 -mr-16 -mt-16 transition-opacity group-hover:opacity-20`}
                />

                {item.notificationCount > 0 && (
                  <div className="absolute top-4 right-4 z-10">
                    <Badge
                      variant="destructive"
                      className="animate-pulse shadow-md font-medium text-sm px-3 py-1"
                    >
                      Nieuwe berichten
                    </Badge>
                  </div>
                )}

                <div
                  className={`inline-flex p-3 rounded-lg bg-gradient-to-br ${item.gradient} shadow-lg mb-4 group-hover:shadow-xl transition-shadow`}
                >
                  {item.icon}
                </div>

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary transition-colors">
                  {item.title}
                </h3>

                <p className="text-gray-600 dark:text-gray-300 text-sm group-hover:text-gray-900 dark:group-hover:text-white transition-colors">
                  {item.description}
                </p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
