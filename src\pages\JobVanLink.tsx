import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { generateSecurePassword, sendNotificationEmail } from "@/lib/utils";
import { generateSampleJobDescription, getJobTitle } from "@/lib/jobUtils";
import { questionnaire } from "@/components/werkwijze/new-jobs/questionnaire";

const REQUIRED_FIELDS = [
  "firstName",
  "lastName",
  "email",
  "phone",
  "postalCode",
  "houseNumber",
  "message",
  "jobType",
];

function getQueryParams(search: string) {
  const params = new URLSearchParams(search);
  const result: Record<string, string> = {};
  for (const key of REQUIRED_FIELDS) {
    const value = params.get(key);
    if (value) result[key] = value;
  }
  return result;
}

const JobVanLink = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [status, setStatus] = useState<
    "loading" | "success" | "error" | "blocked"
  >("loading");
  const [message, setMessage] = useState<string>("");
  const [redirectCountdown, setRedirectCountdown] = useState<number>(5);

  useEffect(() => {
    async function handleJobFromLink() {
      setStatus("loading");
      setMessage("");
      const params = getQueryParams(location.search);
      const missing = REQUIRED_FIELDS.filter((f) => !params[f]);
      if (missing.length > 0) {
        setStatus("error");
        setMessage(`Ontbrekende velden: ${missing.join(", ")}`);
        return;
      }
      const { data: sessionData } = await supabase.auth.getSession();
      const user = sessionData.session?.user;
      if (!user) {
        setStatus("loading");
        setMessage(
          "Account wordt aangemaakt en klus wordt toegevoegd. Even geduld alstublieft..."
        );
        const password = generateSecurePassword();
        const jobDescription = generateSampleJobDescription(
          params.jobType,
          params.message
        );
        try {
          const { data: authData, error: signUpError } =
            await supabase.auth.signUp({
              email: params.email,
              password,
              options: {
                data: { user_type: "klusaanvrager" },
                emailRedirectTo: `${window.location.origin}/kies-uw-vakman`,
              },
            });
          if (signUpError) {
            if (signUpError.message.includes("exist")) {
              throw new Error(
                "Dit e-mailadres is al in gebruik. Probeer in te loggen of gebruik een ander e-mailadres."
              );
            }
            throw new Error(
              "Er ging iets mis bij het aanmaken van uw account. Probeer het later opnieuw."
            );
          }
          if (!authData.user) {
            throw new Error(
              "Er ging iets mis bij het aanmaken van uw account. Probeer het later opnieuw."
            );
          }
          const userId = authData.user.id;
          const [profileResult, jobResult] = await Promise.all([
            supabase
              .from("profiles")
              .update({
                full_name: `${params.firstName} ${params.lastName}`,
                phone_number: params.phone,
                first_name: params.firstName,
                last_name: params.lastName,
                house_number: params.houseNumber,
                postal_code: params.postalCode,
              })
              .eq("id", userId),
            supabase
              .from("jobs")
              .insert({
                title: getJobTitle(params.jobType),
                description: jobDescription,
                user_id: userId,
                services: questionnaire.find((q) => q.id === params.jobType)
                  ?.services || [params.jobType],
              })
              .select()
              .single(),
          ]);
          if (profileResult.error) {
            throw new Error(
              "Er ging iets mis bij het opslaan van uw profielgegevens. Probeer het later opnieuw."
            );
          }
          if (jobResult.error) {
            throw new Error(
              "Er ging iets mis bij het aanmaken van de klus. Probeer het later opnieuw."
            );
          }
          await sendNotificationEmail({
            to: [params.email],
            subject: "Welkom bij Klusgebied - Uw accountgegevens",
            content: `
              <div style="font-family: sans-serif; color: #333;">
                <h2>Welkom bij Klusgebied</h2>
                <p>Beste ${params.firstName} ${params.lastName},</p>
                <p>Uw account is succesvol aangemaakt. Hieronder vindt u uw inloggegevens:</p>
                <p><strong>Email:</strong> ${params.email}</p>
                <p><strong>Wachtwoord:</strong> ${password}</p>
                <p style="color: #666; font-size: 14px;">Wij raden u aan dit wachtwoord direct te wijzigen na uw eerste inlog.</p>
                <p><a href="${window.location.origin}/auth" style="color: #0066cc;">Klik hier om in te loggen</a></p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                <p style="color: #666; font-size: 12px;">Dit is een automatisch gegenereerd bericht. Bewaar deze email op een veilige plaats.</p>
              </div>
            `,
          });
          await sendNotificationEmail({
            to: ["<EMAIL>"],
            subject: "Nieuwe gebruiker geregistreerd",
            content: `
              <div style="font-family: sans-serif; color: #333;">
                <h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
                <p><strong>Naam:</strong> ${params.firstName} ${params.lastName}</p>
                <p><strong>Email:</strong> ${params.email}</p>
                <p><strong>Type gebruiker:</strong> klusaanvrager</p>
                <p><a href="${window.location.origin}/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
              </div>
            `,
          });
          await sendNotificationEmail({
            to: ["<EMAIL>"],
            subject: "Nieuwe klusopdracht geplaatst",
            content: `
              <div style="font-family: sans-serif; color: #333;">
                <h2>Nieuwe klusopdracht</h2>
                <p><strong>Titel:</strong> ${getJobTitle(params.jobType)}</p>
                <p><strong>Klant:</strong> ${params.firstName} ${
              params.lastName
            }</p>
                <p><strong>Email:</strong> ${params.email}</p>
                <p><a href="${
                  window.location.origin
                }/beheerder/berichten" style="color: #0066cc;">Bekijk in admin panel</a></p>
              </div>
            `,
          });
          setStatus("success");
          setMessage(
            "Uw klus is succesvol aangemaakt! U wordt zo doorgestuurd..."
          );
        } catch (err: any) {
          setStatus("error");
          setMessage(
            err.message ||
              "Er ging iets mis bij het verwerken van uw aanvraag. Probeer het later opnieuw."
          );
        }
        return;
      }
      // Ingelogd: check user type
      // Haal profiel op
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("user_type, full_name, email")
        .eq("id", user.id)
        .single();
      if (profileError) {
        setStatus("error");
        setMessage(
          "Fout bij het ophalen van uw profiel. Probeer het later opnieuw."
        );
        return;
      }
      if (profile.user_type === "vakman") {
        toast({
          variant: "destructive",
          title: "Niet toegestaan",
          description:
            "Alleen een klusaanvrager kan een klus plaatsen via deze link.",
        });
        setStatus("blocked");
        setTimeout(() => navigate("/"), 2500);
        return;
      }
      if (profile.user_type === "klusaanvrager") {
        setStatus("loading");
        setMessage("Uw klus wordt aangemaakt. Even geduld alstublieft...");
        const jobDescription = generateSampleJobDescription(
          params.jobType,
          params.message
        );
        const services = questionnaire.find((q) => q.id === params.jobType)
          ?.services || [params.jobType];
        try {
          const { error: jobError } = await supabase.from("jobs").insert({
            title: getJobTitle(params.jobType),
            description: jobDescription,
            user_id: user.id,
            services,
          });
          if (jobError) {
            throw new Error(
              "Er ging iets mis bij het aanmaken van de klus. Probeer het later opnieuw."
            );
          }
          await sendNotificationEmail({
            to: ["<EMAIL>"],
            subject: "Nieuwe klusopdracht geplaatst",
            content: `
              <div style="font-family: sans-serif; color: #333;">
                <h2>Nieuwe klusopdracht</h2>
                <p><strong>Titel:</strong> ${getJobTitle(params.jobType)}</p>
                <p><strong>Klant:</strong> ${
                  profile.full_name || "Onbekend"
                }</p>
                <p><strong>Email:</strong> ${profile.email || "Onbekend"}</p>
                <p><a href="${
                  window.location.origin
                }/beheerder/berichten" style="color: #0066cc;">Bekijk in admin panel</a></p>
              </div>
            `,
          });
          setStatus("success");
          setMessage(
            "Uw klus is succesvol aangemaakt! U wordt zo doorgestuurd..."
          );
        } catch (err: any) {
          setStatus("error");
          setMessage(
            err.message ||
              "Er ging iets mis bij het verwerken van uw aanvraag. Probeer het later opnieuw."
          );
        }
        return;
      }
      setStatus("error");
      setMessage("Onbekend gebruikersrol. Neem contact op met support.");
    }
    handleJobFromLink();
  }, [location.search, navigate]);

  // Redirect after success
  useEffect(() => {
    if (status === "success" && redirectCountdown > 0) {
      const timer = setTimeout(() => {
        setRedirectCountdown((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [status, redirectCountdown]);

  useEffect(() => {
    if (status === "success" && redirectCountdown === 0) {
      navigate("/banen", { replace: true });
    }
  }, [status, redirectCountdown, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 flex flex-col items-center">
        <div aria-live="polite" className="w-full">
          {status === "loading" && (
            <>
              <div className="w-12 h-12 border-4 border-[#40cfc1] border-t-transparent rounded-full animate-spin mb-4 mx-auto"></div>
              <h2 className="text-xl font-semibold mb-2 text-center">
                Even geduld alstublieft...
              </h2>
              <p className="text-gray-600 text-center">
                {message || "Uw aanvraag wordt verwerkt."}
              </p>
            </>
          )}
          {status === "success" && (
            <>
              <div className="w-12 h-12 bg-[#40cfc1] rounded-full flex items-center justify-center mb-4 mx-auto">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center">
                Klus succesvol aangemaakt!
              </h2>
              <p className="text-gray-600 text-center">
                {message ||
                  "Uw klus is toegevoegd. U ontvangt een bevestiging per e-mail."}
              </p>
              <p className="text-gray-500 text-center mt-2">
                U wordt over {redirectCountdown} seconde
                {redirectCountdown !== 1 ? "n" : ""} doorgestuurd...
              </p>
            </>
          )}
          {status === "error" && (
            <>
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center">
                Er is iets misgegaan
              </h2>
              <p className="text-gray-600 text-center">{message}</p>
              <button
                className="mt-4 px-4 py-2 bg-[#40cfc1] text-white rounded hover:bg-[#35b5a8]"
                onClick={() => window.location.reload()}
              >
                Opnieuw proberen
              </button>
            </>
          )}
          {status === "blocked" && (
            <>
              <div className="w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center mb-4 mx-auto">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M13 16h-1v-4h-1m1-4h.01"
                  />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center">
                Niet toegestaan
              </h2>
              <p className="text-gray-600 text-center">
                Alleen een klusaanvrager kan een klus plaatsen via deze link.
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default JobVanLink;
