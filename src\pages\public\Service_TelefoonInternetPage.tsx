/**
 * @description This component renders a comprehensive and SEO-optimized detail page for telephone and internet installation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for network technicians. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import PricingSection from "@/components/landing/PricingSection";
import {
  ArrowLeft,
  Wifi,
  ShieldCheck,
  ArrowRight,
  Globe,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Server,
  Plug,
} from "lucide-react";

const Service_TelefoonInternetPage = () => {
  usePageTitle(
    "Telefoon & Internet Monteur Nodig? | Klusgebied - Installatie & Storingen"
  );
  const navigate = useNavigate();

  const services = [
    {
      icon: Wifi,
      title: "Internet (Glasvezel/DSL) Installatie",
      description:
        "Een snelle en stabiele internetverbinding, vakkundig geïnstalleerd.",
      points: [
        "Aansluiten van modem en configureren van netwerk.",
        "Direct online met de hoogst mogelijke snelheid.",
        "Advies over de beste provider voor uw adres.",
        "Installatie van glasvezel, DSL en kabel.",
      ],
    },
    {
      icon: Globe,
      title: "WiFi Netwerk Optimalisatie",
      description: "Overal in huis een sterk en betrouwbaar WiFi-signaal.",
      points: [
        "Analyse van uw huidige WiFi-dekking.",
        "Plaatsen van access points of mesh-systemen.",
        "Perfecte dekking in elke kamer, zolder en tuin.",
        "Oplossen van trage verbindingen en storingen.",
      ],
    },
    {
      icon: Plug,
      title: "UTP-kabel (Netwerkkabel) Trekken",
      description:
        "Aanleg van een bekabeld netwerk voor de hoogste snelheid en stabiliteit.",
      points: [
        "Ideaal voor computers, gameconsoles en smart TV's.",
        "Trekken van kabels naar elke gewenste ruimte.",
        "Nette afwerking met wandcontactdozen.",
        "Gegarandeerd de snelste en meest stabiele verbinding.",
      ],
    },
    {
      icon: Server,
      title: "Storingen Oplossen",
      description:
        "Snelle hulp bij problemen met uw internet- of telefoonverbinding.",
      points: [
        "Snelle analyse van het probleem.",
        "Reparatie van kabels en aansluitpunten.",
        "24/7 storingsdienst voor urgente problemen.",
        "Zorgt dat u snel weer online bent.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Wifi className="w-8 h-8 text-white" />,
      title: "Stabiel & Snel Netwerk",
      description:
        "Geniet van een betrouwbare en snelle internetverbinding voor werk en ontspanning.",
    },
    {
      icon: <Globe className="w-8 h-8 text-white" />,
      title: "Overal Perfecte WiFi",
      description:
        "Wij zorgen voor een sterk WiFi-signaal in elke kamer, zolder en zelfs de tuin.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Snelle Storingsdienst",
      description:
        "Onze monteurs zijn snel ter plaatse om storingen op te lossen.",
    },
  ];

  const faqs = [
    {
      question: "Mijn WiFi is traag, wat kan de oorzaak zijn?",
      answer:
        "Dit kan vele oorzaken hebben, zoals een verkeerd geplaatste router, storing van andere apparaten, of een verouderd abonnement. Onze monteur kan een WiFi-meting uitvoeren en het probleem voor u oplossen.",
    },
    {
      question: "Wat is het verschil tussen glasvezel en DSL?",
      answer:
        "Glasvezel biedt over het algemeen veel hogere en stabielere snelheden dan DSL, dat via de traditionele telefoonlijn werkt. Glasvezel is de meest toekomstbestendige keuze.",
    },
    {
      question: "Is een bedrade verbinding beter dan WiFi?",
      answer:
        "Ja, een bedrade (UTP) verbinding is altijd sneller en stabieler dan WiFi. Dit is ideaal voor computers, gameconsoles en smart TV's.",
    },
    {
      question: "Helpen jullie ook met het instellen van de modem en router?",
      answer:
        "Jazeker. Wij zorgen ervoor dat uw modem en router correct zijn aangesloten en geconfigureerd voor optimale prestaties en veiligheid.",
    },
  ];

  const reviews = [
    {
      name: "Alex de Groot",
      location: "Utrecht",
      rating: 5,
      quote:
        "Eindelijk overal in huis perfecte WiFi! De monteur heeft een mesh-systeem geïnstalleerd en het werkt fantastisch.",
      highlighted: true,
    },
    {
      name: "Brenda Koster",
      location: "Almere",
      rating: 5,
      quote:
        "De internetstoring was binnen een uur opgelost. Super snelle en vriendelijke service.",
      highlighted: false,
    },
    {
      name: "Familie Singh",
      location: "Den Haag",
      rating: 5,
      quote:
        "Er zijn netjes kabels getrokken naar de zolder voor de thuiswerkplek. Geen gedoe meer met slechte verbindingen.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1554415707-6e8cfc93fe23?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    { type: "image", url: "https://heyboss.heeyo.ai/1751741609-32ae60e3.webp" },
    { type: "image", url: "https://heyboss.heeyo.ai/1751741609-2cdc561a.webp" },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description: "Beschrijf je internetprobleem of installatiewens.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Monteurs reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde netwerkmonteurs.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start",
      description:
        "Vergelijk profielen en kies de beste monteur voor jouw klus.",
      microcopy: "Vergelijk profielen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Netwerkmonteurs in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "netwerkmonteur",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Telefoon & Internet Monteur Nodig? | Klusgebied - Installatie &
          Storingen
        </title>
        <meta
          name="description"
          content="Vind een betrouwbare monteur voor internet installatie, WiFi optimalisatie en het oplossen van storingen. Plaats je klus gratis op Klusgebied."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6">
                    <Wifi className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      Netwerk Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Internet Monteur?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-sky-500 mt-2">
                      Snel & Stabiel Online
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor een snelle en stabiele verbinding. Wij lossen storingen
                    op en optimaliseren uw (WiFi) netwerk.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Vind jouw netwerkmonteur
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1554415707-6e8cfc93fe23?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Monteur die een netwerkverbinding installeert"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte monteur voor jouw
                netwerkklus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Netwerk Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van het oplossen van een storing tot het aanleggen van een
                compleet nieuw netwerk.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <PricingSection
          serviceName="netwerkmonteur"
          themeColor="blue"
          priceItems={[
            {
              label: "Uurtarief monteur:",
              value: "€50–€75",
              unit: "per uur (incl. BTW)",
            },
            { label: "Storingsanalyse:", value: "Vanaf €75", unit: "" },
            { label: "Voorrijkosten:", value: "Vaak inbegrepen", unit: "" },
          ]}
        />

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een ervaren monteur en geniet van een zorgeloze en
                snelle verbinding.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-blue-600 to-sky-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Problemen met uw internet of WiFi?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Wacht niet langer. Plaats uw klus en vind snel een monteur die uw
              verbindingsproblemen oplost.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je netwerk klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_TelefoonInternetPage;
