import { Helmet } from "react-helmet-async";
import { useLocation } from "react-router-dom";

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  type?: "website" | "article" | "service";
  noindex?: boolean;
  canonical?: string;
  structuredData?: object;
}

const SEOHead = ({
  title = "Klusgebied - Betrouw<PERSON><PERSON> in Jouw Buurt",
  description = "Verbind direct met gekwalifice<PERSON><PERSON> v<PERSON><PERSON>sen in jouw buurt. Plaats je klus en ontvang snel reacties van betrouwbare professionals.",
  keywords = "k<PERSON><PERSON><PERSON>, v<PERSON><PERSON>, klussen, reparatie, onderhoud, Amsterdam, Nederland",
  image = "https://klusgebied.nl/logo.png",
  type = "website",
  noindex = false,
  canonical,
  structuredData,
}: SEOHeadProps) => {
  const location = useLocation();
  const baseUrl = "https://klusgebied.nl";
  const currentUrl = canonical || `${baseUrl}${location.pathname}`;
  
  // Clean up the URL (remove trailing slashes, etc.)
  const cleanUrl = currentUrl.replace(/\/$/, '') || baseUrl;
  
  // Generate structured data for the page
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Klusgebied",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "description": description,
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "NL",
      "addressLocality": "Amsterdam"
    },
    "sameAs": [
      // Add social media URLs when available
    ]
  };

  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="Klusgebied" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={cleanUrl} />
      
      {/* Robots Meta */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      )}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={cleanUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:image:secure_url" content={image} />
      <meta property="og:image:type" content="image/png" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Klusgebied" />
      <meta property="og:locale" content="nl_NL" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@klusgebied" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content="Klusgebied Logo" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#40CFC8" />
      <meta name="msapplication-TileColor" content="#40CFC8" />
      <meta name="application-name" content="Klusgebied" />
      
      {/* Security Headers */}
      <meta httpEquiv="Content-Security-Policy" content="upgrade-insecure-requests" />
      <meta httpEquiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains; preload" />
      <meta name="referrer" content="strict-origin-when-cross-origin" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>
      
      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://bbyifnpcqefabwexvxdc.supabase.co" />
      <link rel="preconnect" href="https://api.mollie.com" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    </Helmet>
  );
};

export default SEOHead;
