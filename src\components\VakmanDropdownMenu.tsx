import { Menu, LayoutDashboard } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { MenuItems } from "./menu/MenuItems";
import { useSessionHandler } from "@/hooks/useSessionHandler";
import { ROUTE_PATHS } from "@/config/routes";

interface VakmanDropdownMenuProps {
  onLogout: () => void;
}

export const VakmanDropdownMenu = ({ onLogout }: VakmanDropdownMenuProps) => {
  const navigate = useNavigate();
  const [isAdmin, setIsAdmin] = useState(false);
  const { handleLogout: handleSessionLogout } = useSessionHandler();

  useEffect(() => {
    const checkUserRole = async () => {
      try {
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) {
          console.error("Session error:", sessionError);
          return;
        }

        if (!session?.user?.id) {
          return;
        }

        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("user_type")
          .eq("id", session.user.id)
          .single();

        if (profileError) {
          console.error("Profile error:", profileError);
          return;
        }

        setIsAdmin(profile?.user_type === "admin");
      } catch (error) {
        console.error("Error checking user role:", error);
      }
    };

    checkUserRole();
  }, []);

  const handleLogoutClick = async () => {
    await handleSessionLogout();
    onLogout();
  };

  if (isAdmin) {
    return (
      <div className="flex items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              aria-label="Open navigation menu"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-56 bg-card border shadow-lg"
          >
            <MenuItems onLogout={handleLogoutClick} navigate={navigate} />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {
          navigate(ROUTE_PATHS.HOME);
        }}
        className="hidden md:flex items-center gap-2 text-foreground mr-2"
      >
        <LayoutDashboard className="h-4 w-4" />
        Dashboard
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9"
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-56 bg-card border shadow-lg"
        >
          <MenuItems onLogout={handleLogoutClick} navigate={navigate} />
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
