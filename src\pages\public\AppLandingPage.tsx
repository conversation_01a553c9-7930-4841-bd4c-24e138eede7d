/**
 * @description This component renders the landing page specifically for the Klusgebied mobile application with comprehensive feature showcases and download prompts.
 * It highlights the convenience of mobile service booking, real-time chat, payment processing, and job tracking through an intuitive app interface.
 * The component includes animated phone mockups, feature cards, testimonials, and multiple download call-to-actions optimized for conversion and user engagement.
 * Key variables include app features array, testimonials data, download buttons, and mobile-first design elements for showcasing the app's capabilities.
 */

import React, { useState, useEffect } from "react";
import {
  ArrowLeft,
  Smartphone,
  Clock,
  MessageSquare,
  CreditCard,
  Star,
  CheckCircle,
  Download,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";

const AppLandingPage = () => {
  const navigate = useNavigate();
  const [activeFeature, setActiveFeature] = useState(0);
  const [hoveredFeature, setHoveredFeature] = useState(null);

  usePageTitle("Klusgebied App | Vakmannen in je broekzak");

  // App features with detailed descriptions
  const appFeatures = [
    {
      icon: Clock,
      title: "Directe Reactie",
      description:
        "Ontvang binnen minuten reacties van beschikbare vakmannen in jouw buurt",
      detailedDescription:
        "Met push notificaties weet je direct wanneer een vakman reageert op je klus. Geen wachten meer op telefoontjes of emails - alles gebeurt in real-time via de app.",
    },
    {
      icon: MessageSquare,
      title: "Chat & Bel Direct",
      description: "Communiceer rechtstreeks met vakmannen via chat of bellen",
      detailedDescription:
        "Stel vragen, deel foto's van de klus en plan afspraken - alles vanuit één handige chatinterface. Geen tussenpersonen, directe communicatie voor de beste resultaten.",
    },
    {
      icon: CreditCard,
      title: "Veilig Betalen",
      description:
        "Betaal eenvoudig en veilig via de app na voltooiing van de klus",
      detailedDescription:
        "Geïntegreerde betalingsverwerking met verschillende opties zoals iDEAL, creditcard en Apple Pay. Je betaalt pas als je tevreden bent met het geleverde werk.",
    },
    {
      icon: Star,
      title: "Reviews & Rating",
      description: "Bekijk beoordelingen en laat zelf reviews achter",
      detailedDescription:
        "Maak weloverwogen keuzes op basis van echte ervaringen van andere klanten. Help anderen door jouw ervaring te delen en bouw zo mee aan een betrouwbare community.",
    },
  ];

  // App testimonials
  const appTestimonials = [
    {
      name: "Sarah van der Berg",
      location: "Amsterdam",
      rating: 5,
      text: "Super handig! Binnen 10 minuten had ik 3 loodgieters die konden helpen. De chat functie is echt geweldig.",
      image:
        "https://images.unsplash.com/photo-1729824186568-be656d0eecf9?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxoYXBweSUyMGN1c3RvbWVyJTJDJTIwdGVzdGltb25pYWwlMkMlMjBzYXRpc2ZpZWQlMjB1c2VyfGVufDB8fHx8MTc1MTUwMzc2NXww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      name: "Mike Janssen",
      location: "Rotterdam",
      rating: 5,
      text: "Eindelijk een app die werkt zoals het hoort. Betaling via de app is super veilig en makkelijk.",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Lisa de Wit",
      location: "Utrecht",
      rating: 5,
      text: "De reviews en ratings maken het makkelijk om de juiste vakman te kiezen. Echt een aanrader!",
      image:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.1.0&w=400&h=400",
    },
  ];

  useEffect(() => {
    if (hoveredFeature !== null) return; // Pause animation on hover
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % appFeatures.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [hoveredFeature]);

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 motion-preset-slide-up">
              Klusgebied App
              <span className="block text-3xl md:text-4xl text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-500 mt-2">
                Vakmannen in je broekzak
              </span>
            </h1>

            <p className="max-w-xl mx-auto lg:mx-0 text-lg md:text-xl text-slate-600 mt-6">
              Alle vakmannen in je broekzak. Plan, beheer en betaal je klussen
              moeiteloos en direct vanaf je telefoon met de nieuwe Klusgebied
              App. Krijg toegang tot duizenden professionals, chat in real-time
              en volg de voortgang van je project, waar je ook bent.
            </p>

            <div className="flex flex-col sm:flex-row items-center gap-4 justify-center lg:justify-start motion-preset-slide-up motion-delay-200 mt-10">
              <a
                href="https://apps.apple.com/nl/app/klusgebied/id6747739000"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block hover:-translate-y-1 transition-transform duration-300"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/3/3c/Download_on_the_App_Store_Badge.svg"
                  alt="Download on the App Store"
                  className="h-14 w-auto"
                  loading="lazy"
                  width={144}
                  height={56}
                />
              </a>
              <a
                href="https://play.google.com/store/apps/details?id=nl.klusgebied.android"
                className="inline-block hover:-translate-y-1 transition-transform duration-300"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                  alt="Get it on Google Play"
                  className="h-14 w-auto"
                  loading="lazy"
                  width={144}
                  height={56}
                />
              </a>
            </div>

            <div className="flex items-center justify-center lg:justify-start space-x-6 text-sm text-slate-600 mt-8">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>Gratis download</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>iOS & Android</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>50.000+ downloads</span>
              </div>
            </div>
          </div>

          <div className="relative flex justify-center lg:justify-end motion-preset-slide-up motion-delay-300">
            <div className="relative">
              <img
                src="https://heyboss.heeyo.ai/user-assets/Ontwerp zonder titel (2)_ps3zDgB6.png"
                alt="Klusgebied App"
                className="w-auto h-[467px] object-contain transition-transform duration-500 scale-[1.30] hover:scale-[1.35] drop-shadow-2xl"
                fetchPriority="high"
                width="300"
                height="467"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <section className="py-16 lg:py-20 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              App in cijfers
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Duizenden gebruikers hebben de Klusgebied app al ontdekt. Ervaar
              zelf waarom onze app de nummer 1 keuze is voor het vinden van
              vakmannen.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="text-3xl md:text-4xl font-bold text-teal-500 mb-2">
                50K+
              </div>
              <div className="text-slate-600">App Downloads</div>
            </div>
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="text-3xl md:text-4xl font-bold text-teal-500 mb-2">
                4.8★
              </div>
              <div className="text-slate-600">App Store Rating</div>
            </div>
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="text-3xl md:text-4xl font-bold text-teal-500 mb-2">
                2.5K+
              </div>
              <div className="text-slate-600">Actieve Vakmannen</div>
            </div>
            <div className="text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="text-3xl md:text-4xl font-bold text-teal-500 mb-2">
                &lt; 2 min
              </div>
              <div className="text-slate-600">Gem. Reactietijd</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
              Alles wat je nodig hebt, in één app.
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
              De Klusgebied app is ontworpen om het vinden en beheren van
              vakmannen makkelijker, sneller en transparanter dan ooit te maken.
              Elk aspect van de app is gebouwd met uw gemak en zekerheid in
              gedachten.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div
              className="space-y-8"
              onMouseLeave={() => setHoveredFeature(null)}
            >
              {appFeatures.map((feature, index) => {
                const Icon = feature.icon;
                const isHovered = hoveredFeature === index;
                const isAnotherHovered =
                  hoveredFeature !== null && hoveredFeature !== index;
                const isActive =
                  activeFeature === index && hoveredFeature === null;

                return (
                  <div
                    key={index}
                    onMouseEnter={() => setHoveredFeature(index)}
                    className={`relative p-6 rounded-2xl transition-all duration-300 ease-out cursor-pointer overflow-hidden ${
                      isActive || isHovered
                        ? "bg-slate-50 shadow-2xl scale-105 z-10"
                        : isAnotherHovered
                        ? "bg-slate-50/50 opacity-70 scale-95"
                        : "bg-slate-50/50 hover:bg-slate-50 hover:shadow-lg"
                    }`}
                    onClick={() => setActiveFeature(index)}
                  >
                    {(isActive || isHovered) && (
                      <div className="absolute inset-0 bg-gradient-to-r from-teal-400 to-blue-500 opacity-10"></div>
                    )}
                    <div
                      className={`absolute top-0 left-0 h-full w-1.5 bg-gradient-to-b from-teal-400 to-blue-500 transition-transform duration-300 ease-in-out rounded-full ${
                        isActive || isHovered ? "scale-y-100" : "scale-y-0"
                      }`}
                      style={{ transformOrigin: "top" }}
                    ></div>
                    <div className="relative flex items-start space-x-4">
                      <div
                        className={`p-3 rounded-xl transition-all duration-300 ${
                          isActive || isHovered
                            ? "bg-gradient-to-r from-teal-500 to-blue-600 text-white shadow-lg"
                            : "bg-teal-100 text-teal-600"
                        }`}
                      >
                        <Icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg md:text-xl font-bold text-slate-800 mb-2">
                          {feature.title}
                        </h3>
                        <p className="text-slate-600 mb-3">
                          {feature.description}
                        </p>
                        {(isActive || isHovered) && (
                          <p className="text-sm text-slate-500 leading-relaxed motion-preset-fade-down">
                            {feature.detailedDescription}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="relative flex justify-center">
              <div className="relative">
                <img
                  src="https://heyboss.heeyo.ai/user-assets/Ontwerp zonder titel (2)_ps3zDgB6.png"
                  alt="Klusgebied App Features"
                  className="w-auto h-[467px] object-contain transition-transform duration-500 scale-[1.30] hover:scale-[1.35] drop-shadow-2xl"
                  loading="lazy"
                  width="300"
                  height="467"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 lg:py-20 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Wat gebruikers zeggen
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Lees wat andere gebruikers zeggen over hun ervaring met de
              Klusgebied app. Hun success stories spreken voor zich.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {appTestimonials.map((testimonial, index) => (
              <div
                key={index}
                className={`bg-white rounded-2xl p-8 shadow-lg motion-preset-slide-up motion-delay-${
                  index * 100
                }`}
              >
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover mr-4"
                    loading="lazy"
                    width="48"
                    height="48"
                  />
                  <div>
                    <div className="font-semibold text-slate-800">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-slate-600">
                      {testimonial.location}
                    </div>
                  </div>
                </div>

                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-5 h-5 text-yellow-400 fill-current"
                    />
                  ))}
                </div>

                <p className="text-slate-600 leading-relaxed">
                  {testimonial.text}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-16 lg:py-20 bg-gradient-to-r from-teal-500 to-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 motion-preset-fade-down">
            Klaar voor de toekomst van klussen?
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-base md:text-lg text-white/90 motion-preset-fade-down motion-delay-200">
            Download de app en start je volgende project vandaag nog. De beste
            vakmannen in jouw regio wachten op je. Ervaar de toekomst van
            klussen: eenvoudig, snel en betrouwbaar.
          </p>

          <div className="mt-8 flex flex-col sm:flex-row items-center gap-4 justify-center motion-preset-slide-up motion-delay-300">
            <a
              href="https://apps.apple.com/nl/app/klusgebied/id6747739000"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block hover:-translate-y-1 transition-transform duration-300"
            >
              <img
                src="/download-on-the-app-store.svg"
                alt="Download on the App Store"
                className="h-14 w-auto"
                loading="lazy"
                width={144}
                height={56}
              />
            </a>
            <a
              href="https://play.google.com/store/apps/details?id=nl.klusgebied.android"
              className="inline-block hover:-translate-y-1 transition-transform duration-300"
            >
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                alt="Get it on Google Play"
                className="h-14 w-auto"
                loading="lazy"
                width={144}
                height={56}
              />
            </a>
          </div>

          <div className="mt-8 text-white/80 text-sm">
            ✨ Gratis download • 🔒 100% veilig • ⚡ Direct beschikbaar
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default AppLandingPage;
