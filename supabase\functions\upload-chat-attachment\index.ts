import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const formData = await req.formData();
    const file = formData.get("file");
    const jobId = formData.get("jobId");
    const senderId = formData.get("senderId");
    const receiverId = formData.get("receiverId");
    const chatId = formData.get("chat_id");

    if (!file || !jobId || !senderId || !receiverId) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    const supabase = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    const fileExt = file.name.split(".").pop();
    const fileName = `${crypto.randomUUID()}.${fileExt}`;
    const filePath = `${jobId}/${fileName}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from("chat_attachments")
      .upload(filePath, file, {
        contentType: file.type,
        upsert: false,
      });

    if (uploadError) {
      console.error("Upload error:", uploadError);
      throw uploadError;
    }

    const { data: urlData } = await supabase.storage
      .from("chat_attachments")
      .getPublicUrl(filePath);

    const fileUrl = urlData.publicUrl;

    // Create a message for the file
    const { error: messageError } = await supabase.from("messages").insert({
      job_id: jobId,
      sender_id: senderId,
      receiver_id: receiverId,
      content: `[Bestand gedeeld] ${file.name}`,
      attachment_url: fileUrl,
      attachment_type: file.type.startsWith("image/") ? "image" : "file",
      chat_id: chatId,
    });

    if (messageError) {
      console.error("Message error:", messageError);
      throw messageError;
    }

    return new Response(
      JSON.stringify({
        message: "File uploaded successfully",
        fileUrl,
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Server error:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
