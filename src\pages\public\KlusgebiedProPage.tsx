/**
 * @description Package detail page for Klusgebied Pro (€299 per month) subscription plan.
 * Provides comprehensive information about features, benefits, and pricing for craftsmen
 * who want regional growth and professional profiling.
 */
import { Helmet } from "react-helmet-async";
import {
  CheckCircle,
  ArrowRight,
  Star,
  MapPin,
  MessageSquare,
  Mail,
  Phone,
  Video,
  TrendingUp,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

import Footer from "@/components/landing/Footer";
import usePageTitle from "@/hooks/usePageTitle";

const KlusgebiedProPage = () => {
  usePageTitle("Klusgebied Pro - €299 per maand | Professioneel Vakman Pakket");
  const navigate = useNavigate();

  const features = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Topvermelding in 3 regio's",
      description: "Meer zichtbaarheid in omliggende gebieden en steden.",
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "Onbeperkt reageren op klussen",
      description:
        "Geen limiet op het aantal opdrachten waarop je kunt reageren.",
    },
    {
      icon: <Video className="w-6 h-6" />,
      title: "1-op-1 groeigesprek per kwartaal (30 minuten)",
      description:
        "Persoonlijk advies over strategie, communicatie en conversie.",
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: "Professionele profielvideo (1 minuut)",
      description:
        "Laat jouw vakmanschap en persoonlijkheid zien in een korte bedrijfsvideo.",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Maandelijkse social media advertenties",
      description:
        "€50 adbudget inbegrepen - Gericht adverteren op jouw vakgebied en regio via Facebook en Instagram.",
    },
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Vermelding in de nieuwsbrief (3x per jaar)",
      description: "Promotie richting klusplaatsers met hoge betrokkenheid.",
    },
  ];

  const includedFromPlus = [
    "Professioneel ingericht profiel",
    "Klusgebied Pro badge",
    "Toegang tot de Klusgebied Community",
    "1 social media shout-out per maand",
  ];

  const handleGetStarted = () => {
    navigate("/contact");
  };

  const footerProps = {
    logoSrc:
      "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png",
    brandName: "Klusgebied",
    description:
      "Vind snel en eenvoudig een geverifieerde vakman in jouw regio voor elke klus.",
    contact: {
      address: "Slotermeerlaan 58, 1064 HC Amsterdam",
      phone: "+31 123 456 789",
      email: "<EMAIL>",
    },
    links: {
      "Voor Klanten": [
        { label: "Vind een Vakman", href: "/diensten" },
        { label: "Klantenservice", href: "/klantenservice" },
        { label: "Garantie", href: "/garantie" },
        { label: "Veelgestelde Vragen", href: "/faq" },
      ],
      "Voor Vakmensen": [
        { label: "Aanmelden als Vakman", href: "/vakman" },
        { label: "Voordelen", href: "/vakman" },
        { label: "Investeer in Klusgebied+", href: "/investeren" },
        { label: "Support", href: "/support" },
      ],
    },
    socials: [
      { name: "Facebook", href: "#" },
      { name: "Twitter", href: "https://x.com/heybossAI" },
      { name: "Instagram", href: "#" },
      {
        name: "LinkedIn",
        href: "https://www.linkedin.com/company/heyboss-xyz/",
      },
    ],
    legal: {
      privacyPolicyUrl: "https://legal.heyboss.tech/67845a5e6e6bf5ecd4a3ae47/",
      termsAndConditionsUrl:
        "https://legal.heyboss.tech/67845cfe76f9675292514b80/",
      year: 2025,
    },
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <Helmet>
        <title>
          Klusgebied Pro - €299 per maand | Professioneel Vakman Pakket
        </title>
        <meta
          name="description"
          content="Klusgebied Pro voor €299 per maand. Voor vakmannen die regionaal willen groeien en zich professioneel willen profileren. Topvermelding in 3 regio's, onbeperkt reageren en meer."
        />
        <meta
          property="og:title"
          content="Klusgebied Pro - €299 per maand | Professioneel Vakman Pakket"
        />
        <meta
          property="og:description"
          content="Klusgebied Pro voor €299 per maand. Voor vakmannen die regionaal willen groeien en zich professioneel willen profileren."
        />
        <meta
          property="og:image"
          content="https://heyboss.heeyo.ai/user-assets/7265ee1d-7ceb-41ac-a6af-bc9499143883_3xnKAaGW.png"
        />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-blue-100 py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="flex justify-center mb-6">
                <span className="bg-teal-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  Meest Gekozen
                </span>
              </div>
              <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-500 rounded-full mb-8">
                <TrendingUp className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-gray-900 mb-6 tracking-wide leading-tight">
                Klusgebied Pro
              </h1>
              <div className="text-4xl font-bold text-blue-600 mb-4">
                €299 per maand{" "}
                <span className="text-lg text-gray-600 font-normal">
                  (excl. btw)
                </span>
              </div>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed tracking-wide">
                Voor vakmannen die regionaal willen groeien en zich
                professioneel willen profileren.
              </p>
              <button
                onClick={handleGetStarted}
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold px-8 py-4 rounded-lg text-lg transition-all duration-200 flex items-center justify-center mx-auto group"
              >
                Start Nu
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </section>

        {/* Included from Plus */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Alles van Klusgebied+, plus:
              </h2>
              <div className="grid md:grid-cols-2 gap-4 mb-12">
                {includedFromPlus.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-teal-500 mr-3 flex-shrink-0" />
                    <span className="text-base text-gray-700 tracking-wide leading-relaxed">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Pro Features Section */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-16 tracking-wide leading-relaxed">
                Extra Pro Voordelen
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-shadow"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6">
                      <div className="text-blue-600">{feature.icon}</div>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3 tracking-wide leading-relaxed">
                      {feature.title}
                    </h3>
                    <p className="text-base text-gray-600 leading-relaxed tracking-wide">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Support Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Prioriteitssupport
              </h2>
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mx-auto mb-6">
                  <Phone className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 tracking-wide">
                  Snellere ondersteuning
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed tracking-wide">
                  Als Pro-klant krijg je voorrang bij onze support en snellere
                  reactietijden op al je vragen.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contract Details */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Contract Details
              </h2>
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">
                      Looptijd
                    </h3>
                    <p className="text-base text-gray-700 leading-relaxed tracking-wide">
                      Jaarcontract (12 maanden)
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">
                      Bonus
                    </h3>
                    <p className="text-base text-gray-700 leading-relaxed tracking-wide">
                      Eerste 3 maanden voor €199 bij inschrijving vóór [datum].
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-blue-500 to-blue-600">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl font-bold text-white mb-6 tracking-wide leading-tight">
                Klaar voor professionele groei?
              </h2>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed tracking-wide">
                Vergroot je bereik, verhoog je conversie en bouw een sterker
                merk met Klusgebied Pro.
              </p>
              <button
                onClick={handleGetStarted}
                className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg text-lg transition-all duration-200 flex items-center justify-center mx-auto group"
              >
                Neem Contact Op
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </section>
      </main>

      <Footer {...footerProps} />
    </div>
  );
};

export default KlusgebiedProPage;
