import { Suspense, lazy, useState } from "react";
import { Loader2, Calendar, Euro } from "lucide-react";
import { format, parseISO } from "date-fns";
import { nl } from "date-fns/locale";

import { PortfolioProject } from "./types";

// Lazy load the dialog components
const Dialog = lazy(() =>
  import("@/components/ui/dialog").then((mod) => ({ default: mod.Dialog }))
);
const DialogContent = lazy(() =>
  import("@/components/ui/dialog").then((mod) => ({
    default: mod.DialogContent,
  }))
);
const DialogHeader = lazy(() =>
  import("@/components/ui/dialog").then((mod) => ({
    default: mod.DialogHeader,
  }))
);
const DialogTitle = lazy(() =>
  import("@/components/ui/dialog").then((mod) => ({ default: mod.DialogTitle }))
);

interface PortfolioDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  vakmanName: string;
  portfolioProjects: PortfolioProject[];
}

export const PortfolioDialog = ({
  isOpen,
  onOpenChange,
  vakmanName,
  portfolioProjects,
}: PortfolioDialogProps) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "MMMM yyyy", { locale: nl });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Datum onbekend";
    }
  };

  return (
    <Suspense
      fallback={
        <div className="fixed inset-0 flex items-center justify-center bg-black/50">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      }
    >
      {/* Image Preview Dialog */}
      {selectedImage && (
        <Dialog
          open={!!selectedImage}
          onOpenChange={() => setSelectedImage(null)}
        >
          <DialogContent className="max-w-4xl p-0 overflow-hidden">
            <img
              src={selectedImage}
              alt="Project foto"
              className="w-full h-full object-contain"
              onClick={() => setSelectedImage(null)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Main Portfolio Dialog */}
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-display">
              Portfolio van {vakmanName}
            </DialogTitle>
            <p className="text-muted-foreground mt-2">
              Bekijk de voltooide projecten en ervaring van deze vakman
            </p>
          </DialogHeader>

          {portfolioProjects.length === 0 ? (
            <div className="text-center py-12 bg-muted/10 rounded-lg border border-dashed">
              <p className="text-muted-foreground">
                Deze vakman heeft nog geen portfolio projecten toegevoegd.
              </p>
            </div>
          ) : (
            <div className="space-y-8 py-4">
              {portfolioProjects.map((project) => (
                <div
                  key={project.id}
                  className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">
                          {project.title}
                        </h3>
                        <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {formatDate(project.created_at)}
                          </div>
                          {project.budget && (
                            <div
                              className="flex items-center gap-1"
                              title="Projectwaarde"
                            >
                              <Euro className="h-4 w-4" />
                              <span>
                                Projectwaarde:{" "}
                                {project.budget.toLocaleString("nl-NL", {
                                  style: "currency",
                                  currency: "EUR",
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                })}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {project.description && (
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {project.description}
                      </p>
                    )}

                    {project.photos && project.photos.length > 0 && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {project.photos.map((photo, index) => (
                          <div
                            key={index}
                            className="relative group aspect-video cursor-pointer overflow-hidden rounded-lg"
                            onClick={() => setSelectedImage(photo.photo_url)}
                          >
                            <img
                              src={photo.photo_url}
                              alt={`Project foto ${index + 1}`}
                              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                              loading="lazy"
                            />
                            <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                              <span className="text-white text-sm font-medium">
                                Bekijk foto
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Suspense>
  );
};
